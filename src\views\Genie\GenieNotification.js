import React, { useEffect, useState } from "react";
import { OpenInNewRounded } from "@mui/icons-material";
import { localStorageCache } from "../../utils/utility";
import { markReadNotification, openTicketUrl } from "../../services/GenieService";
import { SortNotificationData } from "../../layouts/SV/components/Sidebar/components/Notifications";
import { updateStateInReduxGenie } from "../../store/actions";
import { useDispatch } from "react-redux";

const GenieNotification = () => {

	const dispatch = useDispatch();
	const [MongonotificationData, setMongonotificationData] = useState([]);

	useEffect(() => {
        let MongoNData = localStorageCache.readFromCache('MongoNotificationData') != null ? JSON.parse(localStorageCache.readFromCache('MongoNotificationData')).filter(item => item.type.includes("genie")) : [];
        if (Array.isArray(MongoNData) && MongoNData.length > 0) {
            try {
                const sortednotificationData = SortNotificationData(MongoNData);
                setMongonotificationData(sortednotificationData ? sortednotificationData : []);
            }
            catch (err) {
                setMongonotificationData(MongoNData);
            }
        }
        else {
            setMongonotificationData([]);
        }
    }, [])

	const redirectNotificationURL = function (data) {
        let MongoNotificationData = localStorageCache.readFromCache('MongoNotificationData') != null ? JSON.parse(localStorageCache.readFromCache('MongoNotificationData')) : [];
        if (MongoNotificationData && Array.isArray(MongoNotificationData)) {
            MongoNotificationData.forEach((item) => {
                if (item.id == data.id) {
                    item.IsRead = true;
                }
            })
            localStorageCache.writeToCache("MongoNotificationData", JSON.stringify(MongoNotificationData), 5 * 60 * 60 * 1000);
        }

        if (data.type.includes("bms") && data.event && data.event.split("_")[0] && data.event.split("_")[1]) 
		{
            openTicketUrl(data.event.split("_")[0], data.event.split("_")[1]);
        }
		else if (data.link) 
		{
			window.open(data.link);
		}

		data.IsRead = true;
        markReadNotification(data.id);
		dispatch(updateStateInReduxGenie({ refreshMyTckt: true }));
    }

	const BindNotification = ({ data, isRead }) => {
        return (
			<li key={data.id}
				className={isRead ? "read-text" : ""}
				onClick={() => { redirectNotificationURL(data) }}
			>
				<span>
					{data.text}
					{data.event && 
						<small>Ticket ID: {data.event.split("_")[0]}</small>
					}
				</span>
				{(
					(
						data.type.includes("bms") 
						&& data.event
						&& data.event.split("_")[0] 
						&& data.event.split("_")[1]
		 			) 
					|| data.link
				) && 
					<span className="text-right">
						<OpenInNewRounded color="secondary" />
					</span>
				}
			</li>
		)
    }

	return (
		<>
			<div className="notification-msgs-list">
				<ul>
					{MongonotificationData.length > 0 ?
						MongonotificationData.map((data) => (
							<BindNotification
								key={data.id}
								data={data}
								isRead={data.IsRead}
							/>
						))
						:
						<li style={{ textAlign: "center" }}>
							No New Notification
			 			</li>
					}
				</ul>
			</div>
        </>
	)
};

export default GenieNotification;
