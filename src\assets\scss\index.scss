@import "../scss/variables/variables";
// @import "../scss//common/table";
// @import "../scss/common/coloredCards";
// @import "../scss/common/dataCards";
// @import "../scss/common/tabs";
// @import "../scss/common/searchInput";
// @import "../scss/common/expansionPanel";
// @import "../scss/common/drawer";
// @import "../scss/common/modal";
// @import "../scss/components/topBar";
// @import "../scss/components/dashboard";
// @import "../scss/components/booking";
// @import "../scss/components/customerHistory";
@import "../scss/components/salesView";
@import "../scss/components/transferTypesModal";
@import "../scss/components/MaturityOverCustomerPopup.scss";
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,300;1,500&display=swap');
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap");
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  height: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.text-left {
  text-align: left;
}
body {
  background-color: #fff !important;
  height: 100%;
  font-family: "Roboto", sans-serif;
  -ms-overflow-style: none;
  // padding-top: 49px;
  &::-webkit-scrollbar {
    display: none;
  }
}
.tool-bar {
  box-shadow: 0px 3px 6px #00000029;
}
.text-center {
  text-align: center;
}
.pading-zero {
  padding: 0;
}
.card {
  background: #ffffff;
  box-shadow: 0px 0px 26px #0000001a;
  border-radius: 20px;
}
a {
  text-decoration: none;
}

#root {
  height: 100%;
}

.title {
  position: relative;
  margin: 12px 0;
  display: flex;
  align-items: baseline;
  padding: 0 16px !important;
  & > div > span {
    font-size: 16px;
    color: $primary-color;
    position: relative;
    font-weight: 700;
  }
  @media (min-width: 600px) {
    flex-direction: row;
    height: auto;
  }
  .firstbox{
    width:100%;
  }
}

.inner-mobile {
  display: flex;
  flex-wrap: wrap;
  @media (min-width: 960px) {
    display: none;
  }
  .firstbox{
    width:100%;
  }
}

.inner-desktop {
  min-width: 800px;
  width: 100%;
  border-radius: 5px 5px 0 0;
  display: none;
  @media (min-width: 960px) {
    display: block;
  }
}
.logo-container {
  justify-content: center !important;
  margin-bottom: 25px;
}

// .MuiIconButton-root {
//   color: #495973 !important;
//   background-color: red;
// }
.wrapper {
  padding: 10px 40px;
  background-color: #FAFAFA;
}

@media all and (max-width: 768px) {
  .wrapper,
  .bg-color {
    padding: 10px 16px;
    .proceedBtn {
      font-size: 11px;
    }
  }
}
