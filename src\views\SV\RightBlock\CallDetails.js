import React, { useEffect, useState } from "react";
import { Grid, Tooltip } from "@mui/material";
import makeStyles from '@mui/styles/makeStyles';
import { CALL_API } from "../../../services";
import { useSelector, useDispatch } from "react-redux";
import rootScopeService from "../../../services/rootScopeService";
import CallAttemptsPopUp from "./Modals/CallAttemptsPopUp";
import ConnectedCalls from "./Modals/ConnectedCalls";
// import { ExpandMore } from "@mui/icons-material";
import {  updateStateInRedux } from '../../../store/actions/SalesView/SalesView';

const useStyles = makeStyles((theme) => ({
    customSize: {
        padding: 0,
        maxWidth: 800,
        maxHeight: '400px',
        overflow: 'auto',
        '& .MuiTableContainer-root': {
            maxHeight: 350,
            maxWidth: 750,
            overflow: 'auto',
        },
        '& p': {
            padding: '8px 16px',
            fontSize: '15px',
        }
    },

}));

export default function CallDetails(props) {
  const dispatch = useDispatch();
  const classes = useStyles();
    // let [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);
    let [parentLeadId] = useSelector(({ salesview }) => [salesview.parentLeadId]);
    // const [show, setShow] = useState(true);
    let CallHistoryData = {
        TotalCallDetails: [],
        CallHistory: {
            TotalCalls: "0",
            ConnectedCalls: "0",
            CallDuration: "0",
            InboundCallDuration: "0",
            C2CCallDuration: "0"
        },
        ConnectedCallDetails: [],
        InboundCallDetails: [],
        C2CCallDetails: []
    }
    const [CallDetails, setCallDetails] = useState(CallHistoryData);

    const getCustomerCallSummary = () => {
        const input = {
            url: "coremrs/api/MRSCore/GetCallSummary?LeadId=" + parentLeadId + "&ProductId="+rootScopeService.getProductId(),
            method: 'GET',
            service: 'MatrixCoreAPI'
        }

        CALL_API(input).then((response) => {

            if (response && Array.isArray(response)) {
                    if (response.length > 0) {
                        if (response[0].TotalCalls !== undefined)
                            CallHistoryData.CallHistory.TotalCalls = response[0].TotalCalls;
                        if (response[0].ConnectedCalls !== undefined)
                            CallHistoryData.CallHistory.ConnectedCalls = response[0].ConnectedCalls;
                        if (response[0].TotalCallDuration !== undefined){
                            CallHistoryData.CallHistory.CallDuration = response[0].TotalCallDuration;
                            dispatch(updateStateInRedux({ key: 'TotalCallDuration', value: response[0].TotalCallDuration }));
                        }
                    }
                    if (response.length > 0) {
                        response.forEach(function (val, key) {
                            CallHistoryData.TotalCallDetails.push({
                                CommunicationDate: { key: "Date", value: val["CommunicationDate"] },
                                ActionBy: { key: "Action By", value: val["AgentName"] + (val["AgentId"] == "" ? "" : ' (') + val["AgentId"] + (val["AgentId"] == "" ? "" : ')') },
                                CommunicationType: { key: "CommunicationType", value: val["CommTypeValue"] },
                                Duration: { key: "CallDuration", value: ConvertSecondsToMinute(val["CallDuration"]) }

                            });
                            if (val["CallDuration"] > 0) {
                                CallHistoryData.ConnectedCallDetails.push({
                                    CommunicationDate: { key: "Date", value: val["CommunicationDate"] },
                                    ActionBy: { key: "Action By", value: val["AgentName"] + (val["AgentId"] == "" ? "" : ' (') + val["AgentId"] + (val["AgentId"] == "" ? "" : ')') },
                                    CommunicationType: { key: "CommunicationType", value: val["CommTypeValue"] },
                                    Duration: { key: "CallDuration", value: ConvertSecondsToMinute(val["CallDuration"]) }

                                });
                            }
                            if (['Inbound', 'inbound', 'IB'].indexOf(val["CommTypeValue"]) != -1) {
                                CallHistoryData.InboundCallDetails.push({
                                    CommunicationDate: { key: "Date", value: val["CommunicationDate"] },
                                    ActionBy: { key: "Action By", value: val["AgentName"] + (val["AgentId"] == "" ? "" : ' (') + val["AgentId"] + (val["AgentId"] == "" ? "" : ')') },
                                    CommunicationType: { key: "CommunicationType", value: val["CommTypeValue"] },
                                    Duration: { key: "CallDuration", value: ConvertSecondsToMinute(val["CallDuration"]) }
                                });
                                if (val["CallDuration"] !== undefined) {
                                    CallHistoryData.CallHistory.InboundCallDuration = parseInt(CallHistoryData.CallHistory.InboundCallDuration) + parseInt(val["CallDuration"]);
                                }
                            }

                            if (['C2C'].indexOf(val["CommTypeValue"]) !== -1) {
                                CallHistoryData.C2CCallDetails.push({
                                    CommunicationDate: { key: "Date", value: val["CommunicationDate"] },
                                    ActionBy: { key: "Action By", value: val["AgentName"] + (val["AgentId"] == "" ? "" : ' (') + val["AgentId"] + (val["AgentId"] == "" ? "" : ')') },
                                    CommunicationType: { key: "CommunicationType", value: val["CommTypeValue"] },
                                    Duration: { key: "CallDuration", value: ConvertSecondsToMinute(val["CallDuration"]) }
                                });
                                if (val["CallDuration"] !== undefined) {
                                    CallHistoryData.CallHistory.C2CCallDuration = parseInt(CallHistoryData.CallHistory.C2CCallDuration) + parseInt(val["CallDuration"]);
                                }
                            }

                        });
                    }
                setCallDetails(CallHistoryData);
            }
        });
    }

    const ConvertSecondsToMinute = (seconds) => {

        if (seconds > 0) {
            var hour = Math.floor(seconds / 3600),
                min = Math.floor((seconds - hour * 3600) / 60),
                sec = seconds - hour * 3600 - min * 60,
                hourStr, minStr, secStr;
            if (hour) {
                hourStr = hour.toString();
                minStr = min < 9 ? "0" + min.toString() : min.toString();
                secStr = sec < 9 ? "0" + sec.toString() : sec.toString();
                return hourStr + ":" + minStr + ":" + secStr + " hrs";
            }
            if (min) {
                minStr = min.toString();
                secStr = sec < 9 ? "0" + sec.toString() : sec.toString();
                return minStr + ":" + secStr + " min";
            }
            return sec.toString() + " sec";
        }
        else {
            return "0 sec";
        }
    }

    useEffect(() => {
        if (parentLeadId) {
            setCallDetails(CallHistoryData);
            setTimeout(() => {
                getCustomerCallSummary();
            }, 1500)
        }
    }, [parentLeadId]);

    // const handleToggle = (e) => {
    //     if (!show) {
    //         getCustomerCallSummary();
    //     }
    //     setShow(!show);
    // }
    // useEffect(() => {
    //     if (RefreshLead) {
    //         setShow(false);
    //     }
    // }, [RefreshLead]);
    return (
        <Grid item sm={12} md={12} xs={12}>
            {/* <div className={show ? 'callDetails opentoggleDivHeight ' : 'callDetails '}> */}
            <h3 className="calldetails">Call Details</h3>
            {/* <div className="expandmoreIcon expandarrow cursorPointer"> <ExpandMore onClick={handleToggle} style={{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)' }} /></div>     */}
            {/* {show && */}
            <ul>
                <li>Total Call Attempts</li>
                <li><Tooltip enterTouchDelay={0} leaveTouchDelay={20000} interactive placement="left" classes={{ tooltip: classes.customSize, popper: "addmoreLeadPopup" }}
                    title={<CallAttemptsPopUp TotalCallDetails={CallDetails.TotalCallDetails.length > 0 ? CallDetails.TotalCallDetails : []} />}>
                    <div className="detailBox"><span className="hoverable">{CallDetails.CallHistory.TotalCalls ? CallDetails.CallHistory.TotalCalls : '0'}</span></div></Tooltip></li>

                <li>Connected Call</li>
                <li><Tooltip enterTouchDelay={0} leaveTouchDelay={20000} interactive placement="left" classes={{ tooltip: classes.customSize, popper: "addmoreLeadPopup" }}
                    title={<ConnectedCalls ConnectedCalls={CallDetails.ConnectedCallDetails.length > 0 ? CallDetails.ConnectedCallDetails : []} />}>
                    <div className="detailBox"><span className="hoverable">{CallDetails.CallHistory.ConnectedCalls ? CallDetails.CallHistory.ConnectedCalls : '0'}</span></div></Tooltip></li>
                <li>Total Call Duration</li>
                <li>{ConvertSecondsToMinute(CallDetails.CallHistory.CallDuration)}</li>

                <li>Total I/B Call Duration</li>
                <li>{ConvertSecondsToMinute(CallDetails.CallHistory.InboundCallDuration)}</li>

                <li>Total CTC Call Duration</li>
                <li>{ConvertSecondsToMinute(CallDetails.CallHistory.C2CCallDuration)}</li>

            </ul>
            {/* } */}
            {/* </div> */}

        </Grid>
    )

}
