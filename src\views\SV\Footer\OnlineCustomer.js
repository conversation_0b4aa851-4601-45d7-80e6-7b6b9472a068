import React, { useEffect, useState } from "react";
import ScrollMenu from 'react-horizontal-scrolling-menu';
import { ChevronRight, ChevronLeft } from '@mui/icons-material';
import { useSelector } from "react-redux";
import { CONFIG } from "../../../appconfig";
import dayjs from "dayjs";
import pluck from 'underscore/modules/pluck';
import { copyToClipboard } from "../../../helpers";
const Arrow = ({ text, className }) => {
    return (
        <div className={className}>{text}</div>
    );
};

const ArrowLeft = Arrow({ text: <ChevronLeft />, className: 'arrow-prev' });
const ArrowRight = Arrow({ text: <ChevronRight />, className: 'arrow-next' });

const OnlineCustomer = (props) => {
    //let [parentLeadId] = useSelector(({ salesview }) => [salesview.parentLeadId]);
    // const [Customers, setCustomers] = useState([{ 'Key': 'test1' }, { 'Key': 'test2' }, { 'Key': 'test3' }]);
    // const { showCaller, setShowCaller } = props;
    // let [Filterarr, setFilterarr] = useState({});
    let [Page, setPage] = useState([]);

    let [AgentStats] = useSelector(state => {
        let { AgentStats } = state.salesview;
        return [AgentStats];
    });
    const isMobile = useSelector(state => state.common.isMobile);

    let arr = AgentStats[0] && Array.isArray(AgentStats[0].revisit) ? AgentStats[0].revisit : [];
    //sample data
    // let arr = [
    //     {
    //         "ts": "2020-12-14T15:14:05.3706866+05:30",
    //         "visitLead": 343579086,
    //         "LeadId": 343579086,
    //         "CustName": "test1okayh okadasdasfsafasf",
    //         "visitEnquiryID": 0,
    //         "RoomCode": null,
    //         "AgentId": 19411,
    //         "Page": "payment"
    //     },
    //     {
    //         "ts": "2020-12-14T15:13:05.3706866+05:30",
    //         "visitLead": 343579084,
    //         "LeadId": 343579084,
    //         "CustName": "Ashish Masih lumar",
    //         "visitEnquiryID": 0,
    //         "RoomCode": null,
    //         "AgentId": 19411,
    //         "Page": "Base Pricing Page"
    //     },
    //     {
    //         "ts": "2020-12-14T15:14:05.3706866+05:30",
    //         "visitLead": 343579086,
    //         "LeadId": 343579086,
    //         "CustName": "test1",
    //         "visitEnquiryID": 0,
    //         "RoomCode": null,
    //         "AgentId": 19411,
    //         "Page": "payment"
    //     },
    //     {
    //         "ts": "2020-12-14T15:15:05.3706866+05:30",
    //         "visitLead": 343579085,
    //         "LeadId": 343579085,
    //         "CustName": "test3",
    //         "visitEnquiryID": 0,
    //         "RoomCode": null,
    //         "AgentId": 19411,
    //         "Page": "proposal"
    //     }, {
    //         "ts": "2020-12-14T15:13:05.3706866+05:30",
    //         "visitLead": 343579084,
    //         "LeadId": 343579084,
    //         "CustName": "Ashish Masih",
    //         "visitEnquiryID": 0,
    //         "RoomCode": null,
    //         "AgentId": 19411,
    //         "Page": "Base Pricing Page"
    //     },
    //     {
    //         "ts": "2020-12-14T15:14:05.3706866+05:30",
    //         "visitLead": 343579086,
    //         "LeadId": 343579086,
    //         "CustName": "test1",
    //         "visitEnquiryID": 0,
    //         "RoomCode": null,
    //         "AgentId": 19411,
    //         "Page": "payment"
    //     },
    //     {
    //         "ts": "2020-12-14T15:14:05.3706866+05:30",
    //         "visitLead": 343579086,
    //         "LeadId": 343579086,
    //         "CustName": "test1",
    //         "visitEnquiryID": 0,
    //         "RoomCode": null,
    //         "AgentId": 19411,
    //         "Page": "payment"
    //     },
    //     {
    //         "ts": "2020-12-14T15:14:05.3706866+05:30",
    //         "visitLead": 343579086,
    //         "LeadId": 343579086,
    //         "CustName": "test1",
    //         "visitEnquiryID": 0,
    //         "RoomCode": null,
    //         "AgentId": 19411,
    //         "Page": "payment"
    //     },
    // ]


    // const groupBy = (arr, property) => {
    //     return arr.reduce(function (memo, x) {
    //         if (!memo[x[property]]) { memo[x[property]] = []; }
    //         memo[x[property]].push(x);
    //         return memo;
    //     }, {});
    // }

    useEffect(() => {
        if (arr.length > 0) {
            //  setFilterarr(groupBy(arr, 'Page'));
            setPage(pluck(arr, 'Page'));
        }
    }, [AgentStats]);

    // const Settitle = (str) => {
    //     let acronym = "";
    //     if (str) {
    //         let matches = str.match(/\b(\w)/g);
    //         acronym = matches.join('').toUpperCase();
    //     }
    //     return acronym
    // }

    const SetPageColor = (Index, CurrrentPage) => {
        let _Page = Page.indexOf(CurrrentPage);
        switch (_Page) {
            case 0:
                return 'green';
            case 1:
                return 'violet';
            default:
                return 'orange';
        }
    }


    let CustomersHTML = [];


    CustomersHTML.push(
        <div>
            <div className="footer-list">
                {arr.map((vdata, innerIdx) => (
                    <div key={innerIdx} className="footer-bl">
                        <div className="right-block">
                            <p className="name">{vdata.CustName}</p>
                            <p className="id-date"><span onClick={copyToClipboard}>{vdata.LeadId}</span> | {dayjs(vdata.ts).format('h:mm a')}</p>
                            <p className={SetPageColor(innerIdx, vdata.Page)}>
                                <span>
                                    {
                                        vdata.Page === 'WAITING ON PBMEET' && dayjs().diff(dayjs(vdata.ts), 'minute') >= 5
                                            ? 'VISITED ON PBMEET'
                                            : vdata.Page
                                    }
                                    {
                                        vdata.Page === 'WAITING ON PBMEET' && dayjs().diff(dayjs(vdata.ts), 'minute') < 5 &&
                                        <span className="blink-dot" />
                                    }
                                </span>
                            </p>
                        </div>
                    </div>
                ))}

            </div>
        </div>
    )

    return (
        <div id="OnlineCustomers" className={isMobile && arr.length===0 ? "noCustomerbox": ""}>
            {arr.length > 0 ?
                (!isMobile)
                    ? < ScrollMenu
                        data={CustomersHTML}
                        arrowLeft={ArrowLeft}
                        arrowRight={ArrowRight}
                        translate={1}
                        alignCenter={false}
                        hideSingleArrow={true}
                        useButtonRole={false}
                        disableTabindex
                        wheel={false}
                    />
                    : CustomersHTML
                : <h1 className="no-customer"><img alt="No online Customers" src={CONFIG.PUBLIC_URL + "/images/salesview/no-customeronline.svg"} /> No Online Customer <p>Check back again after sometime</p></h1>
            }
        </div>
    )
}
export default OnlineCustomer;