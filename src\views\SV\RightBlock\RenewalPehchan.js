import { ExpandMore } from "@mui/icons-material";
import { useSnackbar } from "notistack";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import SelectDropdown from "../../../components/SelectDropdown";
import { CALL_API } from "../../../services/api.service";
import { SetCustomerComment } from "../../../services/Common";
import rootScopeService from "../../../services/rootScopeService";
import User from "../../../services/user.service";
import { TextInput } from "../../../components";

const RenewalPehchan = () => {
    const { enqueueSnackbar } = useSnackbar();
    const [show, setShow] = useState(false);
    const [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);
    const [ParentLeadId, PrimaryLeadId, allLeads] = useSelector(state => {
        let { parentLeadId, primaryLeadId, allLeads } = state.salesview;
        return [parentLeadId, primaryLeadId, allLeads];
    });

    const [PehchanID, setPehchanID] = useState(null);
    const [RenewalLead, setRenewalLead] = useState(0);
    const [visibleLeads, setvisibleLeads] = useState([]);

    const handleChange = (e) => {
        const { name, value, type } = e.target;
        if (name === "RenewalLead") { setRenewalLead(value); }
        else { setPehchanID(value.trim()); }
    }

    const SaveComment = (Comment) => {
        let UserId = User.UserId;
        var requestData = {
            "CustomerId": rootScopeService.getCustomerId(),
            "ProductId": rootScopeService.getProductId(),
            "ParentLeadId": RenewalLead,
            PrimaryLeadId,
            UserId,
            "Comment": Comment,
            "EventType": 12
        };
        SetCustomerComment(requestData);
    }

    useEffect(() => {
        if (RenewalLead != 0)
            GetHealthPehchanID()
    }, [RenewalLead]);

    const GetHealthPehchanID = () => {

        const input = {
            url: `coremrs/api/LeadDetails/GetHealthNeedAnalysis/` + rootScopeService.getCustomerId() + '/' + RenewalLead + '/2',
            method: 'GET', service: 'MatrixCoreAPI'
        };
        CALL_API(input).then((response) => {
            response = response.Data;
            if (response) {
                setPehchanID(response.PehchanID)
            }
            else setPehchanID(null);
        }, function () {
            setPehchanID(null);
        });
    };

    const SetHealthPehchanID = () => {
        const reqData = {
            CustomerId: rootScopeService.getCustomerId(),
            PehchanID: PehchanID,
            ParentId: RenewalLead,
            RenewalFlag: 2
        };
        if (PehchanID == null || PehchanID == undefined || PehchanID.toLowerCase() == 'na' || PehchanID.toLowerCase() == 'null' || PehchanID == '0') {
            enqueueSnackbar('Please enter correct value', {
                variant: 'error',
                autoHideDuration: 3000,
            });
        }
        else {
            const input = {
                url: `coremrs/api/LeadDetails/SetHealthNeedAnalysis/`,
                method: 'POST', service: 'MatrixCoreAPI',
                requestData: reqData
            };
            CALL_API(input).then((resultData) => {
                if (resultData) {
                    GetHealthPehchanID();
                    SaveComment("Customer Pehchan ID Updated.");
                    enqueueSnackbar("Details Updated Successfully.", {
                        variant: 'success',
                        autoHideDuration: 3000,
                    });
                    for (let key in allLeads) {
                        if (allLeads[key].LeadID === RenewalLead
                            && allLeads[key].StatusId >= 13) {
                            PushPehchanToBMS()
                        }
                    }
                }
            }, function () {
                enqueueSnackbar('Something went wrong, Please Connect the Support team.', {
                    variant: 'error',
                    autoHideDuration: 3000,
                });
            });
        }
    }

    const PushPehchanToBMS = () => {
        const input = {
            url: `api/healthrenewal/PushPehchanToBMS/` + RenewalLead + `/` + PehchanID,
            method: 'GET', service: 'MatrixCoreAPI',
        };
        CALL_API(input).then((resultData) => { }, function () { });
    }

    const handleToggle = (e) => {
        setShow(!show);
        setRenewalLead(visibleLeads[0]);
        GetHealthPehchanID();
    }
    useEffect(() => {
        if (RefreshLead) {
            setShow(false);
        }
        let LeadList = [];
        for (let key in allLeads) {
            if (allLeads[key].LeadSource === "Renewal"
                && [16, 2].indexOf(allLeads[key].RenewalSupplierId) !== -1
                && !LeadList.includes(allLeads[key].LeadID)) {
                LeadList.push(allLeads[key].LeadID)
            }
        }
        setvisibleLeads(LeadList);

    }, [RefreshLead]);

    return <>
        <div className="addInfo">
            <h3>HDFC Renewal PehchanID</h3>
            <div className="expandmoreIcon">
                <ExpandMore onClick={handleToggle} style={{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)' }} /></div>
            <p className="caption"> </p>
            {show &&
                <>
                    <SelectDropdown
                        name="RenewalLead"
                        label="Renewal Lead"
                        value={RenewalLead}
                        options={visibleLeads}
                        labelKeyInOptions='_all'
                        valueKeyInOptions="_all"
                        handleChange={handleChange}
                        sm={12} md={12} xs={12}
                    />

                    <div className="Additional-details">
                        <div>Pehchan ID</div>

                    </div>

                    <TextInput
                        name="PehchanID"
                        label="ID"
                        maxLength={50}
                        handleChange={handleChange}
                        value={PehchanID}
                        sm={12} md={12} xs={12}
                    />
                    <button onClick={SetHealthPehchanID} className="submitBtn">Submit</button>
                </>
            }
        </div>
    </>

}
export default RenewalPehchan;
