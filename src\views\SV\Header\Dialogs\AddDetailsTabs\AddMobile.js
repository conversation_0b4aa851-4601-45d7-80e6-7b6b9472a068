import React, { useEffect, useState } from "react";
import { Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, TextField, Radio } from "@mui/material";
import dayjs from "dayjs";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import RefreshIcon from "@mui/icons-material/Refresh";
import rootScopeService from "../../../../../services/rootScopeService";
import { Autocomplete } from '@mui/material';
import { SV_CONFIG } from "../../../../../appconfig";
import { CALL_API } from "../../../../../services";
import User from "../../../../../services/user.service";
import { useSnackbar } from "notistack";
import masterService from "../../../../../services/masterService";
import { JsonToNormalDate } from "../../../../../utils/utility";
import { TextInput } from "../../../../../components";
import { useQuery } from "../../../../../hooks/useQuery";
import { IsAswatUser, UpdateCallableNumber } from "../../../../../services/Common";
import { useSelector } from "react-redux";

export const AddMobile = ({ parentLeadId }) => {
    const [Countries, setCountries] = useState([""]);
    const [IsViewNo, setIsViewNo] = useState(false);
    const [loading, setLoading] = useState(false);
    const [IsShowSendVerificationCode, setIsShowSendVerificationCode] = useState(true);
    const [IsSendVerificationBtnDisabled, setIsSendVerificationBtnDisabled] = useState(false);
    const [VerificationMessageColor, setVerificationMessageColor] = useState("");
    const [timerIntervalId, settimerIntervalId] = useState(0);
    const IsLeadRenewal = useSelector(state => state.salesview.IsRenewal);

    const [IsShowEnterVerificationCode, setIsShowEnterVerificationCode] = useState(false);
    const [VerificationMessage, setVerificationMessage] = useState("");
    let masterTimer = 60;
    let ResendMaxLimit = 4;
    const [timer, setTimer] = useState(masterTimer);
    const [IsResendShow, setIsResendShow] = useState(false);
    const [IsSuccessfullySaved, setIsSuccessfullySaved] = useState(false);
    let [InputMobile, setInputMobile] = useState("");
    let [InputName, setInputName] = useState("");
    let [InputCountry, setInputCountry] = useState("");
    let [IsCallableArr, setIsCallableArr] = useState({});
    let [selectedValue, setSelectedValue] = useState("");
    const [viewTrad, setViewTrad] = useState(false);
    let [InputVerification, setInputVerification] = useState("");
    const [ResendCount, setResendCount] = useState(0);
    const [IsNRICustomer, setIsNRICustomer] = useState(false);
    const [mobileData, setmobileData] = useState([]);
    const ShowAssignCriticalComponents = useSelector(state => state.salesview.ShowAssignCriticalComponents);

    const { enqueueSnackbar } = useSnackbar();
    const query = useQuery();
    let src = query.get('src') || "matrix";
    try { src = src.toLowerCase(); }
    catch { }
    const isBMS = ["bms"].includes(src);
    const apiSource = query.get('src') || "Matrix"
    const handleChange = (event) => {
        const regx = /^[0-9\b]+$/;
        switch (event.target.name) {
            case "IsCallable":
                if (event.target.value === selectedValue) {
                    setSelectedValue("");
                } else {
                    setSelectedValue(event.target.value);
                }
                break;
            case "txtMobile":
                if (event.target.value !== "" && !regx.test(event.target.value)) {
                    enqueueSnackbar("Only Numbers allowed", {
                        variant: "error",
                        autoHideDuration: 3000,
                    });
                } else {
                    setInputMobile(event.target.value);
                }
                break;
            case "txtName":
                setInputName(event.target.value);
                break;
            case "txtVerification":
                if (event.target.value !== "" && !regx.test(event.target.value)) {
                    enqueueSnackbar("Only Numbers allowed", {
                        variant: "error",
                        autoHideDuration: 3000,
                    });
                } else {

                    setInputVerification(event.target.value);
                }
                break;
            default:
                break;
        }
    }
    const GetCallingNumber = (DefaultCountry = true) => {
        GetCountryList(DefaultCountry)

        if (!parentLeadId) return;
        setmobileData([]); //clear details of previous lead
        masterService.GetCallingDetails(rootScopeService.getCustomerId(), parentLeadId).then(
            (result) => {
                // result = result ? JSON.parse(result) : null;
                setLoading(false);
                if (Array.isArray(result)) {
                    setmobileData(result);//setIsNRICustomer
                    let _PrimaryNumberRecord = result.filter((vdata) => vdata.IsPrimary == true && vdata.CountryId != 392);

                    Array.isArray(_PrimaryNumberRecord) && _PrimaryNumberRecord.length > 0 ? setIsNRICustomer(true) : setIsNRICustomer(false)

                    setIsCallableArr(result.filter((vdata) => vdata.IsCallable)[0] || {});
                } else {
                    setmobileData([]);

                }
            }
        );
    };
    const GetCountryList = (DefaultCountry = true) => {
        masterService.GetCountryList().then((result) => {
            if (result && Array.isArray(result)) {
                setCountries(result);
                if (DefaultCountry) {
                    setInputCountry(
                        result.filter((x) => x.CountryID === 392)[0]
                    );
                }
            }
        });
    };
    const setPrimaryMobileNo = function () {
        let primaryMobile = IsCallableArr;
        var reqData = {
            CustMobId: primaryMobile.CustMobId,
            customerId: rootScopeService.getCustomerId(),
            LeadId: parentLeadId,
            mobNo: primaryMobile.EncryptedMobileNo,
            countryId: primaryMobile.CountryId,
            ProductId: rootScopeService.getProductId(),
            UserId: User.UserId,
            Source: apiSource,
        };

        UpdateCallableNumber(reqData).then((result) => {
            // result = result ? JSON.parse(result) : null;
            if (result.statusCode && result.statusCode == 1) {
                GetCallingNumber(!IsSendVerificationBtnDisabled);
                enqueueSnackbar("Callable number updated", {
                    variant: "success",
                    autoHideDuration: 3000,
                });
            } else {
                enqueueSnackbar(result.statusMsg, {
                    variant: "error",
                    autoHideDuration: 3000,
                });
            }
        })

    };
    const ViewMobileNo = (row) => {
        if (User.UserId > 0) {
            // requestData: { "LeadID": parentLeadId, "UserID": User.UserId, "mobileNo": row.EncryptedMobileNo, "AgentId": User.UserId }
            const input = {
                url: `api/SalesView/ViewPhoneNo/${parentLeadId}`,
                method: "GET",
                service: "MatrixCoreAPI",
                Headers: {
                    Token: localStorage.getItem("AsteriskToken"),
                    mobileNo: row.EncryptedMobileNo,
                    AgentId: User.UserId,
                    "Content-Type": "application/json",
                },
            };

            CALL_API(input).then((response) => {
                console.log(response);
                if (response) {
                    let index = mobileData.findIndex(
                        (x) => x.CustMobId === row.CustMobId
                    );
                    var arr = [...mobileData];
                    if (index > -1) {
                        arr[index].MobileNo = response;
                        arr[index].IsViewed = true;
                    }
                    setmobileData(arr);
                }
            });
        }
    };
    const validateMobileForVerification = () => {
        if (InputCountry == "" || InputCountry == null) {
            enqueueSnackbar("Please enter valid country", {
                variant: "error",
                autoHideDuration: 3000,
            });
            return false;
        } else if (
            InputCountry.CountryID == 392 &&
            (InputMobile.length < 10 || InputMobile.length > 10)
        ) {
            enqueueSnackbar("Please enter valid no", {
                variant: "error",
                autoHideDuration: 3000,
            });
            return false;
        } else if (
            InputCountry.CountryID !== 392 &&
            (InputMobile.length < 5 || InputMobile.length > 12)
        ) {
            enqueueSnackbar("Please enter valid no", {
                variant: "error",
                autoHideDuration: 3000,
            });
            return false;
        }

        if (InputCountry.CountryID === "999") {
            enqueueSnackbar("MobileNo against other country not allowed.", {
                variant: "success",
                autoHideDuration: 3000,
            });
            return false;
        }
        return true;
    };
    const onChangeValue = (row) => {
        setIsCallableArr(row);
    };

    const ClearMobileState = () => {
        setIsSendVerificationBtnDisabled(false);
        setInputMobile("")
        setInputCountry({ CountryID: "392", Country: "INDIA" });
        setIsShowSendVerificationCode(true);
        setVerificationMessage("");
        setIsShowEnterVerificationCode("")
        setInputVerification("")
        setResendCount(0);
        setIsResendShow(false);

        clearInterval(timerIntervalId);
        setTimer(masterTimer);
        setIsSuccessfullySaved(false);

        setInputName("");
    }
    const SaveMobileDetail = async () => {
        clearInterval(timerIntervalId);
        if (ResendCount < ResendMaxLimit) { setIsResendShow(true) };
        var chkflag = 0;
        if (InputCountry == "" || InputCountry == null) {
            enqueueSnackbar("Please enter valid country", {
                variant: "error",
                autoHideDuration: 3000,
            });
            return false;
        } else if (
            InputCountry.CountryID == 392 &&
            (InputMobile.length < 10 || InputMobile.length > 10)
        ) {
            enqueueSnackbar("Please enter valid no", {
                variant: "error",
                autoHideDuration: 3000,
            });
            return false;
        } else if (
            InputCountry.CountryID !== 392 &&
            (InputMobile.length < 5 || InputMobile.length > 12)
        ) {
            enqueueSnackbar("Please enter valid no", {
                variant: "error",
                autoHideDuration: 3000,
            });
            return false;
        }
        else if (isShowOTPProduct &&
            (InputVerification == "" ||
                (InputVerification && InputVerification.length != 6))
        ) {
            enqueueSnackbar("Please verify 6 digit verification code", {
                variant: "error",
                autoHideDuration: 3000,
            });
            return false;
        }
        else if (InputName === "") {
            setInputName("self");
        }
        if (InputCountry.CountryID === "999") {
            enqueueSnackbar("MobileNo against other country not allowed.", {
                variant: "success",
                autoHideDuration: 3000,
            });
            return false;
        }

        setLoading(true); // loading
        let resultData = await masterService.GetCallingDetails(
            rootScopeService.getCustomerId(),
            parentLeadId
        ); //Getcallingdetails

        if (Array.isArray(resultData)) {
            resultData.forEach((vdata, key) => {
                if (
                    atob(vdata.EncryptedMobileNo) === InputMobile &&
                    vdata.Country === InputCountry.Country
                ) {
                    setLoading(false);
                    enqueueSnackbar("Number already exists.", {
                        variant: "error",
                        autoHideDuration: 3000,
                    });
                    setInputMobile("");
                    setInputName("");
                    //setInputCountry();
                    setInputCountry("");
                    setInputVerification("");
                    chkflag = 1;
                }
            });

            if (chkflag === 0) {
                var reqData = {
                    CustomerId: rootScopeService.getCustomerId(),
                    ProductId: rootScopeService.getProductId(),
                    MobileNo: InputMobile,
                    Name: InputName !== "" ? InputName : "Self",
                    CountryID: InputCountry.CountryID,
                    UserId: User.UserId,
                    Source: apiSource,
                    Type: "mobileno",
                    LeadId: parentLeadId,
                    OTP: isShowOTPProduct ? InputVerification : "",
                };
                //todo update cust
                const _input = {
                    url: `api/SalesView/SetCustContactInfo`,
                    method: "POST",
                    service: "MatrixCoreAPI",
                    timeout: "m",
                    requestData: reqData,
                    source: src
                };
                let response = await CALL_API(_input); //Setcallingdetails
                // response = response ? JSON.parse(response) : null;
                GetCallingNumber(!IsSendVerificationBtnDisabled);
                //response.status=false//*to be removed
                if (response && response.status) {
                    // response.hasOwnProperty("AddContactNumberResult") &&
                    // response.AddContactNumberResult.IsSaved
                    setLoading(false);
                    // Insert in to Activity Feed.
                    //   $scope.SaveComment("Additional contact number added " + $scope.MaskMobile($scope.NewContact.newMobileNumber));
                    setInputMobile("");
                    setInputName("");
                    setInputVerification("");
                    setVerificationMessage("");
                    setInputCountry({ CountryID: "392", Country: "INDIA" });
                    setIsResendShow(false);
                    setIsSuccessfullySaved(true);
                    //$scope.MobileValidation = /^[1-9]\d{9}$/;
                    enqueueSnackbar("Additional contact number added!", {
                        variant: "success",
                        autoHideDuration: 3000,
                    });
                    ClearMobileState();
                } else if (
                    response &&
                    !response.status
                    // response.AddContactNumberResult &&
                    // !response.AddContactNumberResult.IsSaved
                ) {
                    setLoading(false);


                    setVerificationMessage("");
                    setInputVerification("");
                    enqueueSnackbar(response.message, {
                        variant: "error",
                        autoHideDuration: 3000,
                    });
                }
            }
        }
    };
    const SetIsViewNo = () => {
        if (
            User.ViewGrpCount &&
            User.ViewGrpCount > 0 &&
            User.viewPrd &&
            User.viewPrd.indexOf(rootScopeService.getProductId().toString()) !== -1
        ) {
            setIsViewNo(true);
        }
        if (
            User.RoleId == 13 &&
            User.viewPrd &&
            User.viewPrd.length > 0 &&
            User.viewPrd.indexOf(rootScopeService.getProductId().toString()) != -1
        ) {
            User.PrdGroupList.forEach(function (item) {
                if ([1614].indexOf(parseInt(item.GroupId)) != -1) {
                    setViewTrad(true);
                }
            });
        }
        if (!viewTrad) {
            SV_CONFIG["ViewList"]["L"].forEach(function (item) {
                if (item["ProductId"] == rootScopeService.getProductId()) {
                    if (
                        item["UserIds"] &&
                        Array.isArray(item["UserIds"]) &&
                        item["UserIds"].length > 0 &&
                        item["UserIds"].indexOf(User.UserId) !== -1
                    ) {
                        setViewTrad(true);
                    } else {
                        if (
                            item["GroupIds"] &&
                            Array.isArray(item["GroupIds"]) &&
                            item["GroupIds"].length > 0 &&
                            User.RoleId == 13
                        ) {
                            item["GroupIds"].forEach(function (element) {
                                User.PrdGroupList.forEach(function (e) {
                                    if (element["GroupId"] == e.GroupId) {
                                        if (element["IsNRI"] == 1) {
                                            setIsViewNo(true);
                                            setViewTrad(false);
                                        } else {
                                            setViewTrad(true);
                                        }
                                    }
                                });
                            });
                        }
                    }
                }
            });
        }
    }
    useEffect(() => {
        ClearMobileState()
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])
    useEffect(() => {
        if (parentLeadId) {
            GetCallingNumber(!IsSendVerificationBtnDisabled);
            SetIsViewNo();
            setResendCount(0);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [parentLeadId])
    useEffect(() => {
        if (timer < 0) {
            // console.log(timer, timerIntervalId)
            clearInterval(timerIntervalId);
            settimerIntervalId(0);
            if (ResendCount < ResendMaxLimit)
                setIsResendShow(true);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [timer]);
    const SendVerificationCode = () => {
        setVerificationMessage("");
        // setIsCodeVerified(false);
        const isValid = validateMobileForVerification();
        if (!isValid) {
            return;
        }

        const input = {
            url: `api/SalesView/SendAdditionalNoOTP`,
            method: "POST",
            service: "MatrixCoreAPI",
            //requestData:{},
            requestData: {
                CustId: rootScopeService.getCustomerId(),
                LeadId: parentLeadId,
                MobileNo: InputMobile,
                IsNRICust: IsNRICustomer,
                // InputCountry &&
                //   InputCountry.CountryID &&
                //   InputCountry.CountryID !== 392
                //   ? true
                //   : false,
            }, //
            source: src
        };
        return CALL_API(input)
            .then((res) => {
                //res.status=true;//*to be removed
                setIsResendShow(false);
                setResendCount(prev => prev + 1);
                setIsSendVerificationBtnDisabled(true);
                setIsShowSendVerificationCode(false);
                setIsShowEnterVerificationCode(true);
                res ? setVerificationMessage(res.message) : setVerificationMessage("");

                if (res && !res.status) {
                    ResendCount < ResendMaxLimit ? setIsResendShow(true) : setIsResendShow(false);
                    setVerificationMessageColor("2");
                } else {
                    clearInterval(timerIntervalId);
                    setTimer(masterTimer);
                    let IntervalId = setInterval(() => {
                        // console.log("New date",new Date())
                        setTimer((prev) => prev - 1);
                    }, 1000);
                    settimerIntervalId(IntervalId);
                    // setTimeout(showResend, masterTimer * 1000);
                    setVerificationMessageColor("1");
                    console.log("Sent Verification code");
                }
            })
            .catch((error) => {
                if (ResendCount < ResendMaxLimit) { setIsResendShow(true) };
                setVerificationMessageColor("2");
                console.log("Not able to send verification code", error);
            }); //Getcallingdetails
    };

    let GetShowOTPProducts = (window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.GetShowOTPProducts) || SV_CONFIG.GetShowOTPProducts;
    let isShowOTPProduct =
        (Array.isArray(GetShowOTPProducts) && GetShowOTPProducts.includes(rootScopeService.getProductId()));


    return (
        <Grid container spacing={3}>
            {
                (!["matrix"].includes(src) || ShowAssignCriticalComponents) && isShowOTPProduct ? (
                    <>
                        <Grid item sm={10} md={10} xs={12}>
                            <Autocomplete
                                onChange={(event, value) => setInputCountry(value)}
                                id="combo-box-demo"
                                options={Countries}
                                value={InputCountry || null}
                                // defaultValue={Countries.filter(x => x.CountryID === 392)[0]}
                                getOptionLabel={(option) => option.Country || ""}
                                //style={{ width: 337 }}
                                className={IsSendVerificationBtnDisabled ? "disabledInput" : ""}
                                disabled={IsSendVerificationBtnDisabled}
                                renderInput={(params) => (
                                    <TextField
                                        {...params}
                                        label="Enter Country"
                                        //defaultValue="INDIA"
                                        variant="outlined"
                                    />
                                )}
                            />
                        </Grid>

                        <TextInput
                            name="txtMobile"
                            label="Enter Mobile No."
                            handleChange={handleChange}
                            pattern="[0-9]*"
                            maxLength="12"
                            value={InputMobile}
                            className={IsSendVerificationBtnDisabled ? "disabledInput" : ""}
                            disabled={IsSendVerificationBtnDisabled}
                            sm={5}
                            md={5}
                            xs={6}
                        />

                        {IsShowSendVerificationCode && (
                            <Grid item sm={5} md={5} xs={6}>
                                <button
                                    className="SendVerificationBtn"
                                    onClick={() => {
                                        SendVerificationCode();
                                    }}
                                >
                                    Send Verification Code
                                </button>
                            </Grid>
                        )}

                        {IsShowEnterVerificationCode && (
                            <Grid item sm={5} md={5} xs={12}>
                                <div className="verificationTime">
                                    <TextInput
                                        name="txtVerification"
                                        label="Enter Verification Code"
                                        handleChange={handleChange}
                                        pattern="[0-9]*"
                                        maxLength="6"
                                        value={InputVerification}
                                        sm={12}
                                        md={12}
                                        xs={12}
                                    />
                                    {!IsResendShow && !IsSuccessfullySaved && ResendCount < ResendMaxLimit && timer > 0 && (
                                        <p>
                                            <AccessTimeIcon />
                                            {timer + "sec"}
                                        </p>
                                    )}

                                    {IsResendShow && (
                                        <p
                                            onClick={() => {
                                                SendVerificationCode();
                                            }}
                                        >
                                            <RefreshIcon />
                                            Resend
                                        </p>
                                    )}

                                </div>
                            </Grid>
                        )}
                        {VerificationMessage !== "" && (
                            <Grid item sm={10} md={10} xs={12} className="pdtopBt-0">
                                <div
                                    className={
                                        VerificationMessageColor === "1"
                                            ? "verificationNoti"
                                            : "verificationNotiRed"
                                    }
                                >
                                    {VerificationMessage}
                                    {/* 4 digit verification code would be sent on customer's primary
                number (XXXXXXXX12) to validate this alternate number */}
                                </div>
                            </Grid>
                        )}
                        <TextInput
                            name="txtName"
                            label="Enter Name"
                            handleChange={handleChange}
                            value={InputName}
                            sm={10}
                            md={10}
                            xs={12}
                        />

                        <Grid item sm={12} md={12} xs={12}>
                            {loading === false ? (
                                <button className="saveButton" onClick={SaveMobileDetail}>
                                    Save
                                </button>
                            ) : (
                                <button className="disablebtn">Saving.....</button>
                            )}
                        </Grid>
                    </>
                ) : (User.RoleId !== 13) &&
                <>
                    <Grid item sm={8} md={8} xs={12}>
                        <Autocomplete
                            onChange={(event, value) => setInputCountry(value)}
                            id="combo-box-demo"
                            options={Countries}
                            value={InputCountry || null}
                            // defaultValue={Countries.filter(x => x.CountryID === 392)[0]}
                            getOptionLabel={(option) => option.Country || ''}
                            style={{ width: 337 }}
                            renderInput={(params) =>
                                <TextField {...params}
                                    label="Enter Country"
                                    //defaultValue="INDIA"
                                    variant='outlined' />}
                        />
                    </Grid>

                    <TextInput
                        name="txtMobile"
                        label="Enter Mobile No."
                        handleChange={handleChange}
                        pattern="[0-9]*"
                        maxLength="12"
                        value={InputMobile}
                        sm={8} md={8} xs={12}
                    />

                    <TextInput
                        name="txtName"
                        label="Enter Name"
                        handleChange={handleChange}
                        value={InputName}
                        sm={8} md={8} xs={12}
                    />

                    <Grid item sm={12} md={12} xs={12}>

                        {loading == false ?
                            <button className="saveButton"
                                onClick={SaveMobileDetail}>
                                Save</button> :
                            <button className="disablebtn"
                            >
                                Saving.....
                            </button>
                        }
                    </Grid>
                </>
            }

            {!isBMS &&
                <>
                    <Grid item sm={12} md={12} xs={12}>
                        <TableContainer component={Paper}>
                            <Table stickyHeader aria-label="sticky table">
                                <TableHead>
                                    <TableRow>
                                        <TableCell>Name</TableCell>
                                        <TableCell>Number</TableCell>
                                        <TableCell>Country</TableCell>
                                        <TableCell>Date Added</TableCell>
                                        <TableCell align="right">Action</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {mobileData.map((row, index) => (
                                        <>
                                        
                                        {row.IsEmergencyContact && !IsLeadRenewal ? null :<TableRow key={index}>
                                            <TableCell scope="row">{row.Name}</TableCell>
                                            <TableCell align="left">
                                                {row.MobileNo}
                                                {row.IsPrimary ? " (Primary)" : ""}
                                                {row.IsEmergencyContact ? " (Emergency No)" : ""}
                                                {
                                                    (SV_CONFIG.showMobToAswatUser || !IsAswatUser())  // Hide for aswat user, showMobToAswatUser -> flag for revert
                                                    &&
                                                    (
                                                        (row.CountryId != 392 && IsViewNo && !viewTrad)
                                                        || viewTrad
                                                    )
                                                    && !row.IsViewed && (
                                                        <a onClick={() => ViewMobileNo(row)}>ViewNo</a>
                                                    )
                                                }
                                            </TableCell>
                                            <TableCell align="left">{row.Country} </TableCell>
                                            <TableCell align="right">
                                                {dayjs(JsonToNormalDate(row.CreatedOn)).format(
                                                    "DD/MM/YYYY hh:mm a"
                                                )}
                                            </TableCell>
                                            <TableCell align="right">
                                                <Radio
                                                    type="radio"
                                                    name="IsCallable"
                                                    onChange={() => {
                                                        onChangeValue(row);
                                                    }}
                                                    value={row.CustMobId}
                                                    checked={row.CustMobId === IsCallableArr.CustMobId}
                                                />
                                            </TableCell>
                                        </TableRow>}
                                        </>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </Grid>
                    {ShowAssignCriticalComponents &&
                        <Grid item sm={12} md={12} xs={12} className="text-center">
                            <button
                                className="callableBtn"
                                onClick={() => {
                                    setPrimaryMobileNo();
                                }}
                            >
                                Set Callable
                            </button>
                        </Grid>
                    }
                </>
            }
        </Grid>
    );
}