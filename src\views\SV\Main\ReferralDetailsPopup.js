import React, { useState,useEffect} from "react";
import ModalPopup from "../../../components/Dialogs/ModalPopup";
import { GetLeadReferralDetails } from "../../../services/Common";
import { Grid } from "@mui/material";

const ReferralDetailsPopup = (props) => {

    const [ConsolidatedData, setConsolidatedData] = useState({});
    let utm_medium = props.UTM_Medium;
    let utm_source = props.Utm_source;
    let ProductID = props.ProductID;
    let LeadSource = props.LeadSource;
    let LeadCreationSource = props.LeadCreationSource;
    const [Type, setType] = useState('');

    useEffect(() => {
        let type = '';
        let leadId = 0;
        if(ProductID == 2 && ((utm_medium && utm_medium.toLowerCase() == "health_renewal") || (utm_source && utm_source.toLowerCase() == "health_rm")))
        {   
            type = 'Cross-sell';
            leadId = props.ReferralLeadId;
        }
        else if (ProductID == 115 && LeadSource && ['Crosssell','CrossSell','Referral'].includes(LeadSource) && utm_source && [	'Health_RM','Health','Health_claim'].includes(utm_source))
        {
            type = 'Inv-RefersFromHealth'//Investment leads created by Health CRT/Claim
            leadId = props.LeadId;
        }
        else if(ProductID == 7 && ((utm_source && utm_medium && utm_medium == 'growth_crm_term_referral' && utm_source == 'Whatsapp_crm_sales') || ((utm_medium && utm_medium == 'growth_app_term_referral'))))
        {
            type = 'Term-Referrals'//Term Referrals
            leadId = props.LeadId;
        }
        else if ((LeadSource.toLowerCase()).includes("crosssell") && ((LeadCreationSource.toLowerCase()).includes("bms") || (LeadCreationSource.toLowerCase()).includes("claim")))
        {   
            type = 'Motor-CrossSell'//Motor CrossSell
            leadId = props.LeadId;
        }    
        else if ((LeadSource.toLowerCase()).includes("referral") && ((LeadCreationSource.toLowerCase()).includes("bms") || (LeadCreationSource.toLowerCase()).includes("claim")))
        {   
            type = 'Motor-Referral'//Motor Referral
            leadId = props.LeadId;
        }    
        setType(type);
        GetLeadReferralDetail(leadId, type);
    },[props.open])

    const GetLeadReferralDetail = (LeadId,type) => {
        GetLeadReferralDetails(LeadId,type).then((result) => {
            if(result)
            {
                setConsolidatedData(result);
            }
        })
    }

    return(
        <ModalPopup open={props.open} handleClose={props.handleClose} title="Referral Details" className="ReferalPopup">
            {Type && Type == 'Cross-sell' && 
            <Grid container spacing={2}>
                <Grid item md={6}><label>CreatedBy</label></Grid>
                <Grid item md={6}>{ConsolidatedData.CreatedBy}({ConsolidatedData.EmpCode})</Grid>
                <Grid item md={6}><label>Customer Name</label></Grid>
                <Grid item md={6}>{ConsolidatedData.CustName}</Grid>
                {utm_medium.toLowerCase() == "health_renewal" && <>
                    <Grid item md={6}><label>Team</label></Grid>
                    <Grid item md={6}>Health Renewal</Grid></>
                }
                {utm_source.toLowerCase() == "health_rm" && <>
                    <Grid item md={6}><label>Team</label></Grid>
                    <Grid item md={6}>Health CRT</Grid></>
                }    
            </Grid>}
            {Type && Type == 'Motor-CrossSell' && 
            <Grid container spacing={2}>
                <Grid item md={6}><label>Creation Source : </label></Grid>
                <Grid item md={6}>{LeadCreationSource.toLowerCase().includes("bms") ? "Motor-Service" : "Motor-Claims"}</Grid>
                <Grid item md={6}><label>Ongoing request : </label></Grid>
                <Grid item md={6}>{LeadCreationSource.toLowerCase().includes("bms") ? "Policy Related" : "Claims Related"}</Grid>
                <Grid item md={6}><label>Created By : </label></Grid>
                <Grid item md={6}>{ConsolidatedData.CreatedBy ? ConsolidatedData.CreatedBy : ""} ({ConsolidatedData.EmpCode ? ConsolidatedData.EmpCode : ""})</Grid>
                <Grid item md={6}><label>Vehicle Number : </label></Grid>
                <Grid item md={6}>{ConsolidatedData.VehicleNumber ? ConsolidatedData.VehicleNumber : ""}</Grid>
            </Grid>}
            {Type && Type == 'Motor-Referral' && 
            <Grid container spacing={2}>
                <Grid item md={6}><label>Referred By : </label></Grid>
                <Grid item md={6}>{ConsolidatedData.CustName ? ConsolidatedData.CustName : ""} ({ConsolidatedData.City  ? ConsolidatedData.City : ""},{ConsolidatedData.State  ? ConsolidatedData.State : ""})</Grid>
                <Grid item md={6}><label>Lead Created By : </label></Grid>
                <Grid item md={6}>{ConsolidatedData.CreatedBy ? ConsolidatedData.CreatedBy : ""} ({ConsolidatedData.EmpCode ? ConsolidatedData.EmpCode : ""})</Grid>
                <Grid item md={6}><label>Creation Source: </label></Grid>
                <Grid item md={6}>{LeadCreationSource.toLowerCase().includes("bms") ? "Motor-Service" : "Motor-Claims"}</Grid>
            </Grid>}
            {Type && Type == 'Inv-RefersFromHealth' && 
            <Grid container spacing={2}>
                <Grid item md={6}><label>CreatedBy</label></Grid>
                <Grid item md={6}>{ConsolidatedData.CreatedBy ? ConsolidatedData.CreatedBy : ""} ({ConsolidatedData.EmpCode ? ConsolidatedData.EmpCode : ""})</Grid>
                <Grid item md={6}><label>Customer Name</label></Grid>
                <Grid item md={6}>{ConsolidatedData.CustName ? ConsolidatedData.CustName : ""}</Grid>
                <Grid item md={6}><label>Team</label></Grid>
                <Grid item md={6}>Health CRT</Grid>
            </Grid>}
            {Type && Type == 'Term-Referrals' && 
            <Grid container spacing={2}>
                <Grid item md={6}><label>Customer Name</label></Grid>
                <Grid item md={6}>{ConsolidatedData.CustName ? ConsolidatedData.CustName : ""}</Grid>
                <Grid item md={6}><label>Insurer Name</label></Grid>
                <Grid item md={6}>{ConsolidatedData.InsurerName ? ConsolidatedData.InsurerName : ""}</Grid>
                <Grid item md={6}><label>Plan Name</label></Grid>
                <Grid item md={6}>{ConsolidatedData.PlanName ? ConsolidatedData.PlanName : ""}</Grid>
                <Grid item md={6}><label>City </label></Grid>
                <Grid item md={6}>{ConsolidatedData.City  ? ConsolidatedData.City : ""}{ConsolidatedData.State  ? ", (" + ConsolidatedData.State + ")" : ""}</Grid>
            </Grid>}
        </ModalPopup>
    )
}
export default ReferralDetailsPopup;





