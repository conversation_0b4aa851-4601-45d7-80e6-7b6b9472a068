import { Container, Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import React, { useEffect, useState } from "react";
import '../CreditChange/SalesAdvisorMapping.scss';
import ModalPopup from "../../../../components/Dialogs/ModalPopup";
import {
    IconButton
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { CALL_API } from "../../../../services";
import { useSelector } from "react-redux";
import rootScopeService from "../../../../services/rootScopeService";

const EndorsementPopup = (props) => {
   const [endorsements, setEndorsements] = useState([]);
   const [ReferralLeadId] = useSelector(state => {
        let { ReferralLeadId } = state.salesview;
        return [ReferralLeadId]
    });

    const ExtractDate = (inputDate) => {
        const date = new Date(inputDate);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        const formattedDate = `${day}/${month}/${year}`;
        return formattedDate;
    };

    const ExtractTime = (inputDate) => {
        const date = new Date(inputDate);
        let hours = date.getHours();
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const period = hours >= 12 ? 'PM' : 'AM';
        hours = hours % 12 || 12; // Convert 24-hour time to 12-hour time
        const timeString = `${hours}:${minutes} ${period}`;
        return timeString;
    };

    const columns = [
        {
            id: 'endorsementType',
            label: 'Endorsement Type'
        },
        {
            id: 'endorsementDate',
            label: 'Endorsement Date'
        },
        {
            id: 'currentStatus',
            label: 'Current Status'
        },
        {
            id: 'previousValue',
            label: 'Previous Value'
        },
        {
            id: 'endorsedValue',
            label: 'Endorsed Value'
        }
    ];

    const GetEndorsementData = () => {
        const input = {
            url: `api/SalesView/GetEndorsementData/${ReferralLeadId}/${rootScopeService.getCustomerId()}/117`,
            service: 'MatrixCoreAPI', timeout: 's',
            method: 'GET'
        }
        return CALL_API(input);
    }

    useEffect(() => {
        if(props.open == true){
            GetEndorsementData().then((result) => {
                if(result && result.Status && result.Status == true && result.Data && Array.isArray(result.Data))
                    setEndorsements(result.Data);
            })
        }
        else{
            setEndorsements([]);
        }
    },[props.open])

    return (
        
        <ModalPopup className="continueJourneyPopup" open={props.open} handleClose={props.handleClose}>
            <Container maxWidth="xl">
                <Grid container spacing={2}>
                    <Grid item sm={12} md={12} xs={12}>
                        <h2>Endorsement Details</h2>
                        <br/>
                    </Grid>
                </Grid>
            </Container>
            <IconButton onClick={props.handleClose} size="large">
                <CloseIcon />
            </IconButton>
            <Container maxWidth="xl">
                <Grid container spacing={0}>
                    <TableContainer >
                        <Table stickyHeader aria-label="endorsement table">
                            {endorsements && Array.isArray(endorsements) && endorsements.length > 0 ? 
                            <>  
                                <TableHead>
                                    <TableRow>
                                        {columns.map((column) => (
                                            <TableCell align="center"
                                                key={column.id}
                                            >
                                                {column.label}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {
                                    endorsements.map((row, index) => (
                                        <TableRow key={index} className="moreDeatilsData">
                                            <TableCell align="center">
                                                {row.EndorsementField}
                                            </TableCell>
                                            <TableCell align="center">
                                                {ExtractDate(row.EndorsementDate)}
                                                <label> &nbsp; {ExtractTime(row.EndorsementDate)}</label>
                                            </TableCell>
                                            <TableCell align="center">
                                                {row.CurrentStatus}
                                            </TableCell>
                                            <TableCell align="center">
                                                {row.OldValue}
                                            </TableCell>
                                            <TableCell align="center">
                                                {row.NewValue}
                                            </TableCell>
                                        </TableRow>
                                    ))
                                    }
                                </TableBody>
                            </> : 
                            <>
                                <h3>No Records Found</h3>
                            </>
                            }
                        </Table>
                    </TableContainer>
                </Grid>
            </Container>
        </ModalPopup>
    );
};

export default EndorsementPopup;