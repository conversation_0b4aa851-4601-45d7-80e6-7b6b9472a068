import React from "react";
import ModalPopup from '../../../../components/Dialogs/ModalPopup';
import CrossSell from '../CrossSell';

export const CrossSellModal = (props) => {
    return (
        <ModalPopup
            className="CrossSellModal"
            open={props.open}
            handleClose={props.handleClose}
            title="Sell another plan to this customer before rejecting the lead"
        >
            <CrossSell
                handleClose={props.handleClose}
                isModal={true}
                selectedLeadId={props.selectedLeadId}
            />
        </ModalPopup>
    );
};

export default CrossSellModal;