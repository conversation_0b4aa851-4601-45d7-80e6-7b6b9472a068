import React, { useEffect, useState } from "react";

import '../FOS.scss'
// import '../assets/styles/rescheduled.css'
import '../assets/styles/rescheduledCustomer.scss'
import { AddressNew, gendersMaster } from "./fosMasters";
import { useSnackbar } from 'notistack';
import { Button, Grid, Autocomplete, TextField } from "@mui/material";
import { CONFIG, SV_CONFIG } from "../../../appconfig";
import { useQuery } from "../../../hooks/useQuery";
import withStyles from '@mui/styles/withStyles';
import MuiDialogTitle from '@mui/material/DialogTitle';
import MuiDialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import Typography from '@mui/material/Typography';
import SearchMap from "./SearchMap";
import AppointmentPositionMap from "./AppointmentPositionMap";
import { useLoadScript } from "@react-google-maps/api";
import { GetAppointmentDataService, UpdateAppointmentStatusService, IsAppointmentCreatedService, GetOfflineCitiesService, getBasicLeadDetailsWrapperService } from "./fosServices";
import { TextInput } from "../../../components";
import { useSelector } from "react-redux";
import { IsSourceCustomerWhatsapp } from "./fosServices";
import { bypassGenderInput, getTimeHour, getDistinctCitiesAppointmentMapping } from "./fosCommonHelper";
import { SetAppointmentDataService } from "./fosServices";
import { FormLabel, FormControlLabel, Radio } from "@mui/material";
import { GetCustomerLocationData } from "./fosServices";
import DateRangeIcon from '@mui/icons-material/DateRange';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import RescheduledAppointmentPopup from "../../SV/Header/Dialogs/RescheduledAppointmentPopup";
import { AppointmentBooked } from "./AppointmentBooked";
import { timeConvert } from "./fosCommonHelper";
import { LinearProgress } from "@mui/material";
import { gaEventTracker } from "../../../helpers";

const styles = (theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(2),
  },
  closeButton: {
    position: 'absolute',
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
});
const libraries = ["places"];
const DialogTitle = withStyles(styles)((props) => {
  const { children, classes, onClose, ...other } = props;
  return (
    <MuiDialogTitle disableTypography className={classes.root} {...other}>
      <Typography variant="h6">{children}</Typography>
      {onClose ? (
        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={onClose}
          size="large">
          <CloseIcon />
        </IconButton>
      ) : null}
    </MuiDialogTitle>
  );
});

const DialogContent = withStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
}))(MuiDialogContent);


const AppointmentHyperLocalRenewal = (props) => {
  const { children, classes, onClose, ...other } = props;
  const [AppointmentPosition, setAppointmentPosition] = useState(null);
  const [UserSelectedPosition, setUserSelectedPosition] = useState(null);
  const [Map, setMap] = useState(null);
  // const classes = useStyles();
  const [person, setPerson] = useState({});
  const [productId, setProductId] = useState(0);
  const [CustomerId, setCustomerId] = useState(0);
  const [parentLeadId, setparentLeadId] = useState(0);
  const [token, setToken] = useState(0);
  const [subStatusId, setSubStatusId] = useState(0);
  const [showConfirmbtn, setShowConfirmbtn] = useState(false);
  const [Address, setAddress] = useState("");
  const [Loading, setLoading] = useState(true);
  const [StatusId, setStatusId] = useState(0);
  const { enqueueSnackbar } = useSnackbar();
  const [NoAppointment, setNoAppointment] = useState(false);
  const [IsSomethingwentwrong, setIsSomethingwentwrong] = useState(false);
  const [AppointmentMessage, setAppointmentMessage] = useState("");
  const [IsUpcoming, setIsUpcoming] = useState(false);
  const [CustomerSource, setCustomerSource] = useState("");
  const [appointmentDateTime, setAppointmentDateTime] = useState();
  const [NewAddress, setNewAddress] = useState(AddressNew);
  const [HouseNo, setHouseNo] = useState(null);
  const [IsCustLocationConfirmed, setIsCustLocationConfirmed] = useState(true);

  const [IsCustomerLocationData, setIsCustomerLocationData] = useState(false);
  const [AppointmentCity, setAppointmentCity] = useState(null);
  let [IsShowSaveBtn, setIsShowSaveBtn] = useState(true);
  const [IsGenderDisabled, setIsGenderDisabled] = useState(null);
  const [AppointmentBookedSection, setAppointmentBookedSection] = useState(false);
  const [DateOpen, setDateOpen] = useState(false);
  const [IsActiveAppointment, setIsActiveAppointment] = useState(false);
  const [IsNewAppointment, setIsNewAppointment] = useState(null);
  const [DistinctCitiesMaster, setDistinctCitiesMaster] = useState([]);
  const [ParentId,setParentId]=useState(0);


  let query = useQuery();
  const [UserSelectedAddress, IsSearchBarCall] = useSelector(state => {
    let { UserSelectedAddress, IsSearchBarCall } = state.salesview;
    return [UserSelectedAddress, IsSearchBarCall]
  });
  let IsSrcCustomerWhatsapp = IsSourceCustomerWhatsapp();
  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey: SV_CONFIG && SV_CONFIG.googleMapsApiKey,//process.env.REACT_APP_GOOGLE_MAPS_API_KEY,
    libraries

  });


  const getPersonAppointmentDetails = () => {
    let routeParams = window.location.pathname.toLowerCase().split('/');
    if (!routeParams) {
      return 0;
    }
    if (routeParams.includes('fosappointment')) {
      let _LeadId = query.get("l");
      _LeadId = _LeadId.replace(/ /g, '+');
      let src = query.get("src");
      setCustomerSource(src);
      let productId = parseInt(query.get("p"));
      let token = parseInt(query.get("t"));
      let customerID = query.get("c");
      customerID = customerID.replace(/ /g, '+');

      setProductId(productId)
      setparentLeadId(_LeadId);
      setToken(token);
      setCustomerId(customerID);
      window.localStorage.setItem('CustomerToken', token)
      window.localStorage.setItem('parentLeadId', _LeadId)
      window.localStorage.setItem('EncryptedLeadId', _LeadId)
      window.localStorage.setItem('EncryptedCustomerId', customerID)
      window.localStorage.setItem("CustomerWhatsappURL", window.location.href);

      try {

        gaEventTracker('UTM_Source', 'Hyperlocal_FoS_Whatsapp', _LeadId);
      }
      catch {
        console.log("Error in UTM_Source")
      }


      getBasicLeadDetailsWrapperService(_LeadId).then((response) => {
        if (response.Gender > 0) {
          setNewAddress(prevState => ({ ...prevState, Gender: response.Gender }))
          setIsGenderDisabled(true);
        }
      });

      IsAppointmentCreatedService(_LeadId).then((res) => {
        let _parentId=0;
        if(res!=null && res.Data!=null)
        {
          _parentId=res.Data.LeadId;
          setParentId(_parentId);
        }
     
        if (res!=null && res.Data!=null && res.Data.AppointmentCount>0) {
          setIsNewAppointment(false);
          GetAppointmentDataService(customerID, _LeadId).then(function (response) {

            if (response && response.CustomerId > 0) {

              setSubStatusId(response.subStatusId);
              setStatusId(response.StatusId)
              if (response.subStatusId == 2002 || response.subStatusId == 2005 || response.subStatusId == 2088) {
                setAppointmentBookedSection(true)
              }
              if (response.subStatusId == 2124 || response.subStatusId == 2193 || response.subStatusId == 2194) {

                setAppointmentMessage("Your request for the appointment is already in progress.")
              }
              if ((new Date(response.AppointmentDateTime)) > new Date()) {
                setIsUpcoming(true);
              }
              setAppointmentDateTime(response.AppointmentDateTime);
              setNewAddress(prevState => ({ ...prevState, ...response, place_id: response.location && response.location.place_id }));
              setLoading(false);


            }
            else {
              setNoAppointment(true)

            }
          })
        }
        else {
          setIsNewAppointment(true);
          GetOfflineCitiesService(_LeadId, 0, productId).then((res) => {
            const { DistinctCities, CityMapping } = getDistinctCitiesAppointmentMapping(res);
            setDistinctCitiesMaster(DistinctCities)
            try {

              gaEventTracker('hyperlocalpageviewed', true, _parentId);
            }
            catch {
              console.log("Error in hyperlocal page viewed ")
            }


          }).catch((error) => {

          })
        }
        setLoading(false);
      }).catch((err) => {
        setLoading(false);
      })



      /*  GetCustomerLocationData(customerID, _LeadId).then((response) => {
          if (response.status = true && !!response.Data) {
            console.log('555555', response)
            // setAppointmentPosition({	"lat":28.457523,
            // "lng":77.026344})
  
            setIsCustomerLocationData(true);
            setAppointmentCity(response.Data.City);
            setHouseNo(response.Data.Address);
  
            setNewAddress(prevState => ({ ...prevState, OfflineCityId: response.Data.CityId, CountryId: response.Data.CountryId, OfflineCity: response.Data.City, place_id: response.Data.PlaceId, Gender: response.Data.Gender }));
            setAppointmentPosition({ lat: response.Data.Lat, lng: response.Data.Long })
            setUserSelectedPosition({ lat: response.Data.Lat, lng: response.Data.Long })
          }
          else {
            GetAppointmentDataService(customerID, _LeadId).then(function (response) {
  
              if (response && response.CustomerId > 0) {
  
                setSubStatusId(response.subStatusId);
                setStatusId(response.StatusId)
                if (response.subStatusId == 2002 || response.subStatusId == 2005 || response.subStatusId == 2088) {
                  setAppointmentBookedSection(true)
                }
                if (response.subStatusId == 2124 || response.subStatusId == 2193 || response.subStatusId==2194 ) {
  
                  setAppointmentMessage("Your request for the appointment is already in progress.")
                }
                if ((new Date(response.AppointmentDateTime)) > new Date()) {
                  setIsUpcoming(true);
                }
                setAppointmentDateTime(response.AppointmentDateTime);
                setNewAddress(prevState => ({ ...prevState, ...response, place_id: response.location && response.location.place_id }));
                setLoading(false);
  
  
              }
              else {
                setNoAppointment(true)
  
              }
            }
            ).catch((error) => {
              enqueueSnackbar("Something went wrong, Please try refreshing again. ", {
                variant: 'error',
                autoHideDuration: 3000,
              });
              setLoading(false);
              setIsSomethingwentwrong(true);
            })
          }
          setLoading(false);
  
        })
          .catch((error) => {
            if (error.status == 401) {
              console.log("Error in Reschduling appoitment")
              enqueueSnackbar("Not Authorised or Invalid Token ", {
                variant: 'error',
                autoHideDuration: 3000,
              });
            } else {
              enqueueSnackbar("Something went wrong, Please try refreshing again ", {
                variant: 'error',
                autoHideDuration: 3000,
              });
            }
            setLoading(false);
            setIsSomethingwentwrong(true);
  
          });
  */
    }
  }

  const ConfirmAppointment = () => {
    let reqData = {

      "LeadID": 0,
      "UserID": 4020,
      "SubStatusId": 2088,
      "StatusId": StatusId || 4,
      "EncryptedCustomerId": window.localStorage.getItem("EncryptedCustomerId"),
      "EncryptLeadId": window.localStorage.getItem("EncryptedLeadId"),
      "Source": CustomerSource
    }
    UpdateAppointmentStatusService(reqData).then(() => {
      setShowConfirmbtn(false);
      getPersonAppointmentDetails();
    });


  }

  useEffect(() => {
    if (JSON.stringify(SV_CONFIG) != '{}')
      getPersonAppointmentDetails();
  }, [])
  const OpenFOSPanel = () => {

    window.open('./fosAppointment?src=' + CustomerSource + '&l=' + parentLeadId + '&c=' + CustomerId + '&p=' + productId + '&t=' + token, "_self")
  }


  const DetectMyLocation = () => {
    if ("geolocation" in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setUserSelectedPosition({ lat: position.coords.latitude, lng: position.coords.longitude })
          // const bounds = new window.google.maps.LatLngBounds({ lat: position.coords.latitude, lng: position.coords.longitude });
          // if (Map)
          //   Map.fitBounds(bounds);

        },
        (error) => {
          console.error("Error getting location:", error);
        }
      );
    } else {
      console.error("Geolocation is not available");
    }
  }


  const validateAppointmentData = () => {
    if (!NewAddress) {
      // extra check, if newAddress is a falsey Value
      enqueueSnackbar("All the fields are Mandatory!", { variant: 'error', autoHideDuration: 3000 });
      return
    }
    if (NewAddress && (NewAddress.OfflineCity === "" || !NewAddress.OfflineCity)) {
      enqueueSnackbar("Please select a City to continue", { variant: 'error', autoHideDuration: 3000 });
      return false;
    }
    else if (!NewAddress.AppointmentDateSlot) {
      enqueueSnackbar("Please select a Date Slot for Appointment", { variant: 'error', autoHideDuration: 3000 });
      return false;
    }
    else if (!NewAddress.AppointmentTimeSlot) {
      enqueueSnackbar("Please select a Time Slot for Appointment", { variant: 'error', autoHideDuration: 3000 });
      return false;
    }
    else if (!NewAddress) {
      enqueueSnackbar("Please select a Date and time for Appointment", { variant: 'error', autoHideDuration: 3000 });
      return false;
    }

    if (!UserSelectedAddress || (UserSelectedAddress && !UserSelectedAddress.formatted_address)) {
      enqueueSnackbar("Please map appointment address correctly!", { variant: 'error', autoHideDuration: 3000 });
      return false;
    }
    else if (NewAddress && UserSelectedAddress && Array.isArray(UserSelectedAddress.CityList) && UserSelectedAddress.CityList.length>0 &&UserSelectedAddress.CityList.indexOf(NewAddress.OfflineCityId)==-1) {
      enqueueSnackbar('Oops!! Please reach out to us at 1800-208-8787 if you want to change the city.',
        { variant: 'error', autoHideDuration: 5000, })

      return false;
    }
    else if (!HouseNo) {
      enqueueSnackbar("Please Put your Building No/Flat No.", { variant: 'error', autoHideDuration: 3000 });
      return false;
    }
    else if (HouseNo && HouseNo.trim().length < 10) {
      enqueueSnackbar("Minimum 10 characters are required for Building No/Flat No.", { variant: 'error', autoHideDuration: 3000 });
      return false;
    }
    else if (!NewAddress.Gender) {
      enqueueSnackbar("Please select your gender to continue", { variant: 'error', autoHideDuration: 3000 });
      return false;
    }
    return true;
  }


  const ConfirmLocation = () => {

    let newAddress = NewAddress;

    let _appointmentDateTime = null;
    // let appointmentTypeId_failsafe = -1;
    try {
      _appointmentDateTime = new Date(NewAddress.AppointmentDateSlot.fullDate);
      let hrs = getTimeHour(NewAddress.AppointmentTimeSlot.StartTime);
      // Need to set UTCHours as per backend, otherwise hours are in IST
      _appointmentDateTime.setUTCHours(hrs);
    } catch (e) {
      console.error(e);
    }
    const isValid = validateAppointmentData();

    if (!isValid) {
      return;
    }

    let reqData = {
      "CustomerId": 0,
      "ParentId": 0,
      "UserId": 4020,
      "OfflineCityId": newAddress.OfflineCityId || 0,
      "Pincode": UserSelectedAddress && UserSelectedAddress.Pincode,//newAddress.Pincode || 0,
      "AppointmentType": newAddress.AppointmentType || 4,
      "AssignmentId": newAddress.AssignmentId || 4,
      "ZoneId": newAddress.ZoneId || 0,
      "AppointmentDateTime": _appointmentDateTime,//newAddress.AppointmentDateTime && newAddress.AppointmentDateTime.replace("T", " "),//"2023-10-04 18:00:00.000",// _appointmentDateTime,
      "SlotId": newAddress.AppointmentTimeSlot.SlotId,//newAddress.AppointmentTimeSlot.SlotId,
      "Address": HouseNo,
      "Address1": (newAddress.Address1 && newAddress.Address1.trim()),
      "Landmark": UserSelectedAddress && UserSelectedAddress.formatted_address,//UserSelectedAddress || "",
      "NearBy": (newAddress.NearBy && newAddress.NearBy.trim()) || "",
      "Comments": newAddress.Comments || "",
      "PlanList": newAddress.PlanList || [],
      "subStatusId": newAddress.subStatusId,//IsSrcCustomerWhatsapp ? 2005 : undefined,
      "IncomeId": newAddress.IncomeId,
      "IncomeDocsId": newAddress.IncomeDocsId,
      "EducationId": newAddress.EducationId,
      "Source": CustomerSource,
      "place_id": UserSelectedAddress && UserSelectedAddress.place_id,//newAddress.place_id,
      "Gender": bypassGenderInput(productId) ? 0 : newAddress.Gender,
      "EncryptedLeadId": window.localStorage.getItem('EncryptedLeadId'),
      "EncryptedCustomerId": window.localStorage.getItem('EncryptedCustomerId'),
      "CancelReasonId": 0,
      "IsDropLocationConfirm": 1

    };
    if ([7, 1000].indexOf(productId) > -1) {
      reqData = { ...reqData, productId: productId }
    }
    setIsShowSaveBtn(false);
    try {

      gaEventTracker('ScheduledAppointmentClicked', true, ParentId);
    }
    catch {
      console.log("Error in clicking scheduled appointment")
    }
    // setBookedAppointmentId(null);
    SetAppointmentDataService(reqData).then((response) => {


      if (response != null) {

        if (response && parseInt(response.StatusCode) === 200) {
          setIsShowSaveBtn(true);
          try {

            gaEventTracker('IsAppointmentSuccess', true, ParentId);
          }
          catch {
            console.log("Error in scheduling the appointment");
          }

          window.parent.postMessage({ "action": "SetAppointmentBooked" }, '*');
          window.parent.postMessage({ "action": response.Message }, '*');
          enqueueSnackbar("Appointment Details Saved Successfully", { variant: 'Success', autoHideDuration: 1500, });
          ConfirmAppointment();
          if (IsSrcCustomerWhatsapp === true) {

            if (window.localStorage.getItem('CustomerWhatsappURL')) {
              setTimeout(() => {
                window.open(window.localStorage.getItem('CustomerWhatsappURL'), "_self")
              }, 500);
            }

          }


        }
        else {
          try {

            gaEventTracker('IsAppointmentSuccess', false, ParentId);
          }
          catch {
            console.log("Error in scheduling the appointment");
          }

          setIsShowSaveBtn(true);
          enqueueSnackbar(response.Message, { variant: 'error', autoHideDuration: 3000, });

        }
      }
      else {
        setIsShowSaveBtn(true);
        try {

          gaEventTracker('IsAppointmentSuccess', false, ParentId);
        }
        catch {
          console.log("Error in scheduling the appointment");
        }

        enqueueSnackbar("Something went wrong, Please Refresh page and try again", { variant: 'error', autoHideDuration: 3000, });
      }
    }).catch((err) => {

      setIsShowSaveBtn(true);
      enqueueSnackbar("Something went wrong, please resubmit after few second", { variant: 'error', autoHideDuration: 3000, });

    });



  };

  const handleChange = (event) => {
    const regx = /^[0-9\b]+$/;
    let val = event.target.value;
    switch (event.target.name) {

      case "OfflineCity":
        if (event.target.value) {
          setNewAddress({
            ...NewAddress, OfflineCity: event.target.value, OfflineCityId: event.target.value.CityId,
            AssignmentId: "", ZoneId: "",
            Pincode: "", Landmark: ""
          });
          setUserSelectedPosition({ lat: event.target.value.Lat, lng: event.target.value.Long })
          setAppointmentPosition({ lat: event.target.value.Lat, lng: event.target.value.Long });
          // setOptions([]);
          // setInputLandmarkValue('');
        }
        else {
          setNewAddress({ ...NewAddress, OfflineCity: "", OfflineCityId: 0 });
          setUserSelectedPosition(null)
          setAppointmentPosition(null);
        }

        break;

      case "HouseNo":
        setHouseNo(val)
        break;
      case "AppointmentDateSlot":
        setNewAddress({ ...NewAddress, AppointmentDateSlot: event.target.value, AppointmentTimeSlot: null })
        break

      case 'appointmentTimeSlot':
        setNewAddress({ ...NewAddress, AppointmentTimeSlot: event.target.value })
        break;
      case 'gender':
        if (event.target.value) {
          let Gender = event.target.value;
          setNewAddress({ ...NewAddress, Gender })
        }
        break;
      default: break;
    }
  }

  const GetAppointmentData = (CustomerId, parentLeadId) => {

    GetAppointmentDataService(CustomerId, parentLeadId).then(function (response) {

      if (response && response.CustomerId > 0) {
        setAppointmentDateTime(response.AppointmentDateTime);
        setStatusId(response.StatusId);

        setNewAddress(prevState => ({ ...prevState, ...response, place_id: response.location && response.location.place_id }));

      }
    }).catch((err) => {
      console.log(err)

    });
  }


  return (
    <div>
      {/* {open && <ModalDialogue />} */}
      {Loading && <LinearProgress variant="indeterminate" color="secondary" />}
      <div className="header">
        <span>
          <a href="https://www.policybazaar.com"><img src={CONFIG.PUBLIC_URL + "/images/rescheduled/logo.svg"} alt="PB" /></a>

        </span>
      </div>
      {!Loading && !IsNewAppointment && !AppointmentBookedSection &&

        <div className="inner_box">
          <div className="main-view">
            <div className="main-img"><img src={CONFIG.PUBLIC_URL + "/images/rescheduled/main.png"} alt="banner" /></div>
            {!Loading && IsSomethingwentwrong && <h4 className="details-boxnoappointment">Something went wrong. Please try again later</h4>}
            {!Loading && NoAppointment && AppointmentMessage == "" && !IsCustomerLocationData && !IsSomethingwentwrong && <h4 className="details-boxnoappointment">It seems your request has been completed or canceled, please reach out to us at our Toll-Free Number: <a href="tel:1800-208-8787">1800-208-8787</a> </h4>}
            {!Loading && AppointmentMessage !== "" && !IsSomethingwentwrong && <h4 className="details-boxnoappointment">{AppointmentMessage}</h4>}


          </div>

        </div>}
      {!Loading && !IsSomethingwentwrong && IsNewAppointment ? (<div className="appointment-rescheduleCustomer">


        {DateOpen && <RescheduledAppointmentPopup
          open={DateOpen}
          className='PlayPause'
          handleClose={() => {
            setDateOpen(false);
          }}
          parentLeadId={parentLeadId}
          NewAddress={NewAddress}
          handleChange={handleChange}
          appointmentDateTime={appointmentDateTime}
          setNewAddress={setNewAddress}
          show={true}
          IsLeadRenewal={true}
          HyperLocalRenewal={true}
        />}

        <div className="DropLocation-inner_box">
          <div className="details-box">
            <Grid container spacing={2}>
              <div className="desktopView">

                <h1>Hi,</h1>
                <p>Please provide the following details to schedule an appointment with our insurance expert</p>
              </div>
              <Grid container spacing={3}>
                <Grid item md={6} sm={6} xs={12} >
                  <FormLabel component="p" className="Label" >
                    {/* required={isGenderMandatory()}> */}
                    Select Your Gender
                  </FormLabel>
                  <div className="GenderAlignment">
                    {(gendersMaster.map((gender) => (

                      <FormControlLabel
                        key={gender.value}
                        value={gender.value}
                        control={<Radio color="secondary" />}
                        label={gender.label}
                        onChange={handleChange}
                        checked={NewAddress.Gender == gender.value}
                        name="gender"
                        disabled={IsGenderDisabled}
                        className={NewAddress.Gender == gender.value ? "Active" : ""}
                      />
                    )))}
                  </div>

                </Grid>
                <Grid item sm={6} md={6} xs={12} >
                  <FormLabel component="p" className="Label margintop" >
                    {/* required={isGenderMandatory()}> */}
                    Select City
                  </FormLabel>
                  <Autocomplete
                    onChange={(event, value) => handleChange({ target: { name: 'OfflineCity', value } })}
                    id="OfflineCity"
                    //  options={UAEUser ? DistinctCitiesMaster.filter(item => item.CountryId === NewAddress.CountryId) : DistinctCitiesMaster.filter(item => item.CountryId === CountryAsIndia)}
                    options={DistinctCitiesMaster.filter(item => item.CountryId === 392)}
                    name="OfflineCity"
                    value={NewAddress.OfflineCity || null}
                    getOptionLabel={(option) => (option.CityName) || ''}
                    className="cityBox"
                    // disabled={disabled || IsEditAppDetails}

                    renderInput={(params) =>
                      <TextField {...params}
                        label="Select City*"
                        variant='outlined' />}

                  />
                </Grid>
                <Grid item md={6} sm={6} xs={12}>
                  <FormLabel component="p" className="Label mt-0" >
                    Select date & time
                  </FormLabel>
                  <div onClick={() => {
                    setDateOpen(true)
                  }} className="DateTimeBox">
                    <p><DateRangeIcon /> {NewAddress.AppointmentDateSlot ? NewAddress.AppointmentDateSlot.fullDate : 'Date'}</p><hr />
                    <p><AccessTimeIcon /> {NewAddress.AppointmentTimeSlot ? timeConvert(NewAddress.AppointmentTimeSlot.StartTime) + '-' + timeConvert(NewAddress.AppointmentTimeSlot.EndTime) : 'Time'}</p>
                  </div>

                </Grid>

              </Grid>
              <div className="mobileview">
                {/* <div className="heading"> <span>LOCATION DETAILS</span>
                </div> */}
                <h1>Hi, </h1>
                <p>Please help us locate you better
                  (provide your location or confirm if already provided)</p>
              </div>
              {/* <img src={CONFIG.PUBLIC_URL + "/images/rescheduled/map.jpg"} className="mapImage" /> */}
              <p className="Label">Appointment Address</p>
              {
                isLoaded && AppointmentPosition && <AppointmentPositionMap
                  UserSelectedPosition={UserSelectedPosition ? UserSelectedPosition : AppointmentPosition}
                  setUserSelectedPosition={setUserSelectedPosition}
                  Map={Map}
                  setMap={setMap}
                  isLoaded={isLoaded}
                  NewAddress={NewAddress}

                >

                </AppointmentPositionMap>
              }


              <Grid container>
                <Grid item md={12} sm={12} xs={12}> {AppointmentCity && <p className="CityName">Appointment City : <b>{AppointmentCity}</b></p>}</Grid>
                <div className="SearchLocation">
                  <Grid container spacing={3}>
                    <Grid item md={9} sm={8} xs={8}>
                      {isLoaded && <SearchMap
                        UserSelectedPosition={UserSelectedPosition}
                        setUserSelectedPosition={setUserSelectedPosition}
                        isLoaded={isLoaded}
                      >
                      </SearchMap>
                      }

                    </Grid>
                    <Grid item md={3} sm={4} xs={4}>
                      {/* <img src={CONFIG.PUBLIC_URL + "/images/rescheduled/search-interface.svg"} /> */}
                      <Button className="DetectLocationBtn" color="secondary" variant="contained" onClick={() => { DetectMyLocation() }}> <img src={CONFIG.PUBLIC_URL + "/images/rescheduled/gpsBlue.svg"} /> Detect My Location</Button>

                    </Grid>
                  </Grid>
                </div>
                <div className="MobileSearchLocation">
                  <Grid container spacing={3}>
                    <Grid item md={12} sm={12} xs={12}>
                      {isLoaded && <SearchMap
                        UserSelectedPosition={UserSelectedPosition}
                        setUserSelectedPosition={setUserSelectedPosition}
                        isLoaded={isLoaded}
                      >
                      </SearchMap>
                      }
                      <Button className="DetectLocationBtn" color="secondary" variant="contained" onClick={() => { DetectMyLocation() }}> <img src={CONFIG.PUBLIC_URL + "/images/rescheduled/gpsBlue.svg"} /> Detect My Location</Button>
                    </Grid>

                  </Grid>
                </div>
                <TextInput
                  name="HouseNo"
                  label='House/Flat No, Building No, Block/Society'
                  handleChange={handleChange}
                  value={HouseNo}
                  sm={12} md={12} xs={12}

                />
                <Button className={!IsShowSaveBtn ? "disable sendLocationBtn" : "sendLocationBtn"} color="secondary" variant="contained" disabled={!IsShowSaveBtn} onClick={() => { ConfirmLocation() }}>Schedule Appointment</Button>

              </Grid>
            </Grid>
          </div>

        </div>


      </div>) :
        !Loading && AppointmentBookedSection && !IsSomethingwentwrong && AppointmentMessage == "" && <AppointmentBooked NewAddress={NewAddress} />
      }

    </div >

  );
};

export default AppointmentHyperLocalRenewal;
