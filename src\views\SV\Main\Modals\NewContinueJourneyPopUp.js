import React, { useEffect, useState } from "react";
import ModalPopup from '../../../../components/Dialogs/ModalPopup';
import { SV_CONFIG } from "../../../../appconfig";
import rootScopeService from "../../../../services/rootScopeService";
import User from "../../../../services/user.service";
import { IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useSelector } from "react-redux";
import { IsUnfreeze, GetPaymentAttemptStatus } from "../../../../services/Common";
import { useSnackbar } from "notistack";

export const NewContinueJourneyPopUp = (props) => {
    const { enqueueSnackbar } = useSnackbar();
    const [allLeads] = useSelector(state => {
        let { allLeads } = state.salesview;
        return [allLeads]
    });
    const IsPaymentStatusShowforCJ = SV_CONFIG.IsPaymentStatusShowforCJ
    let visibleLeads = props.lead;
    const [PaymentSuccessLeadID, setPaymentSuccessLeadID] = useState(undefined);
    const [DisplayPrompt, setDisplayPrompt] = useState(false);
    const [ShowCJ, setShowCJ] = useState(false);
    const [IsUnfreezeVisible, setIsUnfreezeVisible] = useState(false);

    const UnfreezeVisible = () => {
        var RoleId = 0;
        if (User.RoleId != null && User.RoleId != "" && User.RoleId != "0") {
            RoleId = User.RoleId;
        }
        if ([2, 130].includes(visibleLeads.ProductID) && RoleId != 13) {
            return true;
        }
        else {
            setShowCJ(true);
        }
    }

    useEffect(() => {
        if (ShowCJ === true && props.open === true) {
            openCJ();
        }
    }, [ShowCJ]);

    useEffect(() => {
        if (props.open === true) {
            setIsUnfreezeVisible(UnfreezeVisible());
        }
    }, [props.open]);

    const IsUnfreezeClick = () => {
        IsUnfreeze(visibleLeads.LeadID).then((result) => {
            if (result && result.isSuccess) {
                alert("Unfreeze Successful");
            }
            else {
                alert("Unable to Unfreeze");
            }
        }).catch((e) => {
            console.log(e);
        })
    }

    const GetPaymentStatusforLeads = () => {
        var ActiveLeadSetArray = [];
        for (let key in allLeads) {
            ActiveLeadSetArray.push(allLeads[key].LeadID)
        }

        if (ActiveLeadSetArray.length > 0) {
            let leadIds = ActiveLeadSetArray.toString();
            var requestData = {
                "LeadIds": leadIds,
            };
            GetPaymentAttemptStatus(requestData).then(function (result) {
                if (Array.isArray(result.data) && result.data.length > 0) {
                    let SuccessLeadIDs = [];
                    for (let key in result.data) {
                        if ((result.data[key].TransactionStatus) && (result.data[key].TransactionStatus == "Success")) {
                            SuccessLeadIDs.push(result.data[key].LeadID);
                        }
                    }

                    setPaymentSuccessLeadID(SuccessLeadIDs.toString())
                    let item = result.data.find(item => item.LeadID === rootScopeService.getLeadId());
                    if (item) {
                        if (item.TransactionStatus && item.TransactionStatus.length > 0) {
                            if (item.TransactionStatus === 'Success') {
                                OpenCJlink()
                            }
                            else {
                                setDisplayPrompt(true);
                            }
                        }
                    }
                    else {
                        setDisplayPrompt(true);
                    }
                }
                else {
                    OpenCJlink()
                }

            });
        }
    }

    const openCJ = () => {
        if (visibleLeads) {
            if (visibleLeads.ContinueJourneyURL != "" && visibleLeads.LeadSource.toLowerCase() !== "renewal" && IsPaymentStatusShowforCJ == true)
                GetPaymentStatusforLeads()
            else {
                OpenCJlink();
            }
        }
    }

    const OpenCJlink = () => {
        setDisplayPrompt(false);
        if (visibleLeads.ContinueJourneyURL != '')
            window.open(visibleLeads.ContinueJourneyURL, "_blank");
        else {
            if(visibleLeads.ProductID == 130){
                window.open("https://health.policybazaar.com/?stu=1", "_blank");
            }
            else{
            enqueueSnackbar("No Continue Journey Link", { variant: 'success', autoHideDuration: 3000, });
            }
        }
        props.handleClose();
        setShowCJ(false);
    }
    const ClosePopup = () => {
        setDisplayPrompt(false);
    }



    return (
        <>
            {IsUnfreezeVisible == true && <>
                <ModalPopup className="newcontinueJourneyPopup" open={props.open} handleClose={props.handleClose} title="Choose option">
                    <div className="popupWrapper">
                        <IconButton onClick={props.handleClose}>
                            <CloseIcon />
                        </IconButton>
                        <div>
                            <button className="continue-journey" onClick={() => openCJ()}> Continue Journey</button>

                            <button className="continue-journey" onClick={() => IsUnfreezeClick()} >  Unfreeze CJ URL</button>
                        </div>
                    </div >
                </ModalPopup >
            </>}
            {DisplayPrompt == true && <>
                <div class="overlay" id="pnlPopup12" style="display: none;">
                    <div class="popup">
                        <p style=" font: normal normal normal 15px/23px Roboto; margin-bottom: 20px">Are you sure you want to proceed with this Lead ID? We have received the Payment confirmation on LeadID - {{ PaymentSuccessLeadID }}</p>
                        <div class="text-right">
                            <button onClick={OpenCJlink()} class="btn  YesBtn">Yes, Proceed</button>
                            <button onClick={ClosePopup()} class="btn okBTN">Cancel</button>
                        </div>
                    </div>
                </div>
            </>}
        </>

    )
}
