import React, { useEffect, useState } from "react";
import Textarea from '@mui/material/TextareaAutosize';
import { Grid, Tooltip } from "@mui/material";
import { CALL_API } from '../../../services/api.service';
import { useDispatch, useSelector } from "react-redux";
import rootScopeService from "../../../services/rootScopeService";
import { useSnackbar } from 'notistack';
import User from "../../../services/user.service";
// import ExpandMore from '@mui/icons-material/ExpandMore';
import { ViewAllCommentsPopup } from "./Modals/ViewAllCommentsPopup";
import { updateStateInRedux } from "../../../store/actions/SalesView/SalesView";
import Common, { GetCustomerComment, SetCustomerComment } from "../../../services/Common";
import dayjs from "dayjs";
// import { ExpandMore } from "@mui/icons-material";
import { CONFIG } from "../../../appconfig";
import { gaEventTracker } from "../../../helpers";
const uuid = require("uuid");

export const saveCommentsService = (requestData) => {
    const input = {
        url: 'LeadDetails/SetCustomerComment', method: 'POST', service: 'core',
        requestData
    };
    return CALL_API(input);
}

export default function Comments(props) {
    //let [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);
    const [RefreshCustomerId] = useSelector(({ salesview }) => [salesview.RefreshCustomerId]);
    const { maxComments = 4 } = props;
    const [OpenViewAllComments, setOpenViewAllComments] = useState(false);
    const [commentInput, setCommentInput] = useState('');
    const [comments, setComments] = useState([]);
    // const [show, setShow] = useState(true);
    const { enqueueSnackbar } = useSnackbar();
    const [ParentLeadId, PrimaryLeadId, LeadIds] = useSelector(state => {
        let { parentLeadId, primaryLeadId, leadIds } = state.salesview;
        return [parentLeadId, primaryLeadId, leadIds];
    });
    const ProductId = rootScopeService.getProductId();
    const CustomerId = rootScopeService.getCustomerId();
    const [RefreshComments, setRefreshComments] = useState(false);
    const [IsLoading, setIsLoading] = useState(false);
    const dispatch = useDispatch();
    const [SubproductTypeId, setSubproductTypeId] =  useState(0);
    const HighIntentFlag = useSelector((state)=> state.salesview.HighIntentLead) || null
    const resetInput = () => {
        setCommentInput('');
    }


    const getComments = () => {
        setComments([]);
        setIsLoading(true)
        if (!(LeadIds && ParentLeadId)) {
            return;  // hit api only after LeadIds is fetched from redux store
        }
        if (!LeadIds.includes(rootScopeService.getLeadId())) {
            return;
        }

        var requestData = { CustomerId, ProductId, "Leads": LeadIds };

        GetCustomerComment(ParentLeadId, requestData).then((result) => {
            if (result) {
                let processedcomments = result;
                if(ProductId == 131 && result.length!=0)
                {
                    setSubproductTypeId(result[0]?.SubProductTypeId);
                }
                let processedcommentsv1 = processedcomments.map(obj => ({ ...obj, UniqueId: uuid.v1().toUpperCase() }));
                if (LeadIds.includes(rootScopeService.getLeadId())) {
                    setComments(processedcommentsv1);
                }
                else {
                    gaEventTracker('COMMENTS_FIX');
                    if (Array.isArray(comments) && comments.length > 0) {
                        if (!LeadIds.includes(comments[0].LeadID)) {
                            setComments([]);
                            gaEventTracker('COMMENTS_RESET');
                        }
                    }
                }
                setRefreshComments(false);
            }
            else {
                setComments([]);
                setRefreshComments(false);
            }
            setIsLoading(false)
        }).catch((err) => {
            setRefreshComments(true);
            setIsLoading(false)
        });
    }
    let viewAllComments = [];
    var CJ_Comments = [];
    //SME - Group Health Insurance
    if(ProductId == 131 && SubproductTypeId == 1 && comments && comments.length!=0)
    {
        CJ_Comments = comments.filter(comment => comment.EventType === "68")[0];
        if(!HighIntentFlag && CJ_Comments && CJ_Comments.SubProductTypeId==1)
        {
            dispatch(updateStateInRedux({ key: "HighIntentLead", value: CJ_Comments.LeadID}));
        }
    }
    if (ProductId == 131 && SubproductTypeId == 1 && CJ_Comments && CJ_Comments.length != 0) {
        const otherComments = comments.filter(comment => comment.UniqueId !== CJ_Comments.UniqueId);
        var sortedComments = [];
        sortedComments = [CJ_Comments, ...otherComments];
        viewAllComments = sortedComments
            .map((commentData, index) => {
                return <div key={commentData.UniqueId} className={index === 0 && commentData.EventType == '68' ? 'comment-text HighLightComment' : 'comment-text'}>
                    <p>{commentData.CreatedBy}{commentData.LeadID ? ' - ' + commentData.LeadID : ''}</p>
                    <small>{dayjs(commentData.CreatedOn).format('DD/MM/YYYY h:mm:ss a')}</small>
                    <p className="commentmsg">{commentData.Comments}</p>
                </div>
            });
    }
    else {
        viewAllComments = comments
            .map((commentData, index) => {
                return <div key={commentData.UniqueId} className="comment-text">
                    <p>{commentData.CreatedBy}{commentData.LeadID ? ' - ' + commentData.LeadID : ''}</p>
                    <small>{dayjs(commentData.CreatedOn).format('DD/MM/YYYY h:mm:ss a')}</small>
                    <p className="commentmsg">{commentData.Comments}</p>
                </div>
            });
    }
    
    let commentList = viewAllComments.slice(0, maxComments);

    const handleChange = (event) => {
        setCommentInput(event.target.value);
    }

    const SaveComments = () => {
        let UserId = User.UserId;
        const requestData = {
            CustomerId, ProductId, ParentLeadId, PrimaryLeadId, UserId,
            "Comment": commentInput, "EventType": 7
        }
        SetCustomerComment(requestData).then((result) => {
            //saveCommentsService(requestData).then((result) => {
            //if (result.SetCustomerCommentResult.Data) {
            if (result) {
                dispatch(updateStateInRedux({ key: 'CommentsAdded', value: true }));
                enqueueSnackbar("Save successfully", {
                    variant: 'success',
                    autoHideDuration: 3000,
                });
                getComments();
                resetInput();
                let reqData = {
                    parentID: ParentLeadId,
                    UserId: User.UserId,
                    action: 4
                };
                Common.SetNotificationAction(reqData);
            }
        });
    }

    const fnOpenAllComments = () => {
        setOpenViewAllComments(true);
    }

    useEffect(() => {
        let CurrentCustomerId = rootScopeService.getCustomerId();
        if (RefreshCustomerId !== CurrentCustomerId) {
            // getComments();
            setComments([]);
        }
    }, [RefreshCustomerId]);
    useEffect(() => {
        getComments();
        resetInput();
    }, [LeadIds]);

    // const handleToggle = (e) => {
    //     if (!show) getComments();
    //     setShow(!show);
    // }
    return (
        <Grid item sm={12} md={12} xs={12}>
            <div className={`commentbox ${props.className}`}>
                <h3>Comments</h3>
                <Tooltip title="View All Comments">
                    <div className="viewAllIcon" onClick={fnOpenAllComments}> <img alt="view All" src={CONFIG.PUBLIC_URL + "/images/viewAll.png"} /></div>
                </Tooltip>
                {/* <div className="expandmoreIcon"> <ExpandMore onClick={handleToggle} style={{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)' }} /></div> */}
                {/* {show && <> */}

                <Textarea onChange={handleChange} minRows={2} placeholder="Enter Comments here…" value={commentInput} />
                <button onClick={SaveComments} className="textareaBtn" >Save</button>
                {RefreshComments && <button onClick={getComments} className="refreshBtn">Click here to refresh Comments
                    {IsLoading && <i className="nc-icon nc-refresh-69" />} </button>}
                <div className="scrollBar">
                    {commentList}
                </div>
                <button onClick={fnOpenAllComments}>View All</button>
                {/*</> } */}
            </div>

            <ViewAllCommentsPopup open={OpenViewAllComments} handleClose={() => { setOpenViewAllComments(false) }} viewAllComments={viewAllComments} />
        </Grid>

    )
}
