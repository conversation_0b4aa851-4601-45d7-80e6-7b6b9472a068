var AgentCall = { url: "", popwin: null, options: "left=100,top=1,width=550px,height=640px,menubar=0,fullscreen=0,location=0,titlebar=0,toolbar=0,status=0,scrollbars=0;dialog=1;" }; function ShowCallWindowAswat(empId) {
    try {
        var user = window.atob(window.localStorage.getItem('User')); user = JSON.parse(user); if (empId == 0) { return; }
        if (!["ASWAT", "ASWATINDIA"].includes(user.CallingCompany)) { return; }

        AgentCall.url = "../libs/aswat/basic-integration/index.htm?agentid=" + empId;
        if (user.CallingCompany === "ASWATINDIA") {
            AgentCall.url += "&india=true"
        }
        var CallWindow = window.localStorage.getItem('CallWindow')
        if (CallWindow == "" || CallWindow == null || CallWindow == undefined) {
            AgentCall.popwin = window.open(AgentCall.url, "callbar", AgentCall.options);
            // try {
            //     AgentCall.popwin.focus()
            // } catch { }
        }
        else {
            var CallWindowTime = (new Date() - new Date(CallWindow)) / 1000;
            if ((CallWindowTime > 3 && !AgentCall.popwin) || ((AgentCall.popwin && AgentCall.popwin.closed))) {
                AgentCall.popwin = window.open(AgentCall.url, "callbar", AgentCall.options);
                // try {
                //     AgentCall.popwin.focus()
                // } catch { }
            }
            else if (AgentCall.popwin && AgentCall.popwin.closed == false) {
                //console.log("pop opened");
            }
            else { AgentCall.popwin = window.open("", "callbar", AgentCall.options); }
        }

    }
    catch (e) { }
}
function setCall() { }
setInterval(function () { var ASWATCALLDATA = JSON.parse(localStorage.getItem("ASWATCALLDATA")); if (AgentCall.popwin && ASWATCALLDATA != null && !ASWATCALLDATA.InCall) { } }, 1000); function ConnectCall(empId, leadId, data) {
    data.empId = empId; var user = window.atob(window.localStorage.getItem('User')); user = JSON.parse(user); if (user.CallingCompany != "ASWAT") { return; }
    data.CallInitTime = new Date(); data.LeadID = leadId; data.SF = data._mobileNo; localStorage.setItem("ASWATCALLDATA", JSON.stringify(data));
}
function toggleMicrophone() {
    try { AgentCall.popwin.toggleMicrophone(); }
    catch (e) { }
}
function CallMute() {
    try { AgentCall.popwin.sipToggleMute(); }
    catch (e) { }
}
function CallHold() {
    try { AgentCall.popwin.sipToggleHoldResume(); }
    catch (e) { }
}
function CallHangup() {
    try { AgentCall.popwin.sipHangUp(); }
    catch (e) { }
}
function setHangup() { }
function logwindow(e) { console.log(e) }
function logoutCallWindowAswat(e) {
    try { AgentCall.popwin.close(); setTimeout(function () { }, 500); }
    catch (e) { }
}
function checkforRegistraion() {
    try {
        if (AgentCall.popwin.checkforRegistraion() == undefined) { return null; }
        else if (AgentCall.popwin.checkforRegistraion()) { return true; }
        return false;
    }
    catch (e) { return null; }
}
// function hideCallWindow() { $(document).focus(); }
function IsWindowOpen(val) { sessionStorage.setItem("AgentCall", val); setCookie("AgentCall", val, 1); if (val == 0) { AgentCall.popwin = null; } }
function setCookie(cname, cvalue, exdays) { var d = new Date(); d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000)); var expires = "expires=" + d.toUTCString(); document.cookie = cname + "=" + cvalue + ";" + expires + ";domain=.policybazaar.com;path=/"; }
function getCookie(cname) {
    var name = cname + "="; var decodedCookie = decodeURIComponent(document.cookie); var ca = decodedCookie.split(';'); for (var i = 0; i < ca.length; i++) {
        var c = ca[i]; while (c.charAt(0) == ' ') { c = c.substring(1); }
        if (c.indexOf(name) == 0) { return c.substring(name.length, c.length); }
    }
    return "";
}

// function bindCallFrame(empId) { ShowCallWindow(empId); }
function ShowCallWindow(empId) { ShowCallWindowAswat(empId) }
// function disableF5(e) { if ((e.which || e.keyCode) == 116) e.preventDefault(); }; function checkChild() {
//     try {
//         if (AgentCall.popwin && AgentCall.popwin.closed) {
//             IsWindowOpen(0); AgentCall.popwin = null
//             // clearInterval(timer);
//         }
//     } catch (e) { }
// }
function reloginAgent() { alert("Someone else is used your id to login. Please relogin again."); }
document.addEventListener('DOMContentLoaded', function () {
    if (!Notification) { alert('Desktop notifications not available in your browser. Try Chromium.'); return; }
    if (Notification.permission !== "granted")
        Notification.requestPermission();
}); setInterval(function () { var notifyMe = localStorage.getItem("notifyMe"); if (notifyMe == 1) { fnnotifyMe(localStorage.getItem("notifyMeMsg")); } }, 1000); function fnnotifyMe(msg) {
    if (Notification.permission !== "granted")
        Notification.requestPermission(); else { window.localStorage.setItem("notifyMe", 0); window.localStorage.setItem("notifyMeMsg", ""); var notification = new Notification('Notification title', { body: msg, }); }
}
function IsMicrophoneEnabled() {
    return window.AgentCall
        && window.AgentCall.popwin
        && window.AgentCall.popwin.CheckMicroPhone
        && window.AgentCall.popwin.CheckMicroPhone();

}