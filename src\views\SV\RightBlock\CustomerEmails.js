import { <PERSON><PERSON>, Grid } from "@mui/material";
import { ExpandMore } from "@mui/icons-material";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { SV_CONFIG } from "../../../appconfig";
import { ErrorBoundary } from "../../../hoc";
import { CALL_API } from "../../../services";
import rootScopeService from "../../../services/rootScopeService";
import User from "../../../services/user.service";
import { JsonToNormalDate } from "../../../utils/utility";
import { ReadEmailModal } from "./Modals/ReadEmailModal";
import { ViewAllEmailsPopup } from "./Modals/ViewAllEmailsPopup";
import { gaEventNames, gaEventTracker } from "../../../helpers";

const CustomerEmails = () => {
    let [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);

    const [show, setShow] = useState(false);
    const [emails, setEmails] = useState([]);
    const [id, setId] = useState(null);
    const [viewAllEmails, setViewAllEmails] = useState(false);
    const ReadEmail = function (id) {
        setId(id);
    }
    const getEmails = function () {
        const CustomerId = rootScopeService.getCustomerId();
        const input = {
            url: SV_CONFIG["Emails"][SV_CONFIG["environment"]] + 'IncomingNotifications?UserID=' + User.UserId + '&CustID=' + CustomerId,
            method: 'GET',
            service: "custom"
        }
        CALL_API(input).then(function (resultData) {
            setEmails(resultData);
        }, function () {
            setEmails([]);
        });
    }
    const handleToggle = () => {
        if (!show) {
            getEmails();
            gaEventTracker(gaEventNames.CustomerEmails, User.EmployeeId, rootScopeService.getLeadId());
        }
        setShow(!show);
    }

    useEffect(() => {
        if (RefreshLead) {
            setShow(false);
        }
    }, [RefreshLead]);

    let EmailList = (Array.isArray(emails) && emails.length > 0)
        ? emails.map((email, index) => (
            <div key={index} className="cursorPointer" onClick={() => { ReadEmail(email.CommID) }}>
                <p className="userName"> {email.Name}</p>
                <p className="time"> {dayjs(JsonToNormalDate(email.CreatedOn)).format('M/D/YYYY h:mm A')}</p>
                <p className="commentMsg">{email.Subject}</p>
            </div>
        ))
        : <span>No Emails</span>;
    return <>
        <Grid item sm={12} md={12} xs={12}>
            <div className="customerEmails">
                <h3>Customer Emails {show && `(${emails.length})`} </h3>

                <div className="expandmoreIcon">
                    <ExpandMore onClick={handleToggle} style={{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)' }} /></div>
                {show &&
                    <>
                        {(Array.isArray(emails) && emails.length > 0)
                            ? <>
                                {EmailList.slice(0, 3)}
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    // size="large"
                                    onClick={() => { setViewAllEmails(true) }}
                                    className=""
                                    fullWidth
                                >
                                    View all
                                </Button>
                            </>
                            : <span>No Emails</span>
                        }
                    </>
                }
            </div>
        </Grid>
        <ErrorBoundary name="ReadEmailModal">
            <ReadEmailModal open={id !== null} id={id} handleClose={() => { setId(null) }} />
        </ErrorBoundary>

        <ErrorBoundary name="ViewAllEmailsPopup">
            <ViewAllEmailsPopup
                open={viewAllEmails}
                allEmails={EmailList}
                handleClose={() => { setViewAllEmails(false) }}
            />
        </ErrorBoundary>
    </>
}
export default CustomerEmails;
