<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="527" height="202" viewBox="0 0 527 202">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_27552" data-name="Rectangle 27552" width="210" height="151" transform="translate(0.413 0.057)" fill="none"/>
    </clipPath>
  </defs>
  <g id="Aviva" transform="translate(-1049.289 -1980.965)">
    <rect id="Rectangle_27553" data-name="Rectangle 27553" width="527" height="202" transform="translate(1049.289 1980.965)" fill="#ffdd16"/>
    <g id="Group_10181" data-name="Group 10181" transform="translate(1203.078 1984.908)">
      <g id="Group_10180" data-name="Group 10180" transform="translate(4.798 0)">
        <g id="Group_10179" data-name="Group 10179" transform="translate(0)" clip-path="url(#clip-path)">
          <path id="Path_105894" data-name="Path 105894" d="M373.342,126.209v-.961q0-39.413,0-78.827c0-1.3,0-1.3,1.256-1.3l56.25-.008c1.42,0,1.372,0,1.227,1.374-.283,2.716-.493,5.442-.757,8.161-.261,2.689-.561,5.374-.823,8.063s-.49,5.384-.75,8.074c-.263,2.718-.552,5.432-.818,8.152-.2,2.054-.381,4.109-.579,6.163-.266,2.747-.544,5.494-.81,8.241-.2,2.054-.386,4.109-.587,6.162-.272,2.776-.558,5.551-.826,8.327q-.221,2.3-.4,4.605c-.05.644-.113,1.1-.98,1.221-5.064.7-10.138,1.358-15.171,2.246a161.657,161.657,0,0,0-35.323,10.086c-.187.079-.376.156-.569.219a1.433,1.433,0,0,1-.337,0" transform="translate(-307.858 -37.123)" fill="#176fc1"/>
          <path id="Path_105895" data-name="Path 105895" d="M88.124,642.606H86.761c-2.566,0-5.132-.018-7.7.013a1.033,1.033,0,0,1-1.158-.773c-1.108-2.91-2.286-5.794-3.393-8.705a1.157,1.157,0,0,0-1.285-.865q-7.3.044-14.608,0a1.219,1.219,0,0,0-1.305.864c-1.267,2.909-2.592,5.794-3.871,8.7a1.087,1.087,0,0,1-1.169.785c-1.953-.048-3.907-.016-5.86-.019-.223,0-.446-.03-.8-.055.646-1.461,1.25-2.838,1.863-4.211q6.287-14.078,12.579-28.155c.805-1.8.37-2.855-1.475-3.553a3.236,3.236,0,0,1-.817-.284.846.846,0,0,1-.263-.646c.01-.142.272-.307.453-.382a1.413,1.413,0,0,1,.519-.015c4.753,0,9.506.01,14.258-.013a1.089,1.089,0,0,1,1.233.789q6.911,17.825,13.868,35.634c.092.237.16.482.294.892m-21.5-29.529-.3-.006-6.6,14.733H72.42l-5.791-14.727" transform="translate(-37.473 -499.28)" fill="#176fc1"/>
          <path id="Path_105896" data-name="Path 105896" d="M910.717,642.635c.735-1.66,1.408-3.2,2.092-4.727q6.058-13.559,12.12-27.117c.06-.131.12-.265.178-.4.926-2.12.512-3.053-1.709-3.838-.389-.138-.89-.256-.673-.785a1.1,1.1,0,0,1,.86-.421c4.811-.026,9.622-.01,14.433-.03a.942.942,0,0,1,1.035.693q6.963,17.9,13.948,35.792c.081.209.127.432.224.772-.378.022-.681.054-.984.054-2.654,0-5.307-.022-7.96.019a1.131,1.131,0,0,1-1.278-.856c-1.078-2.859-2.234-5.69-3.322-8.545a1.212,1.212,0,0,0-1.319-.95q-7.3.056-14.608,0a1.223,1.223,0,0,0-1.309.86c-1.223,2.8-2.512,5.576-3.719,8.384a1.511,1.511,0,0,1-1.675,1.114c-2.059-.088-4.122-.027-6.334-.027m14.136-14.788h12.68l-5.791-14.785-.274.013-6.615,14.773" transform="translate(-751.211 -499.309)" fill="#176fc1"/>
          <path id="Path_105897" data-name="Path 105897" d="M320.643,605.286c-.793,1.928-1.493,3.649-2.207,5.363q-6.465,15.513-12.918,31.029a1.273,1.273,0,0,1-1.415.928q-4.2-.068-8.395,0a1.25,1.25,0,0,1-1.4-.938c-4.17-10.586-8.389-21.154-12.55-31.743a5.318,5.318,0,0,0-3.74-3.461c-.41-.11-.892-.227-.678-.759.088-.219.565-.4.866-.4,3.964-.026,7.929,0,11.893-.034a1.11,1.11,0,0,1,1.219.824q4.994,12.669,10.022,25.323c.168.424.369.832.637,1.432.269-.559.47-.933.633-1.323q5.269-12.655,10.514-25.321a1.26,1.26,0,0,1,1.411-.937c1.689.057,3.381.018,5.072.018Z" transform="translate(-228.611 -499.263)" fill="#176fc1"/>
          <path id="Path_105898" data-name="Path 105898" d="M693.063,632.833c.219-.433.365-.676.473-.934q5.354-12.856,10.684-25.723a1.263,1.263,0,0,1,1.42-.92c1.977.052,3.955.018,6.089.018-.446,1.093-.842,2.078-1.25,3.059q-6.934,16.643-13.855,33.291a1.272,1.272,0,0,1-1.387.973c-2.827-.05-5.655-.044-8.483,0a1.2,1.2,0,0,1-1.335-.9c-4.185-10.613-8.414-21.207-12.591-31.823a5.277,5.277,0,0,0-3.682-3.406c-.372-.1-.849-.112-.78-.725.075-.667.583-.455.941-.456,3.965-.013,7.929.009,11.894-.024a1.107,1.107,0,0,1,1.207.84q5.061,12.83,10.16,25.645c.116.294.26.576.494,1.089" transform="translate(-551.259 -499.252)" fill="#176fc1"/>
          <path id="Path_105899" data-name="Path 105899" d="M570.719,624.054c0,5.832-.016,11.664.018,17.5,0,.83-.206,1.106-1.067,1.087q-3.8-.083-7.609,0c-.825.017-1.023-.245-1.022-1.041q.038-15.615.017-31.231c0-2.112-.523-2.878-2.512-3.673a2.106,2.106,0,0,1-.643-.254,3.985,3.985,0,0,1-.519-.736c.219-.129.432-.354.657-.37.7-.049,1.4-.018,2.1-.018,3.207,0,6.415.025,9.622-.018.789-.01.978.247.974,1-.028,5.919-.015,11.839-.015,17.759" transform="translate(-459.698 -499.291)" fill="#176fc1"/>
          <path id="Path_105900" data-name="Path 105900" d="M165.73,649.83" transform="translate(-136.574 -536.033)" fill="#fff"/>
        </g>
      </g>
      <path id="Path_105904" data-name="Path 105904" d="M3557.037,465.258h22.975v66.954l-29.839,1.523Z" transform="translate(-3428.482 -457.242)" fill="#59c247"/>
      <path id="Path_105905" data-name="Path 105905" d="M3561.156,464.294l6.463,67.886-13.383.826Z" transform="translate(-3432.629 -456.375)" fill="#ffdd16"/>
      <path id="Path_105906" data-name="Path 105906" d="M23.1-226.724V-201H35.463v-2.731h-9v-22.992ZM36.828-201h3.357v-20.222H36.828Zm1.736-23.508a2.238,2.238,0,0,0,1.585-.682,2.238,2.238,0,0,0,.629-1.607,2.238,2.238,0,0,0-.629-1.607,2.238,2.238,0,0,0-1.585-.682,2.253,2.253,0,0,0-1.628.66,2.253,2.253,0,0,0-.66,1.628,2.253,2.253,0,0,0,.66,1.628,2.253,2.253,0,0,0,1.628.66ZM44.32-201h3.357v-17.456h4.244v-2.766H47.677v-1.439c0-2.62,1-3.727,4.022-3.727v-2.8c-5.167,0-7.381,2.03-7.381,6.532v1.438H41.7v2.768h2.62Zm17.9-17.714c3.469,0,6.421,2.177,6.385,6.126H55.834c.369-3.949,3.1-6.127,6.385-6.127Zm9.447,11.478H68.047c-.738,2.177-2.657,3.727-5.683,3.727a6.358,6.358,0,0,1-6.569-6.347H71.96a18.207,18.207,0,0,0,.111-2.03,9.27,9.27,0,0,0-9.706-9.669c-5.9,0-10,4.023-10,10.407,0,6.421,4.244,10.481,10,10.481,5.02,0,8.267-2.88,9.3-6.57ZM81.926-201h3.358v-25.722H81.926Zm21.663,0h3.322v-11.92c0-5.794-3.58-8.674-8.267-8.674a7.47,7.47,0,0,0-6.5,3.248v-2.876H88.79V-201h3.358v-11.182c0-4.355,2.362-6.5,5.794-6.5,3.4,0,5.646,2.1,5.646,6.237Zm21.146-5.5c-.148-7.233-11.883-4.613-11.883-9.41,0-1.624,1.476-2.8,3.949-2.8,2.694,0,4.244,1.476,4.392,3.5h3.358c-.221-3.949-3.174-6.347-7.639-6.347-4.5,0-7.418,2.547-7.418,5.646,0,7.528,11.957,4.907,11.957,9.41,0,1.661-1.476,2.989-4.133,2.989-2.841,0-4.576-1.476-4.761-3.432h-3.47c.221,3.69,3.543,6.274,8.267,6.274,4.465,0,7.382-2.511,7.382-5.832Zm20.445-14.724h-3.359v11.108c0,4.355-2.325,6.5-5.794,6.5-3.4,0-5.646-2.1-5.646-6.2v-11.4h-3.323v11.846c0,5.794,3.654,8.674,8.3,8.674a7.437,7.437,0,0,0,6.458-3.286V-201h3.358ZM152.007-212c0-4.687,2.436-6.126,5.646-6.126h.886v-3.469c-3.211,0-5.388,1.4-6.532,3.654v-3.284h-3.358V-201h3.358Zm6.938.812c0,6.274,4.17,10.518,9.558,10.518a8.445,8.445,0,0,0,7.565-4.1V-201h3.4v-20.223h-3.4v3.69a8.4,8.4,0,0,0-7.528-4.022c-5.425,0-9.595,4.056-9.595,10.368Zm17.123.037c0,4.761-3.174,7.528-6.864,7.528s-6.827-2.8-6.827-7.565,3.137-7.455,6.827-7.455,6.865,2.8,6.865,7.49ZM197.731-201h3.322v-11.92c0-5.794-3.58-8.674-8.267-8.674a7.47,7.47,0,0,0-6.5,3.248v-2.876h-3.358V-201h3.358v-11.182c0-4.355,2.362-6.5,5.794-6.5,3.4,0,5.646,2.1,5.646,6.237Zm5.351-10.149c0,6.421,4.1,10.481,9.853,10.481,5.019,0,8.3-2.8,9.337-6.827h-3.617c-.738,2.547-2.731,3.987-5.72,3.987-3.69,0-6.421-2.62-6.421-7.639,0-4.945,2.731-7.565,6.421-7.565,2.989,0,5.019,1.55,5.72,3.987h3.617c-1.033-4.244-4.318-6.827-9.337-6.827-5.757,0-9.853,4.054-9.853,10.4Zm30.04-7.565c3.469,0,6.421,2.177,6.385,6.126H226.738c.369-3.951,3.1-6.128,6.384-6.128Zm9.447,11.478h-3.616c-.738,2.177-2.657,3.727-5.683,3.727a6.358,6.358,0,0,1-6.569-6.347h16.165a18.214,18.214,0,0,0,.111-2.03,9.27,9.27,0,0,0-9.706-9.669c-5.9,0-10,4.023-10,10.407,0,6.421,4.244,10.481,10,10.481,5.016,0,8.265-2.881,9.3-6.571Z" transform="translate(-23.1 388.659)" fill="#1d1c18"/>
    </g>
  </g>
</svg>
