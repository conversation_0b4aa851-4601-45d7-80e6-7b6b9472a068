import { MoreVert } from "@mui/icons-material";
import React from "react"
import { useDispatch } from "react-redux";
// import { isNonProgressiveWfhCalling } from "../../../helpers";
import { updateStateInRedux } from "../../../store/actions/SalesView/SalesView";

const RightBarMobileMenuIcon = () => {
    const dispatch = useDispatch();

    const OpenRightBarMenu = () => {
        dispatch(updateStateInRedux({ key: 'openRightbarMenuMobileOptions', value: true }))
    }
    // if(isNonProgressiveWfhCalling()){
    //     return null;
    // }
    return (<>
        <div onClick={OpenRightBarMenu} className="rightmenuIcon">
            <MoreVert />
        </div>
    </>)
}

export default RightBarMobileMenuIcon;