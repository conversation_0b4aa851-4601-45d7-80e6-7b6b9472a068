import React, { useEffect, useState } from "react";
import { Grid, LinearProgress, Tooltip } from "@mui/material";
import makeStyles from '@mui/styles/makeStyles';
import ExpandMore from '@mui/icons-material/ExpandMore';
import { CALL_API } from '../../../services/api.service';
import rootScopeService from "../../../services/rootScopeService";
import { useSelector, useDispatch } from "react-redux";
import IssuedCustomerHistory from "./Modals/IssuedCustomerHistory";
import InProgressCustomerHistory from "./Modals/InProgressCustomerHistory";
import CancelledCustomerHistory from "./Modals/CancelledCustomerHistory";
import VisitedCustomerHistory from "./Modals/VisitedCustomerHistory";
import dayjs from "dayjs";
import CallDetails from "../RightBlock/CallDetails";
import { updateStateInRedux } from '../../../store/actions/SalesView/SalesView';
import { GetCustomerVisitTrailsService, GetIssuedPolicies } from "../../../services/Common";
import { SV_CONFIG } from "../../../appconfig";

const useStyles = makeStyles((theme) => ({
    customSize: {
        padding: 0,
        maxWidth: 820,
        maxHeight: '400px',
        overflow: 'auto',
        '& .MuiTableContainer-root': {
            maxHeight: 350,
            maxWidth: 820,
            overflow: 'auto',
        },
        '& p': {
            padding: '8px 16px',
            fontSize: '15px',
        }
    },
}));

export default function CustomerHistory(props) {
    const classes = useStyles();
    const parentLeadId = useSelector(state => state.salesview.parentLeadId)
    const [PoliciesDetails, setPoliciesDetails] = useState([]);
    const [BookingDetails, setBookingDetails] = useState([]);
    const [CustHistoryDetails, setCustHistoryDetails] = useState([]);
    const [IsAllDataLoading, setIsAllDataLoading] = useState(false);
    const [CustomerVisitTrails, setCustomerVisitTrails] = useState([]);
    let [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);
    const dispatch = useDispatch();

    let productId = rootScopeService.getProductId();
    const showCustVisitTrail = [7, 115, 117, 2].includes(productId);
    const showCustHistoryToggle = [7, 117, 2].includes(productId);
    const [show, setShow] = useState(showCustHistoryToggle ? false : true);

    const GetCustomerVisitTrails = () => {
        GetCustomerVisitTrailsService(parentLeadId).then((result) => {
            if (Array.isArray(result)) {
                setCustomerVisitTrails(result);
            }
            else {
                setCustomerVisitTrails([]);
            }
        });
    }

    const getCustomerPolicies = () => {
        GetIssuedPolicies( rootScopeService.getCustomerId(), rootScopeService.getProductId() ).then((result) => {
            if (result) {
                let data = Array.isArray(result) ? result.filter((d) => ([15, 41, 42, 43, 44].indexOf(d.StatusId) !== -1)) : null;
                setPoliciesDetails(data);
                dispatch(updateStateInRedux({ key: 'issuedPolicies', value: result }));
            }
        });
    }

    //For inprogress in cust history
    const getCustomerBookings = () => {
        const input = 
            {
                url: `coremrs/api/MRSCore/GetCustomerBookings/${rootScopeService.getCustomerId()}`,
                method: 'GET', service: 'MatrixCoreAPI',
            };
        CALL_API(input).then((result) => {
                setBookingDetails(result);
        })
            .catch((e) => {
                console.log(e);
            })
            .finally(() => {
                setIsAllDataLoading(false)
            });
    }


    //For Cancelled in cust history
    const getCustomerRejection = () => {


        let CustomerId = rootScopeService.getCustomerId();

        const input =
        {
            url: `coremrs/api/MRSCore/GetCustomerHistory/${CustomerId}`,
            method: "GET",
            service: "MatrixCoreAPI",
        }
        CALL_API(input).then((result) => {
            if (result) {
                setCustHistoryDetails(result);
                if (result.RejectLeads) {
                    var RejLeads = result.RejectLeads;
                    if (Array.isArray(RejLeads)) {
                        var RejectedLeadsWithReasons = RejLeads.filter(R => (R.ReasonDate > 0));
                        if (Array.isArray(RejectedLeadsWithReasons)) {
                            RejectedLeadsWithReasons.sort((a, b) => {
                                return b.ReasonDate - a.ReasonDate;
                            });
                            var Reasons = [];
                            var Res = [];
                            for (var i = 0; i < RejectedLeadsWithReasons.length; i++) {
                                if (Res.indexOf(RejectedLeadsWithReasons[i].Reason) == -1) {
                                    Res.push(RejectedLeadsWithReasons[i].Reason)
                                    Reasons.push({ LeadID: RejectedLeadsWithReasons[i].LeadID, Reason: RejectedLeadsWithReasons[i].Reason })
                                }
                            }
                            dispatch(updateStateInRedux({ key: 'bookingcancelreason', value: Reasons }));
                        }
                    }
                }
            }

        });

    }
    const getAllData = () => {
        getCustomerPolicies();
        getCustomerRejection();
        if (showCustVisitTrail) {
            GetCustomerVisitTrails();
        }
        getCustomerBookings();
    }
    useEffect(() => {
        if (parentLeadId && !showCustHistoryToggle) {
            setPoliciesDetails([]);
            dispatch(updateStateInRedux({ key: 'issuedPolicies', value: [] }));
            setBookingDetails([]);
            setCustHistoryDetails([]);
            setCustomerVisitTrails([]);
            setTimeout(() => {
                getAllData();
            }, 1500)
        }
    }, [parentLeadId, !showCustHistoryToggle]);

    const handleToggle = (e) => {

        setShow(!show);
        if (show == false) {
            setIsAllDataLoading(true)
            setPoliciesDetails([]);
            dispatch(updateStateInRedux({ key: 'issuedPolicies', value: [] }));
            setBookingDetails([]);
            setCustHistoryDetails([]);
            setCustomerVisitTrails([]);
            //setTimeout(() => {
            getAllData();
            // }, 1500)
        }

    }

    useEffect(() => {
        if (RefreshLead && showCustHistoryToggle) {
            setShow(false);
        }
    }, [RefreshLead, showCustHistoryToggle]);

    return (
        <Grid item sm={12} md={12} xs={12}>
            <div className={show ? `customerHistory ${props.className} ${props.classWhenopen}` : `customerHistory ${props.className}`}>
                <h3>Customer History</h3>
                {(showCustHistoryToggle) && <div className="expandmoreIcon">
                    <ExpandMore onClick={handleToggle} style={{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)' }} />
                </div>
                }
                <p>Previous visits and policy details</p>
                {IsAllDataLoading && showCustHistoryToggle && <LinearProgress />}
                {show && !IsAllDataLoading &&
                    <><ul className="customercard-details">
                        <li>Issued</li>
                        <li>  <Tooltip enterTouchDelay={0} leaveTouchDelay={20000} interactive placement="left" classes={{ tooltip: classes.customSize, popper: "addmoreLeadPopup" }}
                            title={<IssuedCustomerHistory data={(PoliciesDetails && PoliciesDetails.length > 0) ? PoliciesDetails : []} />}>
                            <div className="detailBox"><span className="hoverable">{PoliciesDetails && PoliciesDetails.length ? PoliciesDetails.length : '0'}</span></div></Tooltip></li>
                        <li>In Progress</li>
                        <li><Tooltip enterTouchDelay={0} leaveTouchDelay={20000} interactive placement="left" classes={{ tooltip: classes.customSize, popper: "addmoreLeadPopup" }}
                            title={<InProgressCustomerHistory data={(BookingDetails && BookingDetails.length) > 0 ? BookingDetails : []} />}>
                            <div className="detailBox"> <span className="hoverable">{BookingDetails && BookingDetails.length != null ? BookingDetails.length : '0'}</span></div></Tooltip>
                        </li>
                        <li>Cancelled?</li>
                        <li> <Tooltip enterTouchDelay={0} leaveTouchDelay={20000} interactive placement="left" classes={{ tooltip: classes.customSize, popper: "addmoreLeadPopup" }}
                            title={<CancelledCustomerHistory IsCancelledData={true} data={(CustHistoryDetails && CustHistoryDetails.CancelledLeads != undefined && CustHistoryDetails.CancelledLeads.length > 0) ? CustHistoryDetails.CancelledLeads : []} />}>
                            <div className="detailBox"> <span className="hoverable">{(CustHistoryDetails && CustHistoryDetails.CancelledLeads != undefined && CustHistoryDetails.CancelledLeads.length > 0) ? CustHistoryDetails.CancelledLeads.length : '0'}</span></div></Tooltip>
                        </li>
                        <li>Rejected</li>
                        <li> <Tooltip enterTouchDelay={0} leaveTouchDelay={20000} interactive placement="left" classes={{ tooltip: classes.customSize, popper: "addmoreLeadPopup" }}
                            title={<CancelledCustomerHistory data={(CustHistoryDetails && CustHistoryDetails.RejectLeads != undefined && CustHistoryDetails.RejectLeads.length > 0) ? CustHistoryDetails.RejectLeads : []} />}>
                            <div className="detailBox"> <span className="hoverable">{(CustHistoryDetails && CustHistoryDetails.RejectLeads != undefined && CustHistoryDetails.RejectLeads.length > 0) ? CustHistoryDetails.RejectLeads.length : '0'}</span></div></Tooltip>
                        </li>
                        <li>Previous Lead</li>
                        <li>{dayjs(CustHistoryDetails ? CustHistoryDetails.LastLead : 0).format('DD/MM/YYYY h:mm a')}</li>
                        <li>Previous Visit</li>
                        <li>{dayjs(CustHistoryDetails ? CustHistoryDetails.LastVisit : 0).format('DD/MM/YYYY h:mm a')}</li>
                        {(showCustVisitTrail) && <>
                            <li>Visit count</li>
                            <li><Tooltip enterTouchDelay={0} leaveTouchDelay={20000} interactive placement="left" classes={{ tooltip: classes.customSize, popper: "addmoreLeadPopup" }}
                                title={<VisitedCustomerHistory data={(CustomerVisitTrails && CustomerVisitTrails.length > 0) ? CustomerVisitTrails : []} />}>
                                <div className="detailBox"> <span className="hoverable">{CustomerVisitTrails ? CustomerVisitTrails.length : '0'} </span></div></Tooltip>
                            </li>
                        </>
                        }
                    </ul>
                        <CallDetails />
                    </>
                }
            </div>

        </Grid >

    )
}

