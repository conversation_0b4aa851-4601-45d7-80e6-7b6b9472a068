let diallerInput = document.getElementById('diallerInput');
let currentCall = '';

//let accessToken = '635d6620-22d3-11e9-9a01-5ba7bf541cfa';

/**
  This function will execute the _bootstrap function to connect to the Verto server
  and log in with specified credentials and configurations.
  To see the full documentation, go to: http://evoluxbr.github.io/verto-docs/tut/initializing-verto.html
**/
// const baseUrl = 'https://policybazaar-api.aswat.co/'
// //const socketClient = require('socket.io-client')
// let queryParams = 'access_token=' + accessToken
// //var socket = io(baseUrl);
// let socket = io(baseUrl, {
//   query: queryParams,
//   path: '/socket',
//   reconnection: true
// })
// socket.on('GET /live/status', function (data) {
//     console.log(data);
// });

// socket.emit('GET /live/status', {});
var RecentCalls = []

var CallActiveId = null;
var oSipStack;
var parentNotExistCount = 0;

const configName = localStorage.getItem('configName') || 'SVConfig';

let SV_CONFIG = readFromLSCache(configName);

try {
    SV_CONFIG = JSON.parse(SV_CONFIG);
}
catch (e) { }


function readFromLSCache(key) {
    var data = null;
    try {
        data = JSON.parse(localStorage.getItem(key));
    } catch {
        data = null;
    }
    if (data !== null) {
        if (
            (data.expires_at !== null && data.expires_at < new Date().getTime()) // flush expired data
            || !((typeof data === 'object') && ("value" in data) && ("expires_at" in data)) // condition to handle previous data stored, in other format
        ) {
            localStorage.removeItem(key);
        } else {
            return data.value;
        }
    }
    return null;
};

function BindRecentCalls() {
    if (localStorage.getItem("RecentCalls")) {
        RecentCalls = JSON.parse(localStorage.getItem("RecentCalls"));
    }

    // setInterval(function () { BindRecentCallsUI(); }, 1000);
}

function UpdateCall(data) {
    if (AppConfig.InternationalGroupID.indexOf(user.GroupId) == -1) {
        data.params.CallDate = new Date();
        data.params.duration = 0;
        // RecentCalls.push(data.params);
        // if (RecentCalls.length >= 10) {
        //     RecentCalls.shift();
        // }
        // localStorage.setItem("RecentCalls", JSON.stringify(RecentCalls));
    }
}

function BindRecentCallsUI() {
    var html = "";

    RecentCalls.forEach(element => {
        if (element.callID == CallActiveId) {
            element.duration = element.duration + 1;
            localStorage.setItem("RecentCalls", JSON.stringify(RecentCalls));
        }
        var mobileno = '';
        var calldate = element.CallDate;

        if (element.remote_caller_id_name == "Outbound Call") {
            mobileno = element.remote_caller_id_number;
        } else {
            mobileno = element.caller_id_name;
        }


        html = html + "<li id='" + element.callID + "'><span class='number'>" + mobileno + "</span><br/><span class='calldate'>" + moment(calldate).format('MM/DD hh:mm:ss a') + "</span><br/><span class='duration'>" + formatSeconds(element.duration) + "</span></li>"
    });
    document.getElementById("lstNumbers").innerHTML = html;
}

function deleteCallLogs() {
    localStorage.removeItem("RecentCalls");
    BindRecentCalls();
}

function formatSeconds(seconds) {
    var date = new Date(1970, 0, 1);
    date.setSeconds(seconds);
    return date
        .toTimeString()
        .replace(/.*(\d{2}:\d{2}:\d{2}).*/, "$1");
}

function connectToFreeswitch() {
    agendId = getParamValue('agentid').toLowerCase();

    hostname = 'policybazaar-api.aswat.co';
    socketURL = 'wss://policybazaar-api.aswat.co:8082';
    position = agendId;
    password = 'PB' + agendId;

    if (getParamValue('india') && getParamValue('india').toLowerCase() == "true") {
        hostname = 'paisabazaar-api.aswat.co';
        socketURL = 'wss://paisabazaar-api.aswat.co:8082';
    }
    //alert(hostname + socketURL + position + password);




    function _bootstrap(status) {
        document.getElementById('hangup').style.visibility = 'hidden';

        vertoHandle = new jQuery.verto({
            login: position + '@' + hostname,
            passwd: password,
            socketUrl: socketURL,
            ringFile: 'ringtone.wav',
            tag: 'positionDevice',
            deviceParams: {
                useMic: true,
                useSpeak: true
            },
            iceServers: true
        },
            /**
            Almost everything that happens in Verto fires an event.
            onWSLogin will be fired on login
            onWSClose will be fired when closing the socket
            onDialogState is used to handle incoming and outgoind calls
            **/
            {
                onWSLogin: onWSLogin,
                onWSClose: onWSClose,
                onDialogState: onDialogState
            });
    }

    if (hostname && socketURL && position && password) $.verto.init({}, _bootstrap);
    else alert('You need to provide the full configuration settings.');
}

// Verto Events
function onWSLogin(verto, success) {
    if (!success) alert('Wrong connexion infos');
    else {
        oSipStack = true;

        window.parent.postMessage({ type: "oSipStack", oSipStack: true }, '*');
        window.parent.postMessage({ type: "oSipSessionRegister", oSipSessionRegister: true }, '*');
        document.getElementById('positionStatus').innerHTML = 'Connected to position ' + document.getElementById('position').value;
        document.getElementById('dialler').style.borderColor = 'green';
        document.getElementById('dialler').style.backgroundColor = 'rgba(0,255,0,0.05)';
    }
}

function onWSClose(verto, success) {
    oSipStack = false;
    window.parent.postMessage({ type: "oSipStack", oSipStack: false }, '*');
    window.parent.postMessage({ type: "oSipSessionRegister", oSipSessionRegister: false }, '*');
    if (!success) alert('Something went wrong while closing the socket.')
}

function onDialogState(d) {
    if (!currentCall) currentCall = d;
    var data = window
        .localStorage
        .getItem('ASWATCALLDATA');

    if (data != "" && data != null && data != undefined) {
        data = JSON.parse(data);
        if (data)
            data.campaign = "ASWAT"
    }
    switch (d.state.name) {
        case 'ringing':
            if (AppConfig.InternationalGroupID.indexOf(user.GroupId) == -1) {
                document.getElementById('callStatus').innerHTML = 'Receiving a Call' // from ' + currentCall.params.caller_id_name;
            }
            document.getElementById('dialler').style.borderColor = 'lightblue';
            document.getElementById('dialler').style.backgroundColor = 'rgba(0,100,255,0.05)';
            document.getElementById('dialler').style.backgroundColor = '';
            document.getElementById('hangup').style.backgroundColor = 'red';
            document.getElementById('answer').style.backgroundColor = 'green';
            CallActiveId = currentCall.callID;
            localStorage.setItem('onCall', true);
            if (data) {
                data.CallId = currentCall.callID;
                localStorage.setItem("ASWATCALLDATA", JSON.stringify(data));
            }
            //UpdateCall(currentCall.params.caller_id_name);
            UpdateCall(currentCall);
            //currentCall.callID
            notifyMe(document.getElementById('callStatus').innerHTML);
            try { document.getElementById("audioLocal").play(); } catch (e) { }
            setTimeout(function () {
                //if (data) {
                answerCall();
                //}
            }, 3000);
            onmute = false;
            document.getElementById('hangup').style.visibility = 'hidden';
            setTimeout(function () {
                document.getElementById('hangup').style.visibility = 'visible';
            }, 10000);



            break;
        case 'trying':
            document.getElementById('callStatus').innerHTML = 'Call is in Trying state';
            document.getElementById('dialler').style.borderColor = 'lightblue';
            document.getElementById('dialler').style.backgroundColor = 'rgba(0,100,255,0.05';
            break;
        case 'early':
            if (AppConfig.InternationalGroupID.indexOf(user.GroupId) == -1) {
                document.getElementById('callStatus').innerHTML = 'Is Calling ';// + currentCall.params.destination_number;
            }
            document.getElementById('hangup').style.backgroundColor = 'red';
            //UpdateCall(currentCall.params.destination_number);	
            CallActiveId = currentCall.callID;
            localStorage.setItem('onCall', true);
            UpdateCall(currentCall);
            break;
        case 'active':
            diallerInput.value = '';
            if (currentCall && currentCall.params)
                if (AppConfig.InternationalGroupID.indexOf(user.GroupId) == -1) {
                    document.getElementById('callStatus').innerHTML = 'In a Call'; // with ' + currentCall.params.caller_id_name;
                } else {
                    document.getElementById('callStatus').innerHTML = 'In a Call';
                }
            else
                document.getElementById('callStatus').innerHTML = 'In a Call';

            document.getElementById('dialler').style.borderColor = 'red';
            document.getElementById('dialler').style.backgroundColor = 'rgba(255,0,0,0.05)';
            document.getElementById('hangup').style.backgroundColor = 'red';
            document.getElementById('hangup').style.visibility = 'hidden';

            setTimeout(function () {
                document.getElementById('hangup').style.visibility = 'visible';
            }, 10000)

            document.getElementById('answer').style.backgroundColor = '';
            CallActiveId = currentCall.callID;
            localStorage.setItem('onCall', true);
            if (data) {
                data.InCallTime = new Date();
                data.CallId = currentCall.callID;
                localStorage.setItem("ASWATCALLDATA", JSON.stringify(data));
            }
            notifyMe(document.getElementById('callStatus').innerHTML);
            AgentAnswerd();
            break;
        case 'hangup':
            if (data) {
                data.hangupCause = currentCall.cause.toUpperCase().replace(' ', '_');
                localStorage.setItem("ASWATCALLDATA", JSON.stringify(data));
            }
            document.getElementById('callStatus').innerHTML = 'Call has hang up';
            document.getElementById('hangup').style.backgroundColor = '';
            document.getElementById('answer').style.backgroundColor = '';
            document.getElementById('hangup').style.visibility = 'hidden';

            notifyMe(document.getElementById('callStatus').innerHTML);
            try {
                document.getElementById("audioLocal").pause();
                document.getElementById("audioLocal").currentTime = 0;
            } catch (e) { }
            try { ChangeAswatStatus(agentStatus, elem); } catch (e) { }
            try { HangupData(); } catch (e) { }
            CallActiveId = null;
            localStorage.setItem('onCall', false);

            break;
        case 'destroy':
            document.getElementById('hangup').style.backgroundColor = '';
            document.getElementById('callStatus').innerHTML = 'Not in a Call';
            document.getElementById('answer').style.backgroundColor = '';
            if (currentCall) {
                if (data) {
                    data.hangupCause = currentCall.cause.toUpperCase().replace(' ', '_');
                    localStorage.setItem("ASWATCALLDATA", JSON.stringify(data));
                }
                currentCall = null;
                CallActiveId = null;
                localStorage.setItem('onCall', false);
                document.getElementById('dialler').style.borderColor = 'green';
                document.getElementById('dialler').style.backgroundColor = 'rgba(0,255,0,0.05)';
                diallerInput.value = '';
            }
            break;
        default:
            console.warn('Got a not implemented state:', d);
            break;
    }
}


function replaceAt(string, index, replace) {
    return string.substring(0, index) + replace + string.substring(index + 1);
}
// Dialler Actions
function dial(number) {
    diallerInput.value += number;
    if (currentCall) currentCall.dtmf(number);
}

function sendDtmf(number) {
    if (currentCall) currentCall.dtmf(number);
}

function ConnectCall(data) {
    number = data._mobileNo;
    if (number.charAt(0) == "1") {
        number = replaceAt(number, 0, "+");
    }

    diallerInput.value += number;
    makeCall();
    data.InCall = true;
    localStorage.setItem("ASWATCALLDATA", JSON.stringify(data));
}


// Call Actions
function makeCall() {
    if (currentCall) return alert('You are already in a call');

    if (diallerInput.value) {
        currentCall = vertoHandle.newCall({
            destination_number: diallerInput.value,
            caller_id_name: '',
            caller_id_number: '',
            outgoingBandwidth: 'default',
            incomingBandwidth: 'default',
            useStereo: true,
            useMic: true,
            useSpeak: true,
            dedEnc: false,
            audioParams: {
                googAutoGainControl: false,
                googNoiseSuppression: false,
                googHighpassFilter: false
            }
        });
        localStorage.setItem('onCall', true);
    } else alert('You need to provide a number');
}

function answerCall() {
    if (!currentCall) return alert('There is no incoming call');
    if (currentCall.state.name === 'active') return alert('You are currently in a call');
    try {
        document.getElementById("audioLocal").pause();
        document.getElementById("audioLocal").currentTime = 0;
    } catch (e) { }
    currentCall.answer({
        useStereo: true,
        useCamera: false,
        useVideo: false,
        useMic: true,
        callee_id_name: '',
        callee_id_number: ''
    });
}

function hangupCall() {
    if (!currentCall) return alert('You are not in a call');
    currentCall.hangup();
    diallerInput.value = '';
}

var onmute = false;

function mute() {
    if (!currentCall) return alert('You are not in a call');
    currentCall.setMute('off');
    onmute = true;
    document.getElementById('mute').style.backgroundColor = '';
    document.getElementById('unmute').style.backgroundColor = '#ababf3';
}

function unmute() {
    if (!currentCall) return alert('You are not in a call');
    currentCall.setMute('on');
    onmute = false;
    document.getElementById('unmute').style.backgroundColor = '';
    document.getElementById('mute').style.backgroundColor = '#ababf3';
}

function transfer() {
    if (!currentCall) return alert('You are not in a call');
    if (!diallerInput.value) return alert('You should provide a number for tranfer');

    currentCall.transfer(diallerInput.value);
}

var onhold = false;

function hold() {
    if (!currentCall) return alert('You are not in a call');
    currentCall.hold();
    onhold = true;
    document.getElementById('hold').style.backgroundColor = '';
    document.getElementById('unhold').style.backgroundColor = '#ababf3';
}

function unhold() {
    if (!currentCall) return alert('You are not in a call');
    currentCall.unhold();
    onhold = false;
    document.getElementById('unhold').style.backgroundColor = '';
    document.getElementById('hold').style.backgroundColor = '#ababf3';
};

function WindowLogout() {
    setTimeout(function () {
        window.close();
    }, 500);
}

// *************************CUSTOM FUNCTIONS START HERE **************************

function agentLogin() {
    agentId = getParamValue('agentid').toLowerCase();
    //agentDid	= getParamValue('did').toLowerCase();

    url = AppConfig.MatrixCoreApiBaseUrl + "onelead/api/Communication/AswatIndiaLogin/" + agendId;
    


    var settings = {
        "url": url,
        "method": "GET",
        xhrFields: {
            withCredentials: true
        }
    }

    $.ajax(settings).done(function (response) {
        console.log(response);
    });
}

function checkWindowExist() {
    window
        .localStorage
        .setItem('CallWindow', new Date());
}
var agentStatus = 1;

function ChangeAswatStatus(status, elem) {
    agentStatus = status;
    localStorage.setItem("agentaswatstatus", agentStatus);
    var AgentId = getParamValue('agentid').toLowerCase();

    let url = AppConfig.MatrixCoreApiBaseUrl + "onelead/api/Communication/AswatIndiaStatus/" + AgentId + "/" + status;

    try {
        $.ajax({
            type: "GET",
            url: url,
            dataType: "json",
            xhrFields: {
                withCredentials: true
            },
            success: function (json) {
                if (status == 1) {
                    document.getElementById('dialler').style.backgroundColor = 'rgba(0,255,0,0.05)';
                } else if (status == 4) {
                    document.getElementById('dialler').style.backgroundColor = 'rgb(137, 168, 232)';
                }
            },
            error: function (xhr) { }
        });
    } catch (e) { }
}

function getParamValue(paramName) {
    var url = window
        .location
        .search
        .substring(1); //get rid of "?" in querystring
    var qArray = url.split('&'); //get key-value pairs
    for (var i = 0; i < qArray.length; i++) {
        var pArr = qArray[i].split('='); //split key and value
        if (pArr[0] == paramName)
            return pArr[1]; //return value
    }
}
var user;
window.onload = function () {
    window
        .localStorage
        .setItem('setMuteText', "Mute");
    window
        .localStorage
        .setItem('setHoldText', "Hold");
    window
        .localStorage
        .setItem('setCallStatus', "Call");
    // BindRecentCalls();
    MicrophoneEnable();
    connectToFreeswitch();
    agentLogin();
    user = window.atob(window.localStorage.getItem('User'));
    user = jQuery.parseJSON(user);
    if (user) {
        //user.DIDNo = 8888;
        document.getElementById('didno').innerHTML = user.DIDNo;

        if (AppConfig.InternationalGroupID.indexOf(user.GroupId) > -1) {
            $(".recentCalls").hide();
        }

        if (AppConfig.AswatDialerRestriction && Array.isArray(AppConfig.AswatDialerRestriction.EmployeeId) && AppConfig.AswatDialerRestriction.EmployeeId.indexOf(user.EmployeeId) > -1) {
            //document.getElementById('outgoing').style.visibility = "hidden";
        }
        else {
            document.getElementById('outgoing').style.visibility = "hidden";
        }
    }



    try {
        if (localStorage.getItem("agentaswatstatus")) {
            if (!isNaN(localStorage.getItem("agentaswatstatus"))) {
                agentStatus = parseInt(localStorage.getItem("agentaswatstatus"));
            }
            try { setTimeout(function () { ChangeAswatStatus(agentStatus); }, 2000) } catch (e) { }
        }
    } catch { }
    setInterval(function () { checkWindowExist(); }, 1000);

    setInterval(function () {
        if (localStorage.getItem("agentaswatstatus")) {
            if (!isNaN(localStorage.getItem("agentaswatstatus"))) {
                agentStatus = parseInt(localStorage.getItem("agentaswatstatus"));

                switch (agentStatus) {
                    case 1:
                        document.getElementById('dialler').style.backgroundColor = 'rgba(0,255,0,0.05)';
                        break;
                    case 2:
                        document.getElementById('dialler').style.backgroundColor = 'rgba(255, 192, 129, 1)';
                        break;
                    case 3:
                        document.getElementById('dialler').style.backgroundColor = 'rgba(162, 68, 4, 1)';
                        break;
                    case 4:
                        document.getElementById('dialler').style.backgroundColor = 'rgb(137, 168, 232)';
                        break;
                    default:
                        document.getElementById('dialler').style.backgroundColor = 'rgba(0,255,0,0.05)';
                        break;
                }
            }
        }
    }, 2000);



}


function notifyMe(msg) {

    window.localStorage.setItem("notifyMe", 1);
    window.localStorage.setItem("notifyMeMsg", msg);
    //window.opener.notifyMe();

}
// *************************CUSTOM FUNCTIONS END HERE **************************



function AgentAnswerd() {
    var data = window
        .localStorage
        .getItem('ASWATCALLDATA');

    if (data != "" && data != null && data != undefined) {
        data = JSON.parse(data);
        if (data) {
            data.callDate = data.callDate ?
                data.callDate :
                JSON
                    .stringify(new Date())
                    .replace(/"/g, '');
            window
                .localStorage
                .setItem('ASWATCALLDATA', JSON.stringify(data));
        }
    }

    var url = AppConfig.CommBox;
    var ConnectCallSFURL = url + "Asteriskevent.svc/InsertCallData?CallID=" + data.CallId + "&LeadID=" + data.LeadID + "&AgentID=" + data.empId + "&callDate=" + data.callDate + "&callType=OB&uid=" + data.uid + "&Context=" + data.campaign + "&dst=" + data.SF + "&action=agentanswered";

    $.ajax({
        url: ConnectCallSFURL,
        type: 'GET',
        dataType: 'json',
        xhrFields: {
            withCredentials: true
        },
        success: function (data) { },
        error: function (request, error) {
            //alert("Request: " + JSON.stringify(request));
        }
    });
}

function HangupData() {
    var data = window
        .localStorage
        .getItem('ASWATCALLDATA');
    if (data != "" && data != null && data != undefined) {

        var vdata = JSON.parse(data);
        if (vdata) {
            var url = AppConfig.CommBox;
            var ConnectCallSFURL = url + "Asteriskevent.svc/GetCallData?CallId=" + vdata.CallId + "&LeadId=" + vdata.LeadID;

            $.ajax({
                url: ConnectCallSFURL,
                type: 'GET',
                dataType: 'json',
                xhrFields: {
                    withCredentials: true
                },
                success: function (r) {
                    //r = JSON.parse(r);
                    vdata.uid = r.CallDataId;
                    //vdata.hangupCause = currentCall.cause.toUpperCase().replace(' ','_');
                    window
                        .localStorage
                        .setItem('ASWATCALLDATA', JSON.stringify(vdata));

                    AgentHangUp()
                    localStorage.removeItem("ASWATCALLDATA");
                },
                error: function (request, error) {

                }
            });
        }
    }

}


function AgentHangUp() {
    var Disposition, status;

    var data = window
        .localStorage
        .getItem('ASWATCALLDATA');

    var HangUptalkTime = 0;
    var durationTime = 0;
    if (data != "" && data != null && data != undefined) {
        data = JSON.parse(data);
        if (data) {
           
            if (data.InCallTime) {
                HangUptalkTime = (new Date() - new Date(data.InCallTime)) / 1000;
                durationTime = (new Date() - new Date(data.InCallTime)) / 1000;
            }
            if (data.CallInitTime) {
                durationTime = (new Date() - new Date(data.CallInitTime)) / 1000;
            }

            status = "18";
            Disposition = "Not Answered";

            if (['NORMAL_CLEARING', 'ANSWER', 'ANSWERED'].indexOf(data.hangupCause) > -1) {
                Disposition = "Answered";
                status = "16";
            }
            else {
                HangUptalkTime = 0;
            }

            if (getParamValue('india') && getParamValue('india').toLowerCase() == "true") {
                data.campaign = "ASWATINDIA";
            }

            if (data.InCallTime) {
                var url = AppConfig.CommBox;
                var ConnectCallSFURL = url + "Asteriskevent.svc/InsertCallData?CallID=" + data.CallId + "&LeadID=" + data.LeadID + "&AgentID=" + data.empId + "&callDate=" + data.InCallTime + "&callType=OB&talkTime=" + parseInt(HangUptalkTime) + "&Duration=" + parseInt(durationTime) + "&Disposition=" + Disposition + "&status=" + status + "&uid=" + data.uid + "&dst=" + data.SF + "&Context=" + data.campaign + "&action=hangup";

            } else {
                var url = AppConfig.CommBox;
                var ConnectCallSFURL = url + "Asteriskevent.svc/InsertCallData?CallID=" + data.CallId + "&LeadID=" + data.LeadID + "&AgentID=" + data.empId + "&callType=OB&talkTime=" + parseInt(HangUptalkTime) + "&Duration=" + parseInt(durationTime) + "&Disposition=" + Disposition + "&status=" + status + "&uid=" + data.uid + "&dst=" + data.SF + "&Context=" + data.campaign + "&action=hangup";

            }

            $.ajax({
                url: ConnectCallSFURL,
                type: 'GET',
                dataType: 'json',
                xhrFields: {
                    withCredentials: true
                },
                success: function (data) {

                },
                error: function (request, error) {

                }
            });
            
        }

    } else { }
}

setInterval(function () {

    try {
        var date = new Date();
        var user = window
            .localStorage
            .getItem('User');
        if (user && user != "") {
            user = window.atob(window.localStorage.getItem('User'));
            user = jQuery.parseJSON(user);

            if (user.CallingCompany === "ASWATINDIA") {
                /**
                 *  for ASWATINDIA status will be handled via webphoneControl.js file
                 */
                return;
            }
            
            if (CallActiveId != null) {
                cuurentStatus = "BUSY";
                localStorage.setItem('onCall', true);
            }
            else if (!IsMicrophoneEnable) {
                cuurentStatus = "MIC-OFF";
                localStorage.setItem('onCall', false);
            }
            else {
                cuurentStatus = "IDLE";
                localStorage.setItem('onCall', false);
            }
            UpdateAgentStatus(cuurentStatus, date.getTime(), user.EmployeeId);
        }
    } catch (error) { }
}, 3000);

function UpdateAgentStatus(cuurentStatus, lastUpdatedOn, agentCode) {
    var input = '{ "status" : "' + cuurentStatus + '", "LastUpdatedOn" : "\\\/Date(' + lastUpdatedOn + ')\\\/", "AgentCode": "' + agentCode + '"}'

    var updateagentstatusURL = SV_CONFIG["MatrixCoreAPI"][SV_CONFIG["environment"]] + "onelead/api/LeadPrioritization/updateagentstatus?onCall=true";
   
    $.ajax({
        url: updateagentstatusURL,
        type: 'POST',
        data: input,
        dataType: 'json',
        xhrFields: {
            withCredentials: true
        },
        contentType: "application/json; charset=utf-8",
        success: function (data) { },
        error: function (request, error) { }
    });

}
setInterval(function () {
    window.parent.postMessage({ type: "oSipSessionCall", oSipSessionCall: CallActiveId, phone: 'aswat' }, '*');

    window.parent.postMessage({ type: "webPhoneRegistered", isRegistered: checkforRegistraion(), phone: 'aswat' }, '*')
}, 1000);

window.addEventListener("message", function (event) {
    if (event.data && event.data.type == 'sipHangUp') {
        console.log(event.data);
        hangupCall();
    }
    if (event.data && event.data.type == 'sipToggleMute') {
        console.log(event.data);
        if (onmute) {
            unmute();
        } else {
            mute();
        }
    }
    if (event.data && event.data.type == 'sipToggleHoldResume') {
        console.log(event.data);
        if (onhold) {
            unhold();
        } else {
            hold();
        }
    }
    if (event.data && event.data.type == "SendDTMF") {
        sendDtmf(event.data.value);
    }
    
    //}
    // can message back using event.source.postMessage(...)
});

var IsMicrophoneEnable = false;
function MicrophoneEnable() {

    try {

        navigator.mediaDevices.getUserMedia({ audio: true })
            .then(function (stream) {
                IsMicrophoneEnable = true;
            })
            .catch(function (err) {
                IsMicrophoneEnable = false;
                console.log('No mic for you!')
            });
    }
    catch (e) {
        IsMicrophoneEnable = false;
    }

}

function CheckMicroPhone() {
    return IsMicrophoneEnable;
}
function checkforRegistraion() {
    if (oSipStack) {
        return true;
    }
    return false;
}

function sipHangUp() {
    hangupCall();
}
setInterval(function () {
    CheckMicroPhone();
    checkWindowExist();
    if (window.opener == undefined || window.opener == null) {
        parentNotExistCount = parseInt(parentNotExistCount) + 1;
    } else {
        parentNotExistCount = 0;
    }
    if (parentNotExistCount > 3) {
        WindowLogout();
    }
}, 1000);

function sipToggleMute() {
    if (onmute) {
        unmute();
    } else {
        mute();
    }
}