import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, LinearProgress } from "@mui/material";
import InfoRoundedIcon from '@mui/icons-material/InfoRounded';
import { useSnackbar } from "notistack";
import rootScopeService from "../../../services/rootScopeService";
import { copyToClipboard, gaEventNames, gaEventTracker } from "../../../helpers";
import User from "../../../services/user.service";
import { GeneratePbmeetLinkService, sendVCTemplateService } from "../../../services/Common";
import { CONFIG, SV_CONFIG } from "../../../appconfig";
import './VideoCallPopupExternal.scss'
import { useQuery } from "../../../hooks/useQuery";
import { FileCopyOutlined } from "@mui/icons-material";
import { useHistory, useLocation, useParams } from 'react-router-dom';

const SOURCES = {
    MATRIX: "matrix",
    BMS: "bms"
}
const PROCESSES = {
    VERIFICATION: 'verification'
}
const isProcessVerification = (src, process) => {
    try { process = process.toLowerCase(); } catch (error) { console.error(error) }
    return (src === SOURCES.BMS && process === PROCESSES.VERIFICATION);
}

const usingConclave = (src, process) => {
    try {
        let ConclaveUsers = Array.isArray(SV_CONFIG.ConclaveUsers) ? SV_CONFIG.ConclaveUsers : [];
        let enableConclave = SV_CONFIG.enableConclave;

        return (
            (isProcessVerification(src, process) && (enableConclave || ConclaveUsers.includes(User.UserId))) 
            || (SV_CONFIG && SV_CONFIG.enableConclaveTemp)
        )
    } catch {
        return false;
    }
}

export default function VideoCallPopup() {
    const query = useQuery();
    let src = query.get('src') || SOURCES.MATRIX;
    let process = query.get('process');
    try { 
        src = src.toLowerCase();
        if(User.EmployeeId.includes("BPW"))
        {
            src=SOURCES.BMS;
        }
     } catch (error) { console.error(error) }
    try { process = process.toLowerCase(); } catch (error) { console.error(error) }

    const history = useHistory();
    const { EncryptURL } = useParams();
    const location = useLocation();


    const [pbmeetRoomUrl, setPbmeetRoomUrl] = useState(0);
    const [agentUrl, setAgentUrl] = useState(0);
    const [customerUrl, setCustomerUrl] = useState(0);

    // const allLeads = useSelector(({ salesview }) => (salesview.allLeads));
    const parentLeadId = rootScopeService.getLeadId();
    const { enqueueSnackbar } = useSnackbar();
    const OpenVideoURL = (type) => {
        let callType = 'screenshare';
        let gaAction = gaEventNames.screenshare;
        // && window.confirm("Your current call will be disconnected after selecting this option. Do you want to continue?")
        if (type === 2) {
            gaAction = gaEventNames.videocall;
            callType = 'video';
            // window.CallHangup();
        }
        gaEventTracker(gaAction, `${src}_${process}`, parentLeadId);
        let _LeadId = '';
        try {
            _LeadId = pbmeetRoomUrl.split('/')[1];
        } catch (e) {
            _LeadId = parentLeadId;
        }
        if (usingConclave(src, process)) {
            window.open(agentUrl, '_blank');
            return;
        }
        else {
            window.open(`${pbmeetRoomUrl}?leadId=${_LeadId}&agentId=${User.EmployeeId}&process=matrix&callType=${callType}`, '_blank');
        }
    }

    const sendVCTemplate = function () {
        gaEventTracker(gaEventNames.sendinvite, `${src}_${process}`, parentLeadId, 'PbmeetTracking');
        let sendUrl = pbmeetRoomUrl;
        if (usingConclave(src, process)) {
            sendUrl = customerUrl
        }
        var reqData = { "CustId": rootScopeService.getCustomerId(), URL: sendUrl, LeadId: parentLeadId, productId: rootScopeService.getProductId() };

        sendVCTemplateService(reqData)
            .then(function (resultData) {
                if (resultData) {
                    enqueueSnackbar('Send Successfully', {
                        variant: 'success',
                        autoHideDuration: 3000,
                    });
                }
                else {
                    enqueueSnackbar('Invite not sent, Please try again!', {
                        variant: 'error',
                        autoHideDuration: 3000,
                    });
                }
            }).catch((e) => {
                enqueueSnackbar('Invite not sent, Please try again!', {
                    variant: 'error',
                    autoHideDuration: 3000,
                });
            });
    }
    const handleLinkResponse = (res) => {

        if (res && res.data && res.data.link) {
            if (usingConclave(src, process)) {
                setAgentUrl(res.data.link.agentLink);
                setCustomerUrl(res.data.link.customerLink);
            }
            setPbmeetRoomUrl(res.data.link);
        }
        else {
            setPbmeetRoomUrl(-1);
        }
    }
    const GeneratePbmeetLink = () => {
        setPbmeetRoomUrl(0);
        let agentName = User.UserName;
        try {
            // remove invalid characters
            agentName = agentName.replace(/[^a-zA-Z ]/g, "")
        } catch { }
        const reqData = {
            "leadId": parentLeadId,
            "employeeId": User.EmployeeId,
            "agentName": agentName,
            "source": src,
            "process": process,
            "IsConclave": usingConclave(src, process)
        }

        GeneratePbmeetLinkService(reqData).then(handleLinkResponse).catch((err) => {
            setPbmeetRoomUrl(-1);
            let dump = { reqData };
            try {
                dump.err = err;
                JSON.stringify(dump)
                gaEventTracker(gaEventNames.errorFetchingURL, dump, parentLeadId);
            } catch { }
        });
    }
    const copyCustomerLink = () => {
        gaEventTracker("CustomerLinkCopy", `${User.EmployeeId}`, parentLeadId);
        document.getElementById("custUrlTag").click();
    }

    const usingVCConf = () => {
        try {
            const verificationVCConfUsers = Array.isArray(SV_CONFIG.verificationVCConfUsers)
                ? SV_CONFIG.verificationVCConfUsers
                : [];
    
            return verificationVCConfUsers.includes(User.EmployeeId);
        } catch {
            return false;
        }
    };

    useEffect(() => {
        if (!usingVCConf () && parentLeadId) {
            GeneratePbmeetLink();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [parentLeadId]);

    useEffect(() => {
        if (usingVCConf()) {
            const searchParams = location.search;
            history.push(`/pbmeetv2/${EncryptURL}${searchParams}`);
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const isLoading = pbmeetRoomUrl === 0;
    const isError = pbmeetRoomUrl === -1;

    return (
        <div className="videoPopupExternal">
            <div className="popupLeftSection">
                <p className="comments">There is <b> 5x</b> possibility of <b>conversion </b>for leads using <b> Presentation/Screen Sharing </b> <br /> <br />Ask your customer to connect over <b> PBMeet</b></p><br /><br />
                {isError && <>
                    <p>Links could not be loaded</p>
                    <Button onClick={GeneratePbmeetLink}>Click here to retry</Button>
                </>}
                {isLoading && <LinearProgress />}
                {!isError && !isLoading &&
                    <>
                        {!usingConclave(src, process) && <div onClick={() => OpenVideoURL(1)} className="btnContainer cursorPointer"><img src={CONFIG.PUBLIC_URL + "/images/salesview/sharescreen.svg"} alt="Start ScreenShare" /><p className="shareScreen">Share Screen with customer</p></div>}
                        <div onClick={() => OpenVideoURL(2)} className="btnContainer cursorPointer"> <img src={CONFIG.PUBLIC_URL + "/images/salesview/videocall.svg"} alt="Start Video Call" /><p className="shareScreen">Start Video Call</p></div>
                        <button onClick={sendVCTemplate} className="sendInvite cursorPointer"><img alt="Send Invite" src={CONFIG.PUBLIC_URL + "/images/salesview/arrow.svg"} /> &nbsp; Send Invite</button>

                        {usingConclave(src, process) &&
                            <>
                                <Button
                                    onClick={copyCustomerLink}
                                    variant="text"
                                    className="copyCustUrl"
                                    startIcon={<FileCopyOutlined />}> &nbsp;
                                    Copy Customer Link
                                </Button>
                                <p onClick={copyToClipboard} style={{ height: 1, width: 1, color: "white", fontSize: 0.5 }} id="custUrlTag">{customerUrl}</p>
                            </>
                        }

                        {SV_CONFIG.pbmeetLinkExpireWarn && <p className="expireWarning"><InfoRoundedIcon /> Invite will be valid for 24 hours for the customers</p>}
                    </>
                }
            </div>
            <div className="popupRightSection">
                <h3 className="VChighlight">Presentation/Screen Sharing means…</h3>
                <img alt="5x Banner" src={CONFIG.PUBLIC_URL + "/images/salesview/videoClbg.svg"} />
                <h3 className="VChighlight">5x higher conversion</h3>
            </div>
        </div>
    );
}
