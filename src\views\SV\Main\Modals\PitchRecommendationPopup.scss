

.communicationIcon{
    width: 30px !important;
    height: auto !important;
    position: relative;
    top: -4px;
    right: 5px;
}
/* Custom Close Button */
.custom-close-button {
    position: absolute !important;
    top: 12px !important;
    right: 12px !important;
    z-index: 1000 !important;
    background: transparent !important;
    color: #424242 !important;
    border-radius: 50% !important;
    width: 36px !important;
    height: 36px !important;
    padding: 6px !important;
    min-width: 36px !important;

    &:hover {
        background: rgba(0, 0, 0, 0.04) !important;
        color: #212121 !important;
    }

    .MuiSvgIcon-root {
        font-size: 24px !important;
    }
}

/* Header Section - White background with blue text */
.pitch-header {
    background: white;
    color: #424242;
    padding: 0px;
    margin-bottom: 15px;
    text-align: left;
    position: relative;

    .pitch-icon {
        margin-bottom: 6px;

       
    }

    .pitch-title {
        color: #0065FF;
        font-family: Roboto;
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: 35px;
    }

    .pitch-content {
        color: rgba(37, 56, 88, 0.89);
        font-family: <PERSON><PERSON>;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 35px;
    }

    // Multiple recommendations styling
    .recommendations-list {
        margin-top: 8px;
        display: flex;
        flex-direction: column;
        gap: 6px;
    }

    .recommendation-item {
        padding: 8px 12px;
        background: rgba(0, 101, 255, 0.05);
        border-radius: 6px;
        border-left: 3px solid #0065FF;
        transition: all 0.2s ease;

        &:hover {
            background: rgba(0, 101, 255, 0.08);
        }

        .recommendation-text {
            color: rgba(37, 56, 88, 0.89) !important;
            font-family: Roboto !important;
            font-size: 14px !important;
            font-weight: 400 !important;
            line-height: 20px !important;
            margin: 0 !important;
        }
    }
}

/* Customer Concern Section */
.customer-concern-section {
    border-radius: 8px;
    background: rgba(255, 169, 26, 0.10);
    padding:16px;
  

    .concern-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;

       svg {
            font-size: 32px;
            color: #FFA91A;
        }

        .concern-icon {
            font-size: 24px !important;
            color: #FFA91A !important;
        }

        .concern-title {
            color: #FFA91A;
            font-family: Roboto;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 32px; 
            padding-left:0px;
        }
    }

    .concern-content {
        .analysis-list {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .analysis-item {
            position: relative;
            padding-left: 20px;
            
            &::before {
                content: "•";
                position: absolute;
                left: 0;
                top: 0;
                color: #FFA91A;
                font-weight: bold;
                font-size: 16px;
                line-height: 24px;
            }
        }

        .concern-subtitle {
            color:  rgba(37, 56, 88, 0.89);
            font-family: Roboto;
            font-size: 18px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px; 
            padding-left:0px;
            margin-bottom: 8px;
        }

        .concern-description {
            color: rgba(37, 56, 88, 0.89);
            font-family: Roboto;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px;
            cursor: pointer;
            transition: color 0.2s ease;

            &:hover {
                color: rgba(37, 56, 88, 1);
            }

            strong {
                font-weight: 600;
            }
        }

        .no-concerns {
            color: rgba(37, 56, 88, 0.6);
            font-family: Roboto;
            font-size: 16px;
            font-style: italic;
            font-weight: 400;
            line-height: 24px;
            text-align: center;
            padding: 20px;
        }
    }
}

/* Modal Popup Overrides */
.pitchRecommendationPopup {
    .MuiDialog-paper {
        border-radius: 8px;
        overflow: hidden;
        width: 600px;
        margin: 16px;
    }

    .MuiDialogTitle-root {
        display: none !important;
        padding: 0 !important;
        margin: 0 !important;
        height: 0 !important;
    }

    .MuiIconButton-root:not(.custom-close-button) {
        display: none !important;
    }
}

.custom-tooltip {
    background-color: rgba(37, 56, 88, 0.95) !important;
    color: white !important;
    font-family: Roboto !important;
    font-size: 14px !important;
    font-weight: 400 !important;
    line-height: 20px !important;
    max-width: 400px !important;
    border-radius: 6px !important;
    padding: 12px 16px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    
    .MuiTooltip-arrow {
        color: rgba(37, 56, 88, 0.95) !important;
    }
}

/* Responsive Design */
@media (max-width: 600px) {
    .pitch-popup-container {
        min-width: 320px;
        max-width: 90vw;
    }

    .pitch-header {
        padding: 20px;

        .pitch-title {
            font-size: 20px !important;
        }

        .pitch-content {
            font-size: 14px !important;
        }

        .recommendations-list {
            margin-top: 6px;
            gap: 4px;
        }

        .recommendation-item {
            padding: 6px 10px;

            .recommendation-text {
                font-size: 13px !important;
                line-height: 18px !important;
            }
        }
    }

    .customer-concern-section {
        padding: 16px 20px;

        .concern-header {
            .concern-title {
                font-size: 16px !important;
            }
        }

        .concern-content {
            .analysis-list {
                gap: 8px;
            }

            .analysis-item {
                padding-left: 16px;

                &::before {
                    font-size: 14px;
                    line-height: 20px;
                }
            }

            .concern-subtitle {
                font-size: 14px !important;
            }

            .concern-description {
                font-size: 13px !important;
                line-height: 20px !important;
            }

            .no-concerns {
                font-size: 14px !important;
                line-height: 20px !important;
                padding: 16px;
            }
        }
    }

    .custom-tooltip {
        max-width: 280px !important;
        font-size: 12px !important;
        line-height: 18px !important;
        padding: 10px 12px !important;
    }
}