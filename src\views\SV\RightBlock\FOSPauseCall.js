import React, { useEffect, useState } from "react";
import { CALL_API } from "../../../services";
import { Badge, Button, CircularProgress, Tooltip } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from "notistack";
import ModalPopup from "../../../components/Dialogs/ModalPopup";
import { Close } from "@mui/icons-material";
import HandshakeIcon from '@mui/icons-material/Handshake';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import { setRefreshAgentStats } from "../../../store/actions";
const FOSPauseCall = () => {
    const [showPauseCallingPopup, setShowPauseCallingPopup] = useState(false);
    const [showResumeCallTooltip, setShowResumeCallTooltip] = useState(true);
    const [ongoingApptData, setOngoingApptData] = useState([]);
    const [showLoading, setShowLoading] = useState(true);
    const { enqueueSnackbar } = useSnackbar();

    const dispatch = useDispatch();

    const AgentStats = useSelector(state => state.salesview.AgentStats);

    const IsOnAppt = Array.isArray(AgentStats) && AgentStats.length > 0 && AgentStats[0].status && AgentStats[0].status.toUpperCase() === "FOS_APPOINTMENT"

    useEffect(() => {
        if (showLoading) {
            setShowLoading(false);
        }
    }, [AgentStats])

    const PauseFosSelfCalling = () => {

        const _input = {
            url: `api/SalesView/PauseFosSelfCalling`,
            method: 'POST',
            service: 'MatrixCoreAPI',
            requestData: {}
        }
        CALL_API(_input).then((result) => {
            if (result && Array.isArray(result) && result.length > 0) {
                setShowLoading(true);
                setOngoingApptData(result[0]);
            } else {
                enqueueSnackbar("Oops! It looks like you don't have an ongoing appointment right now.\n Please start an appointment to pause calls.", {
                    variant: 'error',
                    autoHideDuration: 3000,
                    style:{ whiteSpace:"pre-line" }
                });
                setShowLoading(false);
                setOngoingApptData([]);
            }
        }).catch(() => {
            setShowLoading(false);
            setOngoingApptData([]);
            enqueueSnackbar("Something went wrong, Please try again", {
                variant: 'error',
                autoHideDuration: 3000,
            });
        }).finally(() => {
            setShowPauseCallingPopup(false);
            dispatch(setRefreshAgentStats({ RefreshAgentStats: true }));
        });
    }

    const ResumeFosSelfCalling = () => {

        setShowLoading(true); 
        const input = {
            url: `api/SalesView/ResumeFosSelfCalling`,
            method: 'POST',
            service: 'MatrixCoreAPI',
            requestData: {}
        };
        CALL_API(input).then((res) => {
            dispatch(setRefreshAgentStats({ RefreshAgentStats: true }));
        });
    }

    return (
        <>
            <Tooltip
                title={
                    <>
                        <span className="heading">
                            <h3>Pause Calls During Appointment</h3>
                        </span>
                        <p>You can now pause calls during an appointment without affecting your daily pause time. Click 'Pause' when you start an appointment.</p>
                    </>
                }
                classes={{ tooltip: "PauseFOSCallTooltip" }}
                arrow

            >
                <Button
                    variant="contained"
                    color="secondary"
                    style={{ display: IsOnAppt || showLoading ? "none" : "block" }}
                    onClick={() => setShowPauseCallingPopup(true)}
                    className="PlayPauseBtn"
                >
                    <Badge
                            badgeContent="new" 
                            color="error" 
                            
                    >
                        <HandshakeIcon />
                        <PauseIcon />
                    </Badge>
                </Button>
            </Tooltip>

            {IsOnAppt && !showLoading ?
                <Tooltip
                    title={
                        <>
                            <span className="heading">
                                <h3>Calling is paused</h3>
                                <span onClick={() => setShowResumeCallTooltip(false)}><Close /></span>
                            </span>
                            <p>Click 'Play' to resume calling during your appointment or once it has ended.</p>
                        </>
                    }
                    classes={{ tooltip: "ResumeFOSCallTooltip" }}
                    open={showResumeCallTooltip}
                    arrow
                >
                    <Button
                        variant="contained"
                        color="secondary"
                        className="PlayPauseBtn"
                        onClick={ResumeFosSelfCalling}
                    >
                        <Badge
                            badgeContent="new" 
                            color="error" 
                            
                        >
                            <HandshakeIcon />
                            <PlayArrowIcon />
                        </Badge>
                    </Button>
                </Tooltip>
                : null
            }

            {showLoading &&
               <Button
                    variant="contained"
                    color="secondary"
                    className="PlayPauseBtn"
                >
                    <Badge
                        badgeContent="new" 
                        color="error"  
                    >
                        <HandshakeIcon />
                        <CircularProgress size="20px" color="inherit"/>
                    </Badge>
                </Button>
            }

            <PauseCallingPopup
                open={showPauseCallingPopup}
                handleClose={() => setShowPauseCallingPopup(false)}
                PauseFosSelfCalling={() => PauseFosSelfCalling()}
            />
        </>
    )
}

const PauseCallingPopup = (props) => {
    return (
        <ModalPopup
            className="pauseCallingPopup"
            title="Your Calling will be paused"
            open={props.open}
            handleClose={props.handleClose}
        >
            <p>Click 'Pause' to stop dialing. Calls will only pause during the appointment only.</p>
            <Button
                variant="contained"
                color="secondary"
                className="pauseCallingBtn"
                onClick={props.PauseFosSelfCalling}
            >
                Pause Calling
            </Button>
        </ModalPopup>
    )
}

export default FOSPauseCall;
