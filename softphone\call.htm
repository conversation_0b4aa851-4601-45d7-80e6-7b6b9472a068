<!DOCTYPE html>
<html>
<!-- head -->

<head>
    <!-- Google analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-QRQXNSV2X6"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
    
        gtag('config', 'G-QRQXNSV2X6');
    </script>
    <!-- Google analytics end -->
    <script type="text/javascript" src="./assets/js/jquery.js"></script>
    <script src="SIPml-api.js?svn=252" type="text/javascript"></script>

    <script src="../../Content/js/appconfig.js?v=0.3" type="text/javascript"></script>
    <script src="call.js?v=5.1" type="text/javascript"></script>
    <!-- Styles -->
    <link href="./assets/css/bootstrap.css" rel="stylesheet" />
    <style type="text/css">
        .displaynone {
            display: none !important;
        }
        
        .display_none {
            /*display:none;*/
        }
        
        body {
            padding-top: 0;
            padding-bottom: 0;
            background-color: initial;
        }
        
        . {
            padding: 5px;
            background-color: #f0f0f0;
            border: 1px solid #eee;
            border: 1px solid rgba(0, 0, 0, 0.08);
            -webkit-border-radius: 4px;
            -moz-border-radius: 4px;
            border-radius: 4px;
            -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
            -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
            box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
            -webkit-transition-property: opacity;
            -moz-transition-property: opacity;
            -o-transition-property: opacity;
            -webkit-transition-duration: 2s;
            -moz-transition-duration: 2s;
            -o-transition-duration: 2s;
            opacity: 1;
            margin-top: 0px;
            text-align: center;
            background: #ddd;
            border: none;
            border-radius: 0px 0px 5px 5px;
            position: relative;
        }
        
        .tab-video,
        .div-video {
            width: 100%;
            height: 0px;
            -webkit-transition-property: height;
            -moz-transition-property: height;
            -o-transition-property: height;
            -webkit-transition-duration: 2s;
            -moz-transition-duration: 2s;
            -o-transition-duration: 2s;
        }
        
        .label-align {
            display: block;
            padding-left: 15px;
            text-indent: -15px;
        }
        
        .input-align {
            width: 13px;
            height: 13px;
            padding: 0;
            margin: 0;
            vertical-align: bottom;
            position: relative;
            top: -1px;
            *overflow: hidden;
        }
        
        .glass-panel {
            z-index: 99;
            position: fixed;
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            top: 0;
            left: 0;
            opacity: 0.8;
            background-color: Gray;
        }
        
        .div-keypad {
            z-index: 100;
            position: fixed;
            -moz-transition-property: left top;
            -o-transition-property: left top;
            -webkit-transition-duration: 2s;
            -moz-transition-duration: 2s;
            -o-transition-duration: 2s;
        }
        
        .previewvideo {
            position: absolute;
            width: 88px;
            height: 72px;
            margin-top: -42px;
        }
        
        #divCallCtrl {
            display: table-cell;
            vertical-align: middle;
            margin: 0;
            width: 380px !important;
            background: #dfeaea;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .container {
            margin: 0;
            width: 380px !important;
            height: 120px !important;
        }
        
        .row-fluid {
            font-size: 20px;
            text-align: center;
            margin-bottom: 5px;
            padding: 5px;
            color: #928686;
        }
        
        #txtRegStatus {
            font-size: 13px;
            display: inline-block;
            color: #a09595;
        }
        
        #txtCallStatus {
            font-size: 13px;
            display: inline-block;
            color: #8473c5;
            font-weight: 700;
        }
        
        .call_head {
            margin-bottom: 2px;
            padding: 14px 0px 0px 0px;
            color: #857ebb;
        }
        
        .call-options {
            padding: 5px 5px;
            background: #ded4d4;
            border-radius: 0 0 5px 5px;
            position: relative;
        }
        
        #btnCall {
            background: #1ead1e;
        }
        
        #btnHangUp {
            background: #ff4d4d;
        }
        
        .blink_label {
            animation: blink-animation 1s steps(5, start) infinite;
            -webkit-animation: blink-animation 1s steps(5, start) infinite;
        }
        
        @keyframes blink-animation {
            to {
                visibility: hidden;
            }
        }
        
        @-webkit-keyframes blink-animation {
            to {
                visibility: hidden;
            }
        }
        .modal.fade.in {
           top: 100%;
           width: auto;
        }
    </style>
    <link href="./assets/css/bootstrap-responsive.css" rel="stylesheet" />
    <!-- Le fav and touch icons -->
    <link rel="shortcut icon" href="./assets/ico/favicon.ico" />
    <link rel="apple-touch-icon-precomposed" sizes="114x114" href="./assets/ico/apple-touch-icon-114-precomposed.png" />
    <link rel="apple-touch-icon-precomposed" sizes="72x72" href="./assets/ico/apple-touch-icon-72-precomposed.png" />
    <link rel="apple-touch-icon-precomposed" href="./assets/ico/apple-touch-icon-57-precomposed.png" />

    <!-- Javascript code -->


</head>

<body>
    <div class="container">
        <div class="row-fluid">
            <div class="row-fluid displaynone">
                <label align="center" id="txtInfo"> </label>
                <input type='checkbox' id='cbVideoDisable' checked="checked" />
                <input type='checkbox' id='cbRTCWebBreaker' />
                <input type="text" style="width: 100%; height: 100%" id="txtWebsocketServerUrl" value="" placeholder="e.g. ws://sipml5.org:5062" />
                <input type="text" style="width: 100%; height: 100%" id="txtSIPOutboundProxyUrl" value="" placeholder="e.g. udp://sipml5.org:5060" />
                <input type="text" style="width: 100%; height: 100%" id="txtIceServers" value="" placeholder="e.g. [{ url: 'stun:stun.l.google.com:19302'}, { url:'turn:<EMAIL>', credential:'myPassword'}]" />
                <input type="text" style="width: 100%; height: 100%" id="txtBandwidth" value="" placeholder="{ audio:64, video:512 }" />
                <input type="text" style="width: 100%; height: 100%" id="txtSizeVideo" value="" placeholder="{ minWidth: 640, minHeight:480, maxWidth: 640, maxHeight:480 }" />
                <input type='checkbox' id='cbEarlyIMS' checked="checked" />
                <input type='checkbox' id='cbDebugMessages' checked="checked" />
                <input type='checkbox' id='cbCacheMediaStream' checked="checked" />
                <input type='checkbox' id='cbCallButtonOptions' />
                <input type="button" class="btn-success" id="btnSave" value="Save" onclick='settingsSave();' />
                <input type="button" class="btn-danger" id="btnRevert" value="Revert" onclick='settingsRevert();' />

                <input type="text" style="width: 100%; height: 100%" id="txtDisplayName" value="" placeholder="e.g. John Doe" />
                <input type="text" style="width: 100%; height: 100%" id="txtPrivateIdentity" value="" placeholder="e.g. +33600000000" />
                <input type="text" style="width: 100%; height: 100%" id="txtPublicIdentity" value="" placeholder="e.g. sip:+<EMAIL>" />
                <input type="password" style="width: 100%; height: 100%" id="txtPassword" value="" />
                <input type="text" style="width: 100%; height: 100%" id="txtRealm" value="" placeholder="e.g. doubango.org" />
                <input type="button" class="btn btn-success" id="btnRegister" value="LogIn" disabled onclick='sipRegister();' />
                <input type="button" class="btn btn-danger" id="btnUnRegister" value="LogOut" disabled onclick='sipUnRegister();' />
                <input type="text" style="width: 100%; height:100%;" id="txtPhoneNumber" value="6457409210974031" placeholder="Enter phone number to call" />
                <table style='width: 100%;'>
                    <tr>
                        <td id="tdVideo" class='tab-video'>
                            <div id="divVideo" class='div-video'>
                                <div id="divVideoRemote" style='position:relative; border:1px solid #009; height:100%; width:100%; z-index: auto; opacity: 1'>
                                    <video class="video" width="100%" height="100%" id="video_remote" autoplay="autoplay" style="opacity: 0;
                                               background-color: #000000; -webkit-transition-property: opacity; -webkit-transition-duration: 2s;"></video>
                                </div>

                                <div id="divVideoLocalWrapper" style="margin-left: 0px; border:0px solid #009; z-index: 1000">
                                    <iframe class="previewvideo" style="border:0px solid #009; z-index: 1000"> </iframe>
                                    <div id="divVideoLocal" class="previewvideo" style=' border:0px solid #009; z-index: 1000'>
                                        <video class="video" width="100%" height="100%" id="video_local" autoplay="autoplay" muted="true" style="opacity: 0;
                                                   background-color: #000000; -webkit-transition-property: opacity;
                                                   -webkit-transition-duration: 2s;"></video>
                                    </div>
                                </div>
                                <div id="divScreencastLocalWrapper" style="margin-left: 90px; border:0px solid #009; z-index: 1000">
                                    <iframe class="previewvideo" style="border:0px solid #009; z-index: 1000"> </iframe>
                                    <div id="divScreencastLocal" class="previewvideo" style=' border:0px solid #009; z-index: 1000'>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>

            <div id="divCallCtrl" class="span12 ">
                <div class="row-fluid call_head" style="font-size: 20px; text-align: center; margin-bottom: 10px;color: #a297ce;">PB Webphone</div>
                <div class="row-fluid" style="margin-bottom:10px; text-align: center;">
                    <label style="/*width: 59%; float:left;*/  font-size:13px; display:inline-block;" id="txtRegStatus"></label>
                    <label style="/*width: 40%; float:right;*/ font-size:13px; display:inline-block;" id="txtCallStatus" class="blink_label"></label>
                    <label align="center" id="leadInfo"></label>
                </div>
                <div class="btn-toolbar display_none" id="callButtonTrId" style="text-align:center;">
                    <div id="divBtnCallGroup" class="btn-group">
                        <button id="btnCall" disabled class="btn btn-primary">Call</button>
                    </div>&nbsp;&nbsp;
                    <div class="btn-group">
                        <input type="button" id="btnHangUp" style="margin: 0; vertical-align:middle; height: 100%;" class="btn btn-primary" value="HangUp" onclick='sipHangUp();' disabled />&nbsp;&nbsp;&nbsp;
                        <input type="button" id="btnFeedBack" style="margin: 0; vertical-align:middle; height: 100%;display: none;" class="btn btn-primary" value="Cust Feedback" onclick='CustFeedback();' />&nbsp;&nbsp;&nbsp;
                        <input type="button" id="btnMuteRemote" style="margin: 0; vertical-align:middle; height: 100%;" class="btn btn-primary" value="Mute Remote" onclick='toggleMicrophone();' /> &nbsp;
                    </div>
                </div>
                <div id='divCallOptions' class='call-options' style='opacity: 0; margin-top: 0px; text-align:center;'>
                    <input type="button" class="btn displaynone" style="" id="btnFullScreen" value="FullScreen" disabled onclick='toggleFullScreen();' /> &nbsp;
                    <input type="button" class="btn" style="color: #a98a8a;" id="btnMute" value="Mute" onclick='sipToggleMute();' /> &nbsp;

                    <input type="button" class="btn" style="color: #9e7ba5;" id="btnHoldResume" value="Hold" onclick='sipToggleHoldResume();' /> &nbsp;

                    <input type="button" class="btn" style="color: #727ab7; display: none;" id="btnTransfer" value="Transfer" onclick='sipTransfer();'  /> &nbsp;
                    <input type="button" class="btn displaynone" style="" id="btnKeyPad" value="KeyPad" onclick='openKeyPad();' />
                </div>
            </div>

        </div>
        <object id="fakePluginInstance" class="displaynone" classid="clsid:69E4A9D1-824C-40DA-9680-C7424A27B6A0" style="visibility:hidden;"> </object>
    </div>
    <!-- /container -->
    <!-- Glass Panel -->
    <div id='divGlassPanel' class='glass-panel displaynone' style='visibility:hidden'></div>
    <!-- KeyPad Div -->
    <div id='divKeyPad' class='span2 well div-keypad displaynone' style="left:0px; top:0px; width:250; height:240; visibility:hidden">
        <table style="width: 100%; height: 100%">
            <tr>
                <td><input type="button" style="width: 33%" class="btn" value="1" onclick="sipSendDTMF('1');" /><input type="button" style="width: 33%" class="btn" value="2" onclick="sipSendDTMF('2');" /><input type="button" style="width: 33%" class="btn"
                        value="3" onclick="sipSendDTMF('3');" /></td>
            </tr>
            <tr>
                <td><input type="button" style="width: 33%" class="btn" value="4" onclick="sipSendDTMF('4');" /><input type="button" style="width: 33%" class="btn" value="5" onclick="sipSendDTMF('5');" /><input type="button" style="width: 33%" class="btn"
                        value="6" onclick="sipSendDTMF('6');" /></td>
            </tr>
            <tr>
                <td><input type="button" style="width: 33%" class="btn" value="7" onclick="sipSendDTMF('7');" /><input type="button" style="width: 33%" class="btn" value="8" onclick="sipSendDTMF('8');" /><input type="button" style="width: 33%" class="btn"
                        value="9" onclick="sipSendDTMF('9');" /></td>
            </tr>
            <tr>
                <td><input type="button" style="width: 33%" class="btn" value="*" onclick="sipSendDTMF('*');" /><input type="button" style="width: 33%" class="btn" value="0" onclick="sipSendDTMF('0');" /><input type="button" style="width: 33%" class="btn"
                        value="#" onclick="sipSendDTMF('#');" /></td>
            </tr>
            <tr>
                <td colspan=3><input type="button" style="width: 100%" class="btn btn-medium btn-danger" value="close" onclick="closeKeyPad();" /></td>
            </tr>
        </table>
    </div>
    <!-- Call button options -->
    <ul id="ulCallOptions" class="dropdown-menu displaynone" style="visibility:hidden">
        <li><a href="#" onclick='sipCall("call-audio");'>Audio</a></li>
        <li><a href="#" onclick='sipCall("call-audiovideo");'>Video</a></li>
        <li id='liScreenShare'><a href="#" onclick='sipShareScreen();'>Screen Share</a></li>
        <li class="divider"></li>
        <li><a href="#" onclick='uiDisableCallOptions();'><b>Disable these options</b></a></li>
    </ul>

    <div class="modal fade" id="transferModal" role="dialog">
        <div class="modal-dialog" style="z-index: 9999;">

            <!-- Modal content-->
            <div class="modal-content">

                <div class="modal-body">
                    <div class="form-group">
                        <label for="transferMenu">Transfer To:</label>
                        <select id="transferMenu" class="form-control">
                            <option value="0">--Select--</option>
                            <optgroup>Car</optgroup>
                            <option value="*01" GroupId="137">Car</option>
                            <option value="*0111" GroupId="682">Car Renewal</option>                            
                            
                            <optgroup>Health</optgroup>
                            <option value="*02" GroupId="138">Health</option>
                            <option value="*0222" GroupId="514">Health Renewal</option>

                            <optgroup>Other</optgroup>
                            <option value="*04" GroupId="139">Investment</option>
                            <option value="*03" GroupId="140">Term</option>
                            <option value="*05" GroupId="521">Travel</option>
                            <option value="*07" GroupId="523">Two Wheeler</option>                            
                        </select>
                    </div>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" data-dismiss="modal"><i class="fa fa-times"></i> Cancel</button>
                </div>
            </div>

        </div>
    </div>

    <!-- Le javascript
    ================================================== -->
    <!-- Placed at the end of the document so the pages load faster -->

    <script type="text/javascript" src="./assets/js/bootstrap-transition.js"></script>
    <script type="text/javascript" src="./assets/js/bootstrap-alert.js"></script>
    <script type="text/javascript" src="./assets/js/bootstrap-modal.js"></script>
    <script type="text/javascript" src="./assets/js/bootstrap-dropdown.js"></script>
    <script type="text/javascript" src="./assets/js/bootstrap-scrollspy.js"></script>
    <script type="text/javascript" src="./assets/js/bootstrap-tab.js"></script>
    <script type="text/javascript" src="./assets/js/bootstrap-tooltip.js"></script>
    <script type="text/javascript" src="./assets/js/bootstrap-popover.js"></script>
    <script type="text/javascript" src="./assets/js/bootstrap-button.js"></script>
    <script type="text/javascript" src="./assets/js/bootstrap-collapse.js"></script>
    <script type="text/javascript" src="./assets/js/bootstrap-carousel.js"></script>
    <script type="text/javascript" src="./assets/js/bootstrap-typeahead.js"></script>

    <!-- Audios -->
    <audio id="audio_remote" autoplay="autoplay"> </audio>
    <audio id="ringtone" loop src="sounds/ringtone.wav"> </audio>
    <audio id="ringbacktone" loop src="sounds/ringbacktone.wav"> </audio>
    <audio id="dtmfTone" src="sounds/dtmf.wav"> </audio>
</body>

</html>