import { LinearProgress } from "@mui/material";
import React, { Fragment, lazy } from "react";
import { Suspense } from "react";
import { Switch, Redirect } from "react-router-dom";
import { CONFIG } from "./appconfig";
import { RouteWithLayout } from "./components";
import { ErrorBoundary } from "./hoc";



const MinimalLayout = lazy(() => import(/*webpackChunkName: "MinimalLayout"*/ './layouts/Minimal'));
const SVLayout = lazy(() => import(/*webpackChunkName: "SVLayout"*/ './layouts/SV'));

const FOSComponent = lazy(() => import(/*webpackChunkName: "FOS"*/ './views/Features/FOS.js'));
const FOSCustomerComponent = lazy(() => import(/*webpackChunkName: "FOSCustomer"*/ './views/Features/FOSCustomer.js'));
const SalesView = lazy(() => import(/*webpackChunkName: "salesview"*/ './views/SV'));
const LeadContent = lazy(() => import(/*webpackChunkName: "salesview"*/ './views/SV/LeadContent.js'));
const LeadContentComponent = lazy(() => import(/*webpackChunkName: "salesview"*/ './views/Features/LeadContent/LeadContentComponent.js'));
const LeadView = lazy(() => import(/*webpackChunkName: "leadView"*/ './views/LeadView/LeadView'));
const LeadOnly = lazy(() => import(/*webpackChunkName: "leadView"*/ './views/LeadOnly/LeadOnly.js'));
const NotFoundView = lazy(() => import(/*webpackChunkName: "NotFound"*/ './views/NotFound'));
const RescheduledAppointment = lazy(() => import(/*webpackChunkName: "RescheduledAppointment"*/ './views/Features/FosHelpers/RescheduledAppointment'));
const QualityCriteriaComponent = lazy(() => import(/*webpackChunkName: "QualityCriteria"*/ './views/Features/QualityCriteria/QualityCriteria'));
const SmeQuoteHistoryComponent = lazy(() => import(/*webpackChunkName: "SmeQuoteHistory"*/ './views/Features/SmeQuoteHistory/SmeQuoteHistory'));
const SmeViewQuotesNewComponent = lazy(() => import(/*webpackChunkName: "ViewQuotes"*/ './views/Features/SmeViewQuotes/SmeViewQuotesNew'));
const CsatRatingComponent = lazy(() => import(/*webpackChunkName: "CSATRating"*/ './views/Features/CsatRating/CsatRating'))
const PbmeetVerificationComponent = lazy(() => import(/*webpackChunkName: "PbmeetVerificationComponent"*/ './views/Features/pbmeet/VideoCallPopupVerification.js'));

const SMELeadStatusExternal = lazy(() => import(/*webpackChunkName: "SMELeadStatusExternal"*/ './views/Features/SMELeadStatusExternal/SMELeadStatusExternal'));
const PaymentFailedCases = lazy(() => import(/*webpackChunkName: "PaymentFailedCases"*/ './layouts/SV/components/Sidebar/components/Modals/PaymentFailedCases'));
const PaymentAttemptLeads = lazy(() => import(/*webpackChunkName: "PaymentAttemptLeads"*/ './views/Features/PaymentAttemptLeads/PaymentAttemptLeads'));

const BusinessHealthRatingPopup = lazy(() => import(/*webpackChunkName: "BusinessHealthRatingPopup"*/ './views/SV/Main/BusinessHealthRatingPopup'));
const AddContactDetailsPanel = lazy(() => import(/*webpackChunkName: "AddContactDetailsPanel"*/ './views/SV/Header/Dialogs/AddDetailsContainer'));
const UpperHierarchyBizBaseData = lazy(() => import(/*webpackChunkName: "UpperHierarchyBizBaseData"*/ './views/SV/Main/UpperHierarchyBizBaseData'));
const DownLevelBizEnforcementData = lazy(() => import(/*webpackChunkName: "DownLevelBizEnforcementData"*/ './views/SV/Main/DownLevelBizEnforcementData'));
const AppointmentView = lazy(() => import(/*webpackChunkName: "AppointmentView"*/ './views/AppointmentView/AppointmentView'));
const CreateLeadComponent = lazy(() => import(/*webpackChunkName: "CreateLead"*/ './views/Features/CreateLead/CreateLead.js'));
const RescheduledAppointmentV2 = lazy(() => import(/*webpackChunkName: "RescheduledAppointmentV2"*/ './views/Features/FosHelpers/RescheduledAppointmentV2'));
const AppointmentHyperLocalRenewal = lazy(() => import(/*webpackChunkName: "AppointmentHyperLocalRenewal"*/ './views/Features/FosHelpers/AppointmentHyperLocalRenewal'));
const CreditChangeRequests = lazy(() => import(/*webpackChunkName: "CreditChangeRequests"*/ './views/SV/Main/CreditChange/CreditChangeRequests.js'));
const ViewTabularData = lazy(() => import(/*webpackChunkName: "ViewTabularData"*/ './views/SV/Main/ViewTabularData/ViewTabularData.js'));
const NewSVCalendar = lazy(() => import(/*webpackChunkName: "NewSVCalendar"*/ './views/SV/RightBar/NewSVCalendar/NewSVCalendar.js'));
/**
 * loadConfigs={['SVConfig']}, REQUIRED CONFIGS to Load Page
 */
const Routes = (props) => {
  return (
    <Suspense fallback={<div><LinearProgress color='secondary' /></div>}>
      <Switch>
        <RouteWithLayout
          component={() => { return <>{CONFIG.VERSION}</> }}
          exact
          layout={Fragment}
          path="/version"
        />
        <RouteWithLayout
          component={LeadView}
          exact
          layout={SVLayout}
          authType='VERIFY_TOKEN'
          hideSidebar
          path="/leadview/:EncryptSVURL/"
          loadConfigs={['SVConfig']}
        />
        <RouteWithLayout
          component={LeadOnly}
          exact
          layout={SVLayout}
          hideSidebar
          path="/LeadOnly/:EncryptSVURL/"
          loadConfigs={['SVConfig']}
        />
        <RouteWithLayout
          component={SalesView}
          exact
          layout={SVLayout}
          path="/salesview/:EncryptSVURL/:Inbound?"
          loadConfigs={['SVConfig']}
        />
        <RouteWithLayout
          component={LeadContent}
          exact
          layout={SVLayout}
          authType='VERIFY_TOKEN'
          path="/LeadContent/:EncryptSVURL/:Inbound?"
          loadConfigs={['SVConfig']}
        />
        <RouteWithLayout
          component={AppointmentView}
          exact
          layout={SVLayout}
          //authType='VERIFY_TOKEN'
          path="/apptview/:EncryptSVURL/:Inbound?"
          loadConfigs={['SVConfig']}
        />
        <RouteWithLayout
          component={LeadContentComponent}   // LeadContentComponent is used for auth
          exact
          layout={Fragment}
          path="/auth"
        //loadConfigs={['SVConfig']}
        />

        {/**
         * FOS route
         * EncryptSVURL:  window.btoa(CustomerId + '/' + ProductId + '/' + parentLeadId + '/' + UserId)
         * queryParams:
         *    view        : Boolean -> open page in viewonly mode
         *    src         : pageopening src ,   "" |  "referralPage" | "MatrixGoApp" | "CrossSell"
         *    referralId  : AES encrypted leadId which referred this lead
         *    referralProduct :  product of referring lead
         */}
        <RouteWithLayout
          component={FOSComponent}
          exact
          path="/FOS/:EncryptSVURL"
          layout={ErrorBoundary}
          loadConfigs={['SVConfig']}
        />
        {/**
         * Create Lead route
         * EncryptSVURL:  window.btoa(CustomerId + '/' + ProductId + '/' + parentLeadId + '/' + UserId + '/'+ LeadSource)       
         */}
        <RouteWithLayout
          component={CreateLeadComponent}
          exact
          path="/createlead/:EncryptSVURL"
          layout={Fragment}
          loadConfigs={['SVConfig']}
        />

        {/**
         * FOS route for customer
         * queryParams:
         *    src : pageopening src , "customerwhatsapp"
         *    l : AES encrypted leadId
         *    c:  AES encrypted CustomerId 
         *    p: productId against leadid for customer 
         *    t: token against leadid for customer 
         */}

        <RouteWithLayout
          component={FOSCustomerComponent}
          exact
          path="/fosAppointment"
          layout={ErrorBoundary}
        //loadConfigs={['SVConfig']}
        />
        <RouteWithLayout
          component={AppointmentHyperLocalRenewal}
          exact
          layout={Fragment}
          path="/fosAppointment/HealthRenewal"
          loadConfigs={['SVConfigCustomer']}
        />
        <RouteWithLayout
          component={PaymentFailedCases}
          exact
          layout={Fragment}
          path="/PaymentFailedCases"
          loadConfigs={['SVConfig']}
        />
        <RouteWithLayout
          component={QualityCriteriaComponent}
          exact
          layout={Fragment}
          path="/qualityCriterias/:productId"
        />
        <RouteWithLayout
          component={SmeQuoteHistoryComponent}
          exact
          layout={Fragment}
          path="/SmeQuoteHistory/:leadId/:productId"
          loadConfigs={['SVConfig']}
        />
        <RouteWithLayout
          component={SmeViewQuotesNewComponent}
          exact
          layout={Fragment}
          path="/ViewQuotes/:EncryptURL"
          loadConfigs={['SVConfig']}
        />
        <RouteWithLayout
          component={CsatRatingComponent}
          exact
          layout={Fragment}
          path="/CsatRating"
          loadConfigs={['SVConfig']}
        />
        {/* "/LeadStatus/:EncryptURL" -> currently not in use, was used in oldsv, code may be obsolete in SMELeadStatusExternal */}
        <RouteWithLayout
          component={SMELeadStatusExternal}
          exact
          layout={Fragment}
          path="/LeadStatus/:EncryptURL"
          //path="/LeadStatus/:LeadId/:ProductId/:CustomerId"
          loadConfigs={['SVConfig']}
        />

        {/**
         * FOS Reschdule route for customer
         * queryParams:
         *    src : pageopening src ,   "customerwhatsapp"
         *    l : AES encrypted leadId
         *    c:  AES encrypted CustomerId 
         *    p: productId against leadid for customer 
         *    t: token against leadid for customer 
         */}
        <RouteWithLayout
          component={RescheduledAppointment}
          exact
          layout={Fragment}
          path="/RescheduleAppointment"
          loadConfigs={['SVConfigCustomer']}
        />

        <RouteWithLayout
          component={RescheduledAppointmentV2}
          exact
          layout={Fragment}
          path="/RescheduleAppointmentV2"
          loadConfigs={['SVConfigCustomer']}
        />


        <RouteWithLayout
          component={PaymentAttemptLeads}
          exact
          layout={Fragment}
          path="/PaymentAttemptLeads"
          loadConfigs={['SVConfig']}
        />
        <RouteWithLayout
          component={BusinessHealthRatingPopup}
          exact
          layout={Fragment}
          path="/BusinessHealthRatingPopup"
          loadConfigs={['SVConfig']}
        />
        {/**
         * Add details route
         * EncryptURL:  window.btoa(CustomerId + '/' + ProductId + '/' + parentLeadId)
         * queryParams:
         *    process:  pageopening src ,   "" | "BMS"
         *    
         */}
        <RouteWithLayout
          component={AddContactDetailsPanel}
          exact
          layout={Fragment}
          path="/ContactDetailsPanel/:EncryptURL"
          loadConfigs={['SVConfig']}
        />
        <RouteWithLayout
          component={UpperHierarchyBizBaseData}
          exact
          layout={Fragment}
          path="/UpperHierarchyBizBaseData"
          loadConfigs={['SVConfig']}
        />
        <RouteWithLayout
          component={DownLevelBizEnforcementData}
          exact
          layout={Fragment}
          path="/DownLevelBizEnforcementData"
          loadConfigs={['SVConfig']}
        />
        {/**
         * Pbmeet verification call link generation route
         * EncryptURL:  window.btoa(CustomerId + '/' + ProductId + '/' + parentLeadId)
         * queryParams:
         *    src: "BMS"
         *    process: "verification"
         */}
        <RouteWithLayout
          component={PbmeetVerificationComponent}
          exact
          layout={Fragment}
          path="/pbmeet/:EncryptURL"
          loadConfigs={['SVConfig']}
        />
        <RouteWithLayout
          component={CreditChangeRequests}
          exact
          layout={Fragment}
          path="/CreditChangeRequests"
          loadConfigs={['SVConfig']}
        />
        <RouteWithLayout
          component={ViewTabularData}
          exact
          layout={Fragment}
          path="/ViewTabularData"
          loadConfigs={['SVConfig']}
        />
        <RouteWithLayout
          component={NewSVCalendar}
          exact
          layout={Fragment}
          path="/NewSVCalendar"
          loadConfigs={['SVConfig']}
        />
        <Redirect exact from="/" to="/salesview" />
        <RouteWithLayout
          component={NotFoundView}
          exact
          layout={MinimalLayout}
          path="**"
        />


      </Switch>
    </Suspense>
  );
};
export default Routes;