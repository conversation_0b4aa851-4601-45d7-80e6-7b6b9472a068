﻿var AgentCall = { url: "", popwin: null, options: "left=3000,top=1,width=5px,height=5px,menubar=0,fullscreen=0,location=0,titlebar=0,toolbar=0,status=0,scrollbars=0;dialog=1;" };
//var AgentCall = { url: "", popwin: null, options: "left=3000,top=1,dialogHeight: 190px; dialogWidth: 550px; titlebar: no; toolbar: no; statusbar:no;" };

function ShowCallWindow(empId) {

    try {
        if (empId == 0) {
            return;
        }

        var user = window.atob(window.localStorage.getItem('User'));
        user = JSON.parse(user);

        if (user.Asterisk_Url == "" || user.Asterisk_Url == null || user.Asterisk_Url == undefined)
            return;

        var pwd = (user.DialerPWD == undefined || user.DialerPWD == "") ? empId : user.DialerPWD;

        AgentCall.url = "../softphone/call.htm?empId=" + empId + "&ipAddress=" + user.Asterisk_Url + "&empPassword=" + pwd + "&custPhone=";



        var CallWindow = window.localStorage.getItem('CallWindow')

        if (CallWindow == "" || CallWindow == null || CallWindow == undefined) {
            AgentCall.popwin = window.open(AgentCall.url, "callbar", AgentCall.options);

        }
        else {
            var CallWindowTime = (new Date() - new Date(CallWindow)) / 1000;
            if ((CallWindowTime > 3 && !AgentCall.popwin) || ((AgentCall.popwin && AgentCall.popwin.closed))) {
                AgentCall.popwin = window.open(AgentCall.url, "callbar", AgentCall.options);

            }
            else if (AgentCall.popwin && AgentCall.popwin.closed == false) {
                //console.log("pop opened");
            }
            else {
                AgentCall.popwin = window.open("", "callbar", AgentCall.options);

            }
        }

        // if (!localStorage.getItem('sipPhone')) {
        // AgentCall.popwin = window.open(AgentCall.url, "sipPhone", AgentCall.options);

        // if(window.location.origin.indexOf("localhost") >-1){
        // localStorage.setItem('sipPhone','true');
        // }

        // return false;
        // } else {

        // }




    }
    catch (e) {

    }
}
function setCall() {

}


function ConnectCall(empId, Phone, data) {
    if (!checkforRegistraion()) {
        ShowCallWindow(empId);
        setTimeout(function () {
            if (AgentCall.popwin && AgentCall.popwin.ConnectCall && AgentCall.popwin.oSipSessionCall == null) {
                AgentCall.popwin.ConnectCall(Phone, data, empId);
            }
        }, 3000)
    }
    else {
        if (AgentCall.popwin && AgentCall.popwin.ConnectCall) {
            AgentCall.popwin.ConnectCall(Phone, data, empId);
        }
    }
    // if(AgentCall.popwin && AgentCall.popwin.oSipSessionCall == null){
    // AgentCall.popwin.ConnectCall(Phone,data,empId);
    // }
    //}
}



function toggleMicrophone() {
    try {
        AgentCall.popwin.toggleMicrophone();
    }
    catch (e) { }
}


function CallMute() {
    try {
        AgentCall.popwin.sipToggleMute();
    }
    catch (e) { }
}

function CallHold() {
    try {
        AgentCall.popwin.sipToggleHoldResume();
    }
    catch (e) { }
}

function CallHangup() {
    try {
        AgentCall.popwin.sipHangUp();
    }
    catch (e) { }
}

function CustFeedback() {
    //     try {
    //         AgentCall.popwin.CustFeedback();
    //     }
    //     catch (e) { }
}

function sipAccept(toOpenSVLead = true) {
    try {
        AgentCall.popwin.sipAccept(toOpenSVLead);
    }
    catch (e) { }

    try {
        hideAlert();
    }
    catch (e) { }

    try {
        hideVCIBAlert();
    }
    catch (e) { }
}
function showAlert(calltype) {
    let CallAlertDiv = document.querySelector(".CallAlert");
    let CallAlertTitle = document.querySelector(".CallAlertTitle");

    CallAlertDiv.classList.remove("hide");
    if (calltype == "IB_OB") {
        CallAlertTitle.innerText = "You are getting a OB call.";
    }
    else {
        CallAlertTitle.innerText = "You are getting a call.";
    }
}

function hideAlert() {
    let CallAlertDiv = document.querySelector(".CallAlert");
    CallAlertDiv.classList.add("hide");
}

const hideVCIBAlert = () => {
    let IBCallAlertDiv = document.querySelector(".IBVCCallAlert");
    IBCallAlertDiv.classList.add("hide")
}



document.addEventListener("DOMContentLoaded", function (event) {
    function onAlertKeypress(e) {
        let CallAlertDiv = document.querySelector(".CallAlert");
        if (e.keyCode == 13 && !CallAlertDiv.classList.contains('hide')) {
            sipAccept();
        }
    }
    document.addEventListener("keydown", onAlertKeypress);
});

function setHangup() {

}
// function logwindow(e){
//     console.log(e)
// }

function logoutCallWindow(e) {
    try {

        var user = window.atob(window.localStorage.getItem('User'));
        //Discuss with mohit
        // user = jQuery.parseJSON(user);

        //bindCallFrame(user.EmployeeId);
        setTimeout(function () {
            if (AgentCall && AgentCall.popwin && AgentCall.popwin.WindowLogout) {
                AgentCall.popwin.WindowLogout();
            }
            window.localStorage.removeItem('searchObj');
        }, 500);
    }

    catch (e) { }
}

function checkforRegistraion() {
    try {
        if (AgentCall.popwin.checkforRegistraion() == undefined) {
            return null;
        }
        else if (AgentCall.popwin.checkforRegistraion()) {

            return true;
        }
        return false;
    }
    catch (e) {
        return null;
    }
}

// function hideCallWindow() {
//     //$(document).focus();
// }

function IsWindowOpen(val) {
    sessionStorage.setItem("AgentCall", val);
    setCookie("AgentCall", val, 1);
}

function setCookie(cname, cvalue, exdays) {
    var d = new Date();
    d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
    var expires = "expires=" + d.toUTCString();
    document.cookie = cname + "=" + cvalue + ";" + expires + ";domain=.policybazaar.com;path=/";
}
function getCookie(cname) {
    var name = cname + "=";
    var decodedCookie = decodeURIComponent(document.cookie);
    var ca = decodedCookie.split(';');
    for (var i = 0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) == ' ') {
            c = c.substring(1);
        }
        if (c.indexOf(name) == 0) {
            return c.substring(name.length, c.length);
        }
    }
    return "";
}

// $(document).ready(function () {
// try{

// var user = window.atob(window.localStorage.getItem('User'));
// user = jQuery.parseJSON(user);
// //if(user){bindCallFrame(user.EmployeeId);}
// }catch(e){}
// })



// function handleMessage(obj) {
//     if (obj.data.message == "HangUp") {
//         hideCallWindow();

//         // To re-enable f5
//         /* jQuery < 1.7 */
//         $(document).unbind("keydown", disableF5);
//         /* OR jQuery >= 1.7 */
//         $(document).off("keydown", disableF5);
//     }
// }

// function bindCallFrame(empId) {



//     ShowCallWindow(empId);
// }

// slight update to account for browsers not supporting e.which
// function disableF5(e) { if ((e.which || e.keyCode) == 116) e.preventDefault(); };

//var timer = setInterval(checkChild, 1000);

// function checkChild() {
//     try{
//         if (AgentCall.popwin && AgentCall.popwin.closed) {
//             IsWindowOpen(0);
//             clearInterval(timer);
//         }
//     } catch (e) {

//     }
// }

function reloginAgent(reason) {
    if(reason == "micDisabled"){
        alert("You are logged out as the microphone is off");
    } else {
        alert("Session is already in progress for provided username, please logout and re-login to continue!.");
    }
}

function PredictiveCallAnswered() {
}
function queueLogin() {
}
function queueLogout() {
}

function IsMicrophoneEnabled() {
    return window.AgentCall
        && window.AgentCall.popwin
        && window.AgentCall.popwin.CheckMicroPhone
        && window.AgentCall.popwin.CheckMicroPhone();
}

function sendDTMF(key) {
    if(AgentCall.popwin){
        AgentCall.popwin.sipSendDTMF(key)
    }
}