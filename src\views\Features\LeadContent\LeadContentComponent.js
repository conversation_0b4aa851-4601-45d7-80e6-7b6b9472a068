import React, { useEffect, useState } from "react";
import LoaderComponent from "../../../components/Loader";
import { setCookie } from "../../../store/token-store/Token-Store";
import { CALL_API } from "../../../services";
import { MatrixCoreAPI } from "../../../appconfig";
import { gaEventTracker } from "../../../helpers";

const LeadContentComponent = (props) => {
  const [isLoading, setIsLoading] = useState(true);
  const [IsPageRefreshRequired, setIsPageRefreshRequired] = useState(false);
  const [IsUnauth, setIsUnAuth] = useState(false);
  const setuserinformation = (user, targeturl) => {
    try {
      //set basic token for api call authentication
      let MatrixToken = { UserId: user.UserId, AsteriskToken: user.AsteriskToken, EmployeeId: "", GroupId: 0 }
      MatrixToken = btoa(JSON.stringify(MatrixToken));
      setCookie('MatrixToken', MatrixToken, 0.25, '.policybazaar.com')
    }
    catch { }
    // API call 
    let userId = user.UserId;
    let AsteriskToken = user.AsteriskToken
    const input = {
      url: MatrixCoreAPI + `api/SalesView/GetUserInfov2/${userId}/${AsteriskToken}`,
      method: 'GET', service: 'custom', //'MatrixCoreAPI',
      Headers: {
        "Content-Type": "application/json",
        AgentId: user.UserId,
        Token: user.AsteriskToken
      },
      timeout: 6000

    };

    let temp_res = -1;
    let temp_status = -1;
    CALL_API(input).then((response) => {
      if (response && response.StatusCode === 200) {
        window.localStorage.setItem('User', response.User)
        window.localStorage.setItem('AsteriskToken', AsteriskToken)
        window.localStorage.setItem('mtoken', 'cG9saWN5 YmF6YWFy')
        let decodeuser = atob(response.User);
        let user = JSON.parse(decodeuser);
        user['ProductList'] = [];
        user['UserGroupList'] = [];
        user['GroupList'] = [];
        user = JSON.stringify(user);
        user = btoa(user);
        setCookie('User', user, 0.25, '.policybazaar.com');
        setCookie('AsteriskToken', AsteriskToken, 0.25, '.policybazaar.com');
        setCookie('AgentId', userId, 0.25, '.policybazaar.com')

        try {
          decodeuser = JSON.parse(decodeuser);
          let MatrixToken = { UserId: userId, AsteriskToken, EmployeeId: decodeuser.EmployeeId, GroupId: decodeuser.GroupId }
          MatrixToken = btoa(JSON.stringify(MatrixToken));
          setCookie('MatrixToken', MatrixToken, 0.25, '.policybazaar.com')
          window.localStorage.setItem('MatrixToken', MatrixToken)
        }
        catch { }
        window.location.href = targeturl;
      }
      else {
        setIsUnAuth(true);
        setIsLoading(false);
        setIsPageRefreshRequired(false);
        temp_res = response;
      }

    }).catch((err) => {
      temp_status = err.status;
      temp_res = err.response ? err.response : err.request;
      if (err.status === 401) {
        setIsUnAuth(true);
        setIsLoading(false);
        setIsPageRefreshRequired(false);
      }
      else {
        setIsPageRefreshRequired(true);
        setIsLoading(false);
        setIsUnAuth(false);
      }

    }).finally(() => {
      // todo: remove these logs
      if (temp_res != -1) {
        try {
          gaEventTracker("AUTHPAGE-FAIL-2", JSON.stringify(temp_res), temp_status)
          gaEventTracker("AUTHPAGE-FAIL-1", JSON.stringify({ response: temp_res, input: input }), temp_status)
        } catch (e) {

        }
      }
    });

  }

  const getUserInformation = () => {
    let url_string = window.location.href;
    let url = new URL(url_string);
    let user = JSON.parse(atob(url.searchParams.get("u")));
    let targeturl = atob(url.searchParams.get("t"));
    setuserinformation(user, targeturl);
  }

  useEffect(() => {
    getUserInformation();
  }, []);

  return (
    <div className="AuthPage">
      {isLoading && <LoaderComponent open={true} />}
      {IsPageRefreshRequired && <>
        <h4 className="NoAuthorized"> Please refresh after sometime! </h4>
        <button onClick={()=>{window.location.reload()}} className="backToMatrixGoHomme">
          Click here to Refresh
        </button>
      </>}
      {IsUnauth && <>
        <h4 className="NoAuthorized">You are not authorised to view this page, Please Relogin!</h4>
        <button id="fos_home" className="backToMatrixGoHomme">
          Go to Home
        </button>
      </>
      }
    </div>
  );
}
export default LeadContentComponent;