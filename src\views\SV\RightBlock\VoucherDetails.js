import { Switch } from "@mui/material";
import { ExpandMore } from "@mui/icons-material";
import { useSnackbar } from "notistack";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import SelectDropdown from "../../../components/SelectDropdown";
import { CALL_API } from "../../../services/api.service";
import { SetCustomerComment, SetLeadAudit } from "../../../services/Common";
import rootScopeService from "../../../services/rootScopeService";
import User from "../../../services/user.service";
import ModalPopup from '../../../../src/components/Dialogs/ModalPopup';


let VoucherInfo = {
    PharmacyDiscount: 0
};

const VoucherDetails = () => {
    const { enqueueSnackbar } = useSnackbar();
    const [show, setShow] = useState(false);
    const [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);

    const [allLeads] = useSelector(state => {
        let { allLeads } = state.salesview;
        return [allLeads]
    });
    const [originalInfo, setOriginalInfo] = useState({});
    const [NewVoucherInfo, setNewVoucherInfo] = useState(VoucherInfo);
    const [ActiveLead, setActiveLead] = useState(0);
    const [IsVoucherAlreadyGiven, setIsVoucherAlreadyGiven] = useState(false);
    const [IsConfirmationPopupShow, setIsConfirmationPopupShow] = useState(false);

    useEffect(() => {
        if (RefreshLead) {
            setShow(false);
        }
    }, [RefreshLead]);

    useEffect(() => {
        if (ActiveLead != 0)
            getVoucherInfo();
    }, [ActiveLead]);


    const visibleLeads = [];
    const AlreadyAppliedVoucherLeads = [];
    let ApplicablePlan = ['Care Supreme', 'Care Supreme Direct', 'caresupremeplan'];
    for (let key in allLeads) {
        var PlanName = allLeads[key].CustomerSelection !== undefined ? allLeads[key].CustomerSelection.PlanName : "";
        if (allLeads[key].LeadSource !== "Renewal" &&
            allLeads[key].StatusId >= 13 &&
            allLeads[key].StatusMode === "P" &&
            ApplicablePlan.includes(PlanName) &&
            allLeads[key].PostBookingSumInsured >= 1000000 &&
            allLeads[key].BookingSalesAgent === User.UserId) {
            visibleLeads.push(allLeads[key].LeadID);
            if (allLeads[key].IsPharmacyVoucherAvailed == true) {
                AlreadyAppliedVoucherLeads.push(allLeads[key].LeadID);
            }
        }
    }

    const handleToggle = (e) => {
        setShow(!show);
        if (visibleLeads.length > 0) {
            setActiveLead(visibleLeads[0]);
            getVoucherInfo();
        }
    }

    const handleChange = (e) => {
        const { name, value, type } = e.target;
        if (name === "ActiveLead") {
            setActiveLead(value);
            AlreadyAppliedVoucherLeads.includes(value) ? setIsVoucherAlreadyGiven(true) : setIsVoucherAlreadyGiven(false);
        }
        else {
            setNewVoucherInfo({ ...NewVoucherInfo, [name]: !NewVoucherInfo[name] });
        }
    }

    const getVoucherInfo = () => {
        const input = {
            url: `coremrs/api/LeadDetails/GetHealthNeedAnalysis/` + rootScopeService.getCustomerId() + '/' + ActiveLead + '/2',
            method: 'GET', service: 'MatrixCoreAPI'
        };
        CALL_API(input).then((response) => {
            response = response.Data;
            if (response) {
                setNewVoucherInfo({
                    PharmacyDiscount: response.PharmacyDiscount
                });
                setOriginalInfo({
                    PharmacyDiscount: response.PharmacyDiscount
                });
                if (response.PharmacyDiscount == true) {
                    AlreadyAppliedVoucherLeads.push(ActiveLead);
                    setIsVoucherAlreadyGiven(true);
                }
            }
            else setNewVoucherInfo(VoucherInfo);
        }, function () {
            setNewVoucherInfo(VoucherInfo);
        });
    };

    const UpdateVoucherInfo = () => {
        setIsConfirmationPopupShow(false);
        const reqData = {
            CustomerId: rootScopeService.getCustomerId(),
            ParentId: ActiveLead,
            RenewalFlag: 2,
            PharmacyDiscount: NewVoucherInfo.PharmacyDiscount
        };
        setHealthRenewalNeedAnalysisService(reqData).then(function (resultData) {
            if (resultData) {
                SetLeadAudits(NewVoucherInfo, originalInfo);
                getVoucherInfo();
                SaveComment("Opted for Pharmacy Voucher.");
                enqueueSnackbar("Details Updated Successfully.", {
                    variant: 'success',
                    autoHideDuration: 3000,
                });
                if (NewVoucherInfo.PharmacyDiscount == true) {
                    const BMSreqData = {
                        BookingId: ActiveLead,
                        OfferId: 1,
                        AvailedVoucher: "PharmacyDiscount",
                    };
                    PushVoucherdatatoBMS(BMSreqData).then(function (resData) {
                        if (resData.IsSuccess == true) {
                            enqueueSnackbar("Voucher Details successfully sent.", {
                                variant: 'success',
                                autoHideDuration: 3000,
                            });
                        }
                    }, function () {
                        enqueueSnackbar('Error in sending voucher details.', {
                            variant: 'error',
                            autoHideDuration: 3000,
                        });
                    });
                }
            }
        }, function () {
            enqueueSnackbar('Something went wrong, Please Connect the Support team.', {
                variant: 'error',
                autoHideDuration: 3000,
            });
        });
    }

    const GetConfirmationResponse = () => {
        if (NewVoucherInfo.PharmacyDiscount === true && NewVoucherInfo.PharmacyDiscount != originalInfo.PharmacyDiscount) {
            setIsConfirmationPopupShow(true);
        }
        else {
            UpdateVoucherInfo();
        }
    }

    const SaveComment = (Comment) => {
        let UserId = User.UserId;
        var requestData = {
            "CustomerId": rootScopeService.getCustomerId(),
            "ProductId": rootScopeService.getProductId(),
            "ParentLeadId": ActiveLead,
            ActiveLead,
            UserId,
            "Comment": Comment,
            "EventType": 61
        };
        SetCustomerComment(requestData);
    }

    const SetLeadAudits = (NewInfo, oldInfo) => {
        let FieldsData = require("../../../assets/json/FieldsData").default;
        let reqDataAudit = [];
        let invalid = [undefined, null, 0, "", "0"];

        for (const [key, vdata] of Object.entries(NewInfo)) {
            var fieldVal = FieldsData.Field[key];
            if (vdata != oldInfo[key] && fieldVal !== undefined && !(invalid.indexOf(vdata) >= 0 && invalid.indexOf(oldInfo[key]) >= 0)) {
                reqDataAudit.push({
                    LeadId: ActiveLead,
                    AgentId: User.UserId || 90368,
                    SectionName: "VoucherDetails",
                    Field: fieldVal,
                    OldValue: oldInfo[key],
                    NewValue: vdata,
                    ProductId: rootScopeService.getProductId()
                });
            }
        }
        if (reqDataAudit.length > 0) {
            SetLeadAudit(reqDataAudit).then((result) => {
                enqueueSnackbar("Lead Audit Details Saved", { variant: 'success', autoHideDuration: 3000, });
            }, function () {
                console.log("Lead Audit not saved");
            })

        }
    }


    const setHealthRenewalNeedAnalysisService = (reqData) => {
        const input = {
            url: `coremrs/api/LeadDetails/SetHealthNeedAnalysis/`,
            method: 'POST', service: 'MatrixCoreAPI',
            requestData: reqData
        };
        return CALL_API(input);
    }

    const PushVoucherdatatoBMS = (reqData) => {
        const input = {
            url: `coremrs/api/BMS/PushVoucherDetailstoBMS`,
            method: 'POST', service: 'MatrixCoreAPI',
            requestData: reqData
        };
        return CALL_API(input);
    }

    const handleClose = () => {
        setIsConfirmationPopupShow(false);
    }


    return <>
        <div className="addInfo">
            <h3>Voucher Details</h3>
            <div className="expandmoreIcon">
                <ExpandMore onClick={handleToggle} style={{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)' }} /></div>
            <p className="caption">Applicable vouchers on booked Leads</p>
            {show && visibleLeads.length > 0 &&
                <>
                    <SelectDropdown
                        name="ActiveLead"
                        label="Active Lead"
                        value={ActiveLead}
                        options={visibleLeads}
                        labelKeyInOptions='_all'
                        valueKeyInOptions="_all"
                        handleChange={handleChange}
                        sm={12} md={12} xs={12}
                    />
                    {IsVoucherAlreadyGiven &&
                        <p style={{ color: 'red' }}>PharmEasy Voucher already been given on this LeadID.</p>
                    }
                    <div className="Additional-details">
                        <div>PharmEasy Discount</div>
                        <div>
                            {NewVoucherInfo.PharmacyDiscount == true ? "Yes" : "No"}
                            <Switch
                                checked={!!NewVoucherInfo.PharmacyDiscount}
                                onChange={handleChange}
                                name="PharmacyDiscount"
                                disabled={IsVoucherAlreadyGiven}
                            />
                        </div>
                    </div>
                    <button
                        className={IsVoucherAlreadyGiven ? "disabledBtn" : "submitBtn"}
                        onClick={GetConfirmationResponse}
                        disabled={IsVoucherAlreadyGiven}>
                        Submit
                    </button>

                </>
            }
            {show && visibleLeads.length <= 0 && <>
                <h4>No Data Found</h4>
            </>
            }
            {IsConfirmationPopupShow &&
                <ModalPopup open={IsConfirmationPopupShow} className="EMIPendingPopup" handleClose={handleClose}>
                    <div className="comboPolicyPopup">
                        <h4 style={{ color: 'black' }}>Make sure the Customer has been informed about the PharmEasy Voucher</h4>
                        {/* <h5 style={{color: 'red'}}>Eligibility Criteria : </h5>
                    <h5 style={{color: 'red'}}>1. Have you Pitched the Apollo Pharmacy Voucher to Customer.</h5>
                    <h5 style={{color: 'red'}}>2. This will be applicable on Care Supreme and Care Supreme Direct plan with Sum Insured &gt;= to 10 Lacs.</h5> */}
                        <div>
                            <button onClick={() => { UpdateVoucherInfo() }} >Yes, Proceed</button>
                            <button onClick={() => { setIsConfirmationPopupShow(false) }}>No</button>
                        </div>
                    </div>
                </ModalPopup>
            }
        </div>
    </>

}
export default VoucherDetails;
