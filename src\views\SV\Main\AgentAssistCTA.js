import React, { useState } from "react";
import { Grid } from "@mui/material";
import { ExpandMore } from "@mui/icons-material";

export default function (props) {
    const [show, setShow] = useState(false);
    const url = "https://docs.google.com/spreadsheets/d/1greopUjVIZWGiifKj0t0ZMOwUQkg3bRhoNXTfjVVra0/edit?gid=0#gid=0";
    const cjValidationUrl = "https://docs.google.com/spreadsheets/d/1XOnYyQfiR2BVrx-VC9pzQxL2Zhw-K2-jRq8KO1Kwux0/edit#gid=807981234";
    const issuanceEbUrl = "https://docs.google.com/spreadsheets/d/12DL-8wJYK4bRK9klgM56j7wbmyVvYah8VEw54gyrCiw/edit#gid=929948740";

    const handleToggle = (e) => {
        setShow(!show);
    }

    return (
        <>
            {(props && props.ProductId && props.ProductId === 131)
                ?
                <Grid item sm={12} md={12} xs={12}>
                    <div className={`SmeAgentAssist`}>
                        <button onClick={handleToggle} className={show ? "activeBlack" : ""}>Agent Assist</button>
                        <div className="expandmoreIconAgentAsist">
                            <ExpandMore onClick={handleToggle} style={{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)' }} />
                        </div>
                        {
                            show &&
                            <>
                                <p>
                                    <a onClick={() => window.open(cjValidationUrl, '_blank')} > Click here to open EB CJ Validations </a>
                                </p>
                                <p>
                                    <a onClick={() => window.open(issuanceEbUrl, '_blank')} > Click here to open EB Issuance Checklist </a>
                                </p>
                            </>
                        }
                    </div>
                </Grid>
                :
                <Grid item sm={12} md={12} xs={12}>
                    <div className={`Foscityofflinestore`}>
                        <button onClick={() => { window.open(url, '_blank') }}>Agent Assist</button>
                    </div>
                </Grid>
            }
        </>
    )
}