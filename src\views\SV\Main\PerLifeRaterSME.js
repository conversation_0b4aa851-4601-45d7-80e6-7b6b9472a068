import React, { useEffect, useState } from "react";
import { Mo<PERSON>, Box, Button } from "@mui/material";
import masterService from "../../../services/masterService";
import { enqueueSnackbar } from "notistack";

const PerLifeRaterSME = (props) => {
    const [url, seturl] = useState(""); // Initialize as an empty string
    var customerId = props.leadCollection && props.leadCollection.length > 0 ? props.leadCollection[0].CustomerID : 0;

    const GetPerLifeRaterSME = () => {
        if (!customerId || !props.ParentLeadId) {
            enqueueSnackbar("CustomerId or ParentLeadId is missing", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return;
        }

        const reqData = {
            CustomerId: customerId,
            LeadId: props.ParentLeadId,
            Token: "", // Update this if Token is required dynamically
        };

        masterService
            .GetPerLifeRaterUrl(reqData)
            .then((res) => {
                if (res && res.StatusCode==1 && res.URL) {
                    seturl(res.URL);
                } else {
                    enqueueSnackbar("Failed to fetch Per Life Rater URL. Please try again.", { variant: "error" });
                }
            })
            .catch((error) => {
                console.error("Error fetching Per Life Rater URL:", error);
                enqueueSnackbar("An error occurred while fetching the Per Life Rater URL.", { variant: "error" });
            });
    };

    useEffect(() => {
        if (props.show) {
            GetPerLifeRaterSME();
        }
    }, [props.show]);

    return (
        <Modal open={props.show} onClose={props.handleClose}>
            <Box
               className = "perliferater"
            >
                {/* Iframe to display the link */}
                    <iframe
                        src={url}
                        title="Per Life Rater"
                        style={{
                            width: "100%",
                            height: "90%", // Leave some space for the close button
                            border: "none",
                        }}
                    ></iframe>
                {/* Close Button */}
                <Button
                    variant="outlined"
                    color="secondary"
                    sx={{ mt: 2 }}
                    onClick={props.handleClose}
                    style={{
                        position: "absolute",
                        bottom: "10px",
                        right: "10px",
                    }}
                >
                    Close
                </Button>
            </Box>
        </Modal>
    );
};

export default PerLifeRaterSME;