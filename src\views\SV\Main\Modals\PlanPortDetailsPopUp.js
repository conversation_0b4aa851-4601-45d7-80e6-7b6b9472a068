import React, { useEffect, useState } from "react";
import ModalPopup from '../../../../components/Dialogs/ModalPopup';
import { Grid, Button } from "@mui/material";
import { getInsurerList } from "../../../../assets/json/getInsurerList";
import { GetESQuickQuotesForPortV3, GetPortSelectionURL } from "../../../../services/Common";
import { useSnackbar } from "notistack";
import {  gaEventTracker } from '../../../../helpers/index';
import User from "../../../../services/user.service";


export const PlanPortDetailsPopUp = (props) => {
    const [PortDetials, setPortDetials] = useState([]);
    const { enqueueSnackbar } = useSnackbar();

    useEffect(() => {
        if (props.open && props.lead && props.lead.LeadID && props.lead.LeadID > 0)
            GetESQuickQuotesForPortV3(props.lead.LeadID).then((result) => {
                gaEventTracker("PortPlansBannerClicked","Star", User.EmployeeId)
                if (result) {
                    setPortDetials(result);
                }
            }).catch((e) => {
                console.log(e);
            })
    }, [props.lead.LeadID, props.open]);

    const getlink = (PlanID) => {
        for (let key in PortDetials) {
            if (PortDetials[key].PlanId == PlanID) {
                const reqData = {
                    proposerID: PortDetials[key].proposerId,
                    planId: PortDetials[key].PlanId,
                    enquiryID: PortDetials[key].enquiryId,
                    sumInsured: PortDetials[key].SumInsured
    
                };
                GetPortSelectionURL(reqData).then((result) => {
                    gaEventTracker("CJPlanClicked",PortDetials[key].PlanName, User.EmployeeId)
                    if (result) {
                        window.open(result)
                    }
                    else {
                        enqueueSnackbar('Please try again', {
                            variant: 'error',
                            autoHideDuration: 4000,
                            style: { whiteSpace: 'pre-line' },
                        });
                    }
                }).catch((e) => {
                    enqueueSnackbar('Please try again', {
                        variant: 'error',
                        autoHideDuration: 4000,
                        style: { whiteSpace: 'pre-line' },
                    });
                    console.log(e);
                })
            }
        }
    };

    const getimg = (id) => {
        if (id > 0) {
            let img = getInsurerList(id)
            return img.image;
        }
    }

    return (

        <ModalPopup open={props.open} title="Plan Port" className="PlanPortDetailsPopup" handleClose={props.handleClose}>
            <Grid container spacing={3}>
                {PortDetials && PortDetials.length > 0 && PortDetials.map((section, index) => {
                    return (
                        <>
                            <Grid className="item" item xs={6} md={2} lg={2}>
                                <img src={getimg(section.SupplierId)} />
                            </Grid>
                            <Grid className="item" item xs={6} md={3} lg={3}>
                                <label>Plan name:</label>
                                <span> {section.PlanName}</span>
                            </Grid>
                            <Grid className="item" item xs={6} md={2} lg={2}>
                                <label>Starting Premium:</label>
                                <span>{section.Premium}</span>
                            </Grid>
                            <Grid className="item" item xs={6} md={2} lg={2}>
                                <label>Sum Insured:</label>
                                <span>{section.SumInsured}</span>
                            </Grid>
                            <Grid className="item" item xs={6} md={2} lg={2}>
                                <label></label>
                                <span><Button className="cjbutton" onClick={() => { getlink(section.PlanId) }} >Click for Plan CJ </Button></span>
                            </Grid>
                            <hr />
                        </>)
                })
                }
                {!PortDetials && <><Grid item xs={12} md={12} lg={12} className="text-center"><b> No Data Found</b></Grid> </>}
                {PortDetials && PortDetials.length == 0 && <><Grid item xs={12} md={12} lg={12} className="text-center"><b> No Data Found </b></Grid></>}


            </Grid>
        </ModalPopup>
    )
}
