import { But<PERSON>, Grid } from "@mui/material";
import {  LinearProgress } from "@mui/material";
import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import rootScopeService from "../../../services/rootScopeService";
import { GetCustomerPitchedContent } from "../../../services/Common";
import { ExpandMore } from "@mui/icons-material";
import { gaEventTracker } from "../../../helpers";
import User from "../../../services/user.service";

const CustomerPitchedContent = () => {
    const [custpitchedresult, setcustpitchedresult] = useState();
    const [isPitchedDataLoading, setIsPitchedDataLoading] = useState(false);
    const [apifailed, setapifailed] = useState(false);
    let leadIds = useSelector(state =>  state.salesview.leadIds).split(';').join(',').slice(0, -1);
    const [show, setShow] = useState(false);
    let [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);
    let noOfLeadCardsToShow = useSelector(({ salesview }) => salesview.noOfLeadCardsToShow);
    const [IsRenewal] = useSelector(state => {
        let { IsRenewal } = state.salesview;
        return [ IsRenewal]
    });
    
    useEffect(() => {
        if (RefreshLead) {
            setShow(false);
        }
    }, [RefreshLead]);

    const getCustomersPitch = () => {
        setIsPitchedDataLoading(true);
        setapifailed(false);
        GetCustomerPitchedContent(leadIds).then((result) => {
            if (result) {
                setcustpitchedresult(result);
            }
        }).catch((e)=>{
            setapifailed(true)
            console.log(e);
        })
        .finally(() => {
            setIsPitchedDataLoading(false);
        });
    }
    
    const handleToggle = () => {
        setShow(!show);
        setcustpitchedresult(null)
        if(noOfLeadCardsToShow <=50){
            if(show==false){
                gaEventTracker("Health_Renewal","Customer_Pitched_Content",User.UserId)
                getCustomersPitch(leadIds)
            }
        }
    }

    if(rootScopeService.getProductId()!=2 || !IsRenewal){
        return null
    }
    return <>
        <Grid item sm={12} md={12} xs={12}>
            <div className="custPitched">
                <h3>Customer Pitched Content</h3>
                <div className="expandmoreIcon">
                    <ExpandMore onClick={()=>handleToggle()} style={{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)' }} /></div>
                    {isPitchedDataLoading && <LinearProgress />}
                    {show && !isPitchedDataLoading && <>
                    {custpitchedresult && Array.isArray(custpitchedresult.RemarksList) && custpitchedresult.RemarksList[0]!=="" ?
                        <>
                        <ul>
                            {custpitchedresult.RemarksList.slice(0,5).map((item,index) => <li key={index}>{item}</li> )}
                        </ul>  
                        </> : <>{apifailed ? <Button variant="text" onClick={()=>getCustomersPitch(leadIds)}>Click here to retry</Button> :
                         <p>No Details</p>}
                        </>
                    }
                    </>
                }
            </div>
        </Grid>


    </>
}
export default CustomerPitchedContent;