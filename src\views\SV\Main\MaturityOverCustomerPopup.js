import React,{useEffect, useState} from "react";
import ModalPopup from "../../../components/Dialogs/ModalPopup";
import { Typography, Box } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { IconButton } from "@mui/material";
import { CONFIG } from "../../../appconfig";

const MaturityOverCustomerPopup = (props) => {
    let lead = props.lead;

    const [leadDetails, setleadDetails] = useState({
            InsurerId:0,
            InsurerImageURL:'',
            TotalInvestment:0,
            AbsoluteReturn:'',
            PolicyStatus:''
        });

    useEffect(()=>{
        if(props.open == true){
            if(lead && lead.UTM_Medium && lead.Utm_term){
                let MediumParts = lead.UTM_Medium.split(":");
                let TermParts = lead.Utm_term.split(":");
                let SourceParts = lead.Utm_source.split('_');
                if(MediumParts && Array.isArray(MediumParts) && MediumParts.length > 2 && TermParts && Array.isArray(TermParts) && TermParts.length > 1 && SourceParts && Array.isArray(SourceParts) && SourceParts.length > 3)
                {
                    
                let SubMediumParts = MediumParts[1].split("&");
                    let insurerid = SubMediumParts[0].trim();
                    let url = '';
                    
                    switch(insurerid){
                        case "1":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/Bandhan Life with Formerly Aegon Life-svg_400x400.svg`;
                            break;
                        case "2":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/Aviva.svg`;
                            break;
                        case "3":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/Bajaj_Allianz.png`;
                            break;
                        case "4":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/AVSLI Logo.png`;
                            break;
                        case "5":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/FG Logo copy-01.svg`;
                            break;
                        case "6":
                            url = `${CONFIG.PUBLIC_URL}/images/HDFC-Life.svg`;
                            break;
                        case "7":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/ipru.svg`;
                            break;
                        case "8":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/Exide Logo _ HDFC LOGO_29072022-02.svg`;
                            break;
                        case "9":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/Kotak Life.svg`;
                            break;
                        case "10":
                        case "184":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/LICLogo.svg`;
                            break;
                        case "11":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/MAX+AXIS LOGO COLLECTION - with patch.svg`;
                            break;
                        case "12":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/PNB Metlife.svg`;
                            break;
                        case "13":
                            // url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/Reliance.png`;
                            break;
                        case "14":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/sbi-life-insurance-vector-logo-2022.svg`;
                            break;
                        case "15":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/TATA_AIA 1.svg`;
                            break;
                        case "16":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/Canra.svg`;
                            break;                      
                        case "17":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/Edelweiss.png`;
                            break;                      
                        case "18":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/Bharti_Axa.png`;
                            break;                      
                        case "19":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/Sahara.svg`;
                            break;                      
                        case "20":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/ShriramLifeInsurance.svg`;
                            break;                      
                        case "21":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/preme.svg`;
                            break;                      
                        case "22":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/India First.svg`;
                            break;                      
                        case "23":
                            // url = `${CONFIG.PUBLIC_URL}`;
                            break;                      
                        case "24":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/state union of dai ichi.svg`;
                            break;                      
                        case "25":
                            // url = `${CONFIG.PUBLIC_URL}`;
                            break;                      
                        case "191":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/DIGIT.png`;
                            break;                      
                    }

                    if(url != '' && MediumParts[2] > 0 && TermParts[1] != '' && SourceParts[2] != ''){
                        setleadDetails({...leadDetails,
                        InsurerId : insurerid,
                        InsurerImageURL:url,
                        TotalInvestment:MediumParts[2],
                        AbsoluteReturn:TermParts[1],
                        PolicyStatus:SourceParts[2] + " Over"
                    });
                    }
                    else{
                        props.handleClose()
                    }
                }
                else{
                    props.handleClose()
                }
            }
            else{
                props.handleClose();
            }
        }
    },[props.open]);

    return (
        <ModalPopup open={props.open} handleClose={props.handleClose} className="MaturityOverCustomerPopup">
            <Box className="maturity-popup-container">
                {/* Close Icon */}
                 <IconButton onClick={props.handleClose} size="large">
                    <CloseIcon />
                </IconButton>
                {/* Logo and Title */}
                <Box className="maturity-header">

                    <img src={leadDetails.InsurerImageURL} alt="TATA AIA" className="maturity-logo" />
                    {/* <Typography className="maturity-title">TATA AIA i SIP</Typography> */}
                </Box>
                {/* Info Cards */}
                <Box className="maturity-info-row">
                    <Box className="maturity-info-card">

                        <img
                            src={`${CONFIG.PUBLIC_URL}/images/salesview/total-Inv.svg`}
                            alt="Total Investment"
                            className="maturity-Icon"
                        />

                        <Box className="maturity-info-right">
                            <Typography className="maturity-info-label">Total Investment</Typography>
                            <Typography className="maturity-info-value">₹{leadDetails.TotalInvestment}</Typography>
                        </Box>
                    </Box>
                    <Box className="maturity-info-card">

                        <img src={`${CONFIG.PUBLIC_URL}/images/salesview/return.svg`} alt="Absolute Return" className="maturity-Icon" />

                        <Box className="maturity-info-right">
                            <Typography className="maturity-info-label">Absolute Return</Typography>
                            <Typography className="maturity-info-value">{leadDetails.AbsoluteReturn}</Typography>
                        </Box>
                    </Box>
                    <Box className="maturity-info-card">

                        <img src={`${CONFIG.PUBLIC_URL}/images/salesview/policy.svg`} alt="Policy Status" className="maturity-Icon" />

                        <Box className="maturity-info-right">
                            <Typography className="maturity-info-label">Policy Status</Typography>
                            <Typography className="maturity-info-value ">{leadDetails.PolicyStatus}</Typography>
                        </Box>
                    </Box>


                </Box>
                {/* CTA Banner */}
                <Box className="maturity-cta-banner">
                    Time to reinvest- Don't let your customer's money sit idle
                </Box>
            </Box>
        </ModalPopup>
    );
};

export default MaturityOverCustomerPopup;