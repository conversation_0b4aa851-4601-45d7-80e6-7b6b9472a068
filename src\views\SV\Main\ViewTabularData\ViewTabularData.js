import React, { useState, useEffect, useRef } from "react";
import "./ViewTabularData.css";
import {
  GetUserAuthorizedAttributesValues,
  FetchTabularData
} from "../../../../layouts/SV/components/Sidebar/helper/sidebarHelper";
import { SV_CONFIG } from "../../../../appconfig";
import { GetBmsUrlService, OpenLeadContentOnClick } from "../../../../services/Common";
import { enqueueSnackbar } from "notistack";

const SideMenu = ({ items, selectedItem, onSelectItem }) => {
  return (
    <nav className="panel-nav">
      <ul>
        {items.map((item) => (
          <li
            key={item.id}
            className={`panel-nav-item ${
              selectedItem === item.id ? "panel-nav-item-active" : ""
            }`}
            onClick={() => onSelectItem(item.id)}
          >
            {item.label}
          </li>
        ))}
      </ul>
    </nav>
  );
};

const DataTable = ({ data, menuId }) => {
  const [sortConfig, setSortConfig] = useState({ key: null, direction: "asc" });
  const [filters, setFilters] = useState({});
  const [filteredData, setFilteredData] = useState([]);
  const [expandedCell, setExpandedCell] = useState(null);
  const [columnWidths, setColumnWidths] = useState({});
  const headerRefs = useRef({});

  const hiddenColumnsConfig = {
    'TransferUWCalls': ['CustomerId']
  };
  
  const allColumns = data.length > 0 ? Object.keys(data[0]) : [];
  const hiddenColumns = hiddenColumnsConfig[menuId] || [];
  const columns = allColumns.filter(column => !hiddenColumns.includes(column));

  useEffect(() => {
    const newColumnWidths = {};

    columns.forEach((column) => {
      if (headerRefs.current[column]) {
        const headerWidth = headerRefs.current[column].scrollWidth + 20;

        const width = Math.max(Math.min(headerWidth, 200), 120);
        newColumnWidths[column] = width;
      }
    });

    setColumnWidths(newColumnWidths);
  }, [columns]);

  useEffect(() => {
    let dataToProcess = [...data];

    Object.keys(filters).forEach((key) => {
      const filter = filters[key];

      if (filter) {
        if (filter.filterType === "date" && filter.dateRange) {
          const { start, end } = filter.dateRange;

          if (start || end) {
            dataToProcess = dataToProcess.filter((item) => {
              const itemValue = item[key];

              if (
                itemValue === null ||
                itemValue === undefined ||
                itemValue === ""
              ) {
                return false;
              }

              const itemDate = new Date(itemValue);

              if (isNaN(itemDate.getTime())) {
                return false;
              }

              if (start && new Date(start) > itemDate) {
                return false;
              }

              if (end && new Date(end) < itemDate) {
                return false;
              }

              return true;
            });
          }
        } else if (filter.value) {
          const value = filter.value;

          dataToProcess = dataToProcess.filter((item) => {
            const itemValue = item[key];

            if (
              itemValue === null ||
              itemValue === undefined ||
              itemValue === ""
            ) {
              return value === "";
            }

            return String(itemValue)
              .toLowerCase()
              .includes(value.toLowerCase());
          });
        }
      }
    });

    if (sortConfig.key) {
      dataToProcess.sort((a, b) => {
        const aValue = a[sortConfig.key] || "";
        const bValue = b[sortConfig.key] || "";

        if (aValue < bValue) {
          return sortConfig.direction === "asc" ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === "asc" ? 1 : -1;
        }
        return 0;
      });
    }

    setFilteredData(dataToProcess);
  }, [data, sortConfig, filters]);

  const handleSort = (key) => {
    let direction = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }
    setSortConfig({ key, direction });
  };

  const detectValueType = (value) => {
    if (value === null || value === undefined || value === "") return "string";
    if (!isNaN(value) && typeof value !== "boolean") return "number";
    if (!isNaN(Date.parse(value))) return "date";
    return "string";
  };

  const handleFilterChange = (
    key,
    value,
    filterType = "text",
    dateRange = null
  ) => {
    setFilters((prev) => ({
      ...prev,
      [key]: { value, filterType, dateRange },
    }));
  };

  const handleCellClick = (rowIndex, column) => {
    const cellId = `${rowIndex}-${column}`;
    setExpandedCell(expandedCell === cellId ? null : cellId);
  };

  const GetBmsUrl = (LeadId) => {
    GetBmsUrlService(LeadId).then((resp) => { 
      if (resp) {
        window.open(resp, '_blank');
      }
      else {
        enqueueSnackbar("URL not Found.", { variant: 'error', autoHideDuration: 3000 });
      }
    });
  }
  
  const renderCellContent = (menuId, column, cellValue, row) => {
    // Configuration for links based on menuId and column
    const linkConfig = {
      'TransferUWCalls': {
        'BookingId': {
          isLink: true,
          onClick: () => GetBmsUrl(cellValue)
        },
        'Customer Name': {
          isLink: true,
          onClick: () => OpenLeadContentOnClick(row.BookingId, row.CustomerId, 2)
        }
      }
      // Add more menuId configurations as needed
    };
    
    // Check if this cell should be a link
    const config = linkConfig[menuId]?.[column];
    
    if (config?.isLink) {
      return (
        <a 
          href="#" 
          onClick={(e) => {
            config.onClick();
          }}
          style={{
            color: '#3498db',
            textDecoration: 'underline',
            cursor: 'pointer'
          }}
        >
          {cellValue}
        </a>
      );
    }
    
    // Default: return cell value as text
    return cellValue;
  }

  return (
    <div className="panel-table-wrapper">
      <table className="panel-table">
        <thead>
          <tr>
            <th 
              style={{ 
                minWidth: "60px",
                textAlign: "center"
              }}
            >
              S.No
            </th>
            {columns.map((column) => (
              <th
                key={column}
                onClick={() => handleSort(column)}
                ref={(el) => (headerRefs.current[column] = el)}
                style={{
                  minWidth: columnWidths[column]
                    ? `${columnWidths[column]}px`
                    : "120px",
                }}
              >
                {column}
                {sortConfig.key === column && (
                  <span className="panel-sort-indicator">
                    {sortConfig.direction === "asc" ? " ▲" : " ▼"}
                  </span>
                )}
              </th>
            ))}
          </tr>
          <tr className="panel-filter-row">
            <th style={{ minWidth: "60px" }}></th>
            {columns.map((column) => (
              <th
                key={`filter-${column}`}
                style={{
                  minWidth: columnWidths[column]
                    ? `${columnWidths[column]}px`
                    : "120px",
                }}
              >
                <div className="panel-filter-container">
                  {detectValueType(data[0][column]) === "date" ? (
                    <div className="date-range-filter">
                      <input
                        className="panel-filter-input date-input"
                        type="date"
                        placeholder="From"
                        onChange={(e) => {
                          const startDate = e.target.value;
                          const endDate = filters[column]?.dateRange?.end || "";
                          handleFilterChange(column, "", "date", {
                            start: startDate,
                            end: endDate,
                          });
                        }}
                      />
                      <input
                        className="panel-filter-input date-input"
                        type="date"
                        placeholder="To"
                        onChange={(e) => {
                          const endDate = e.target.value;
                          const startDate =
                            filters[column]?.dateRange?.start || "";
                          handleFilterChange(column, "", "date", {
                            start: startDate,
                            end: endDate,
                          });
                        }}
                      />
                    </div>
                  ) : (
                    <input
                      className="panel-filter-input"
                      type="text"
                      placeholder={`Search ${column}`}
                      defaultValue=""
                      onInput={(e) =>
                        handleFilterChange(column, e.target.value, "text")
                      }
                    />
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {filteredData.map((row, rowIndex) => (
            <tr key={rowIndex}>
              <td style={{ textAlign: "center" }}>{rowIndex + 1}</td>
              {columns.map((column) => {
                const cellId = `${rowIndex}-${column}`;
                const isExpanded = expandedCell === cellId;
                const cellValue =
                  row[column] !== null &&
                  row[column] !== undefined &&
                  row[column] !== ""
                    ? row[column]
                    : "-";

                return (
                  <td
                    key={cellId}
                    onClick={() => handleCellClick(rowIndex, column)}
                    className={isExpanded ? "panel-table-cell-expanded" : ""}
                    title={cellValue}
                    style={{
                      minWidth: columnWidths[column]
                        ? `${columnWidths[column]}px`
                        : "120px",
                    }}
                  >
                    {renderCellContent(menuId, column, cellValue, row)}
                  </td>
                );
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

function ViewTabularData() {
  const [selectedMenu, setSelectedMenu] = useState("");
  const [data, setData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [menuItems, setmenuItems] = useState([]);

  const fetchData = (menuId) => {
    setIsLoading(true);
    setData(null);

    FetchTabularData(menuId).then((result) => {
      if (result) {
        try {
          let finalData;
          if (Array.isArray(result) && result.length > 0) {
            if (Array.isArray(result[0])) {
              finalData = result[0];
            } else {
              finalData = result;
            }
          } else {
            finalData = [];
          }

          if (finalData.length > 0) {
            setData(finalData);
          } else {
            setData("empty");
          }
        } catch (error) {
          setData(null);
        }
      } else {
        setData(null);
      }
      setIsLoading(false);
    });
  };

  const handleMenuSelect = (menuId) => {
    setSelectedMenu(menuId);
    setData(null);
    fetchData(menuId);
  };

  useEffect(() => {
    if (
      SV_CONFIG["TableDataJson"] &&
      Array.isArray(SV_CONFIG["TableDataJson"]) &&
      SV_CONFIG["TableDataJson"].length > 0
    ) {
      GetUserAuthorizedAttributesValues().then((result) => {
        if(result && Array.isArray(result) && result.length > 0){
          const filteredData = SV_CONFIG["TableDataJson"].filter(item => result.includes(item.attributeId));

          setmenuItems(filteredData);
        }
      })
    }
  }, []);

  const renderContent = () => {
    if(menuItems && Array.isArray(menuItems) && menuItems.length === 0){
      return <div className="panel-error">No Menu Found for the User!</div>;
    }

    if (!selectedMenu) {
      return <div className="panel-error">Please select a menu first.</div>;
    }

    if (isLoading) {
      return <div className="panel-loading">Loading data...</div>;
    }

    if (data) {
      return <DataTable data={data} menuId={selectedMenu} />;
    }

    return <div className="panel-loading">No Data Found for the User!</div>;
  };

  return (
    <div className="panel-container">
      <SideMenu
        items={menuItems}
        selectedItem={selectedMenu}
        onSelectItem={handleMenuSelect}
      />
      <main className="panel-content">
        <h1 className="panel-title">
          {menuItems.find((item) => item.id === selectedMenu)?.label}
        </h1>
        {renderContent()}
      </main>
    </div>
  );
}

export default ViewTabularData;
