import React, { useEffect, useState } from "react";
import { connect } from 'react-redux'
import ScrollMenu from 'react-horizontal-scrolling-menu';
import { ChevronRight, ChevronLeft } from '@mui/icons-material';
import { useSelector } from "react-redux";
import { setNext5LeadsData, setRefreshAgentStats, setRefreshLead, updateStateInRedux } from "../../../store/actions/SalesView/SalesView";
import Common from "../Main/helpers/Common";
import { useDispatch } from "react-redux";
// import { updateStateInRedux } from "../../../../store/actions/SalesView/SalesView";

const Arrow = ({ text, className }) => {
    return (
        <div className={className}>{text}</div>
    );
};

const ArrowLeft = Arrow({ text: <ChevronLeft />, className: 'arrow-prev' });
const ArrowRight = Arrow({ text: <ChevronRight />, className: 'arrow-next' });

const NextFiveLeads = (props) => {
    let [parentLeadId, next5leads] = useSelector(({ salesview }) => [salesview.parentLeadId, salesview.next5leads]);
    const [Leads, setLeads] = useState([]);
    const { ConnectCallSF } = props;
    const dispatch = useDispatch();
    const openSalesView = (CustomerId, ProductId, LeadId, openSource, Reason, ReasonId) => {

        if (parentLeadId !== LeadId) {
            Common.OpenSalesView(CustomerId, ProductId, LeadId, openSource, Reason, ReasonId);
            props.setRefreshLeadToRedux(true);
        }
    }

    if (Leads && Leads.length > 0) { }

    const LeadsHTML = Leads.map((lead, index) => {

        let css = "leadName " + lead.CallStatus;
        if (ConnectCallSF && lead.LeadId === ConnectCallSF.LeadID && (lead.CallStatus && lead.CallStatus != " ")) {
            //css = "leadName active " + lead.CallStatus;
            //window.localStorage.setItem("CallStatus", lead.CallStatus);
        }
        return <div key={index} className={css} title={lead.CallStatus}>
            <div className="nextfiveLead" onClick={() => { openSalesView(lead.CustomerId, lead.ProductId, lead.LeadId, 's', lead.Reason, lead.ReasonId) }}>
                {(lead.CallStatus && lead.CallStatus!==" ") ?
                    <>
                        <marquee scrollamount="2" width="155">
                            <h4>{lead.Name} {lead.LeadId}  ({lead.Reason})</h4></marquee>
                        <p> {lead.isPrimaryMobile !=undefined ? (lead.isPrimaryMobile ? "(P)" : "(S)"): ""} {lead.CallStatus} &nbsp;</p>
                    </>
                    :
                    <>
                        <h4>{lead.Name}</h4>
                        <p>{lead.LeadId}  {lead.isPrimaryMobile !=undefined ? (lead.isPrimaryMobile ? "(P)" : "(S)"): ""} ({lead.Reason}) &nbsp;</p>
                    </>
                }
                {/* <marquee scrollamount="2" width="155"><h4>{lead.Name} {lead.LeadId} ({lead.Reason})</h4></marquee> */}
                {/* <span>{lead.LeadId} ({lead.Reason})</span> */}
                {/* <p>{lead.CallStatus} &nbsp;</p> */}
            </div>
        </div>
    })

    useEffect(() => {
        if (parentLeadId) {
            getNextLeads();
        }
    }, [parentLeadId]);

    useEffect(() => {
        if (window.sessionStorage.getItem("reloadNext5Lead")) {
            getNextLeads();
            window.sessionStorage.setItem("reloadNext5Lead", false);
        }
    }, [window.sessionStorage.getItem("reloadNext5Lead")]);

    const AddPrimaryMobileandStatus = (next5leads) => {
        let IsPrimary = [];
        next5leads.forEach(function (element) {
            var IsPrimaryMobile = window.localStorage.getItem("IsPrimaryMobile");
            if (IsPrimaryMobile != null && IsPrimaryMobile != undefined) {
                IsPrimary = JSON.parse(IsPrimaryMobile);
            }
            if (IsPrimary.length > 0) {
                IsPrimary.forEach(function (mobile) {
                    if (mobile.LeadId == element.LeadId) {
                        element.isPrimaryMobile = mobile.IsPrimary;
                        element.status = mobile.status;
                    }
                });
            }
        })
        dispatch(updateStateInRedux({ key: "IsPrimary", value: IsPrimary }));
    }

    useEffect(() => {
        if (Array.isArray(next5leads)) {
            setLeads(next5leads);
            // AddPrimaryMobileandStatus(next5leads)
        }
    }, [JSON.stringify(next5leads)])


    const getNextLeads = () => {
        props.setRefreshAgentStatsToRedux(true);
    }

    if (Leads && Leads.length > 0) {

        return (
            <div id="nextFive" style={{ width: props.width }}>
                <ScrollMenu
                    data={LeadsHTML}
                    arrowLeft={ArrowLeft}
                    arrowRight={ArrowRight}
                    translate={1}
                    alignCenter={false}
                    hideSingleArrow={true}
                    useButtonRole={false}
                    disableTabindex
                    wheel={false}
                />
            </div>
        )
    }
    else {
        return (
            <div id="nextFive" className="noLeadbox" style={{ width: props.width, minWidth: '300px' }}>
                <h3 className="noLead">NO LEAD TO WORK RIGHT NOW! Click on Done.</h3>
            </div>
        )
    }
}

const mapStateToProps = state => {
    return {

    };
};

const mapDispatchToProps = dispatch => {

    return {
        setRefreshAgentStatsToRedux: (value) => dispatch(setRefreshAgentStats({ RefreshAgentStats: value })),
        setNext5LeadsToRedux: (leads) => dispatch(setNext5LeadsData({ next5leads: leads })),
        setRefreshLeadToRedux: (value) => dispatch(setRefreshLead({ RefreshLead: value }))
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(NextFiveLeads);