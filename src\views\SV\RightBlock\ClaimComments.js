import { Grid } from "@mui/material";
import { ExpandMore } from "@mui/icons-material";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { CALL_API } from "../../../services";
import { gaEventNames, gaEventTracker } from "../../../helpers";
import User from "../../../services/user.service";
import rootScopeService from "../../../services/rootScopeService";
const ClaimComments = () => {
    let [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);
    let [comments, setComments] = useState([]);
    let [commentsLength, setCommentLength] = useState(-1);
    let [ParentLeadId, PrimaryLeadId, LeadIds] = useSelector(state => {
        let { parentLeadId, primaryLeadId, leadIds } = state.salesview;
        return [parentLeadId, primaryLeadId, leadIds];
    });
    const [show, setShow] = useState(false);

    const getClaimComments = () => {
        // if (!LeadIds) return;

        const input = {
            url: 'api/SalesView/GetClaimComments/' + ParentLeadId,
            method: 'GET', service: 'MatrixCoreAPI'
        }
       
        CALL_API(input).then((result) => {
            var arr = [];
            if (result.Data) {
                result.Data.forEach(function (val, index) {
                    if (val.Comments.length > 0) {
                        val.Comments.forEach(function (item, _index) {
                            arr.push(item);
                        });
                    }
                });
                setComments(arr);
                setCommentLength(arr.length);
            }
            else {
                setCommentLength(0);
                setComments([]);
            }
        }).catch(() => {
            setCommentLength(-2);
        });
    }

    const handleToggle = (e) => {
        if (!show) {
            getClaimComments();
            gaEventTracker(gaEventNames.ClaimComments, User.EmployeeId, rootScopeService.getLeadId());
        }
        setShow(!show);
    }

    const getStatus = (code) => {
        if (code === -1) return "Loading...";
        if (code === 0) return "No Comments";
        if (code === -2) return "No Comments";

    }
    useEffect(() => {
        if (RefreshLead) {
            setShow(false);
            setCommentLength(-1);
        }
        // }, [RefreshLead]);
        // getClaimComments();
    }, [RefreshLead]);
    return <>
        <Grid item sm={12} md={12} xs={12}>

            <div className="claimComments">
                <h3>Claims Comments</h3>

                <div className="expandmoreIcon">
                    <ExpandMore onClick={handleToggle} style={{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)' }} /></div>
                {show &&
                    (commentsLength > 0 ?
                        <>
                            <div class="scrollBar">
                                {comments.map((comment, index) => (
                                    <div className="comment-text">
                                        <h4>{comment.UserName}</h4>
                                        <p>{comment.Comments}</p>
                                        <span>{dayjs(comment.CommentDate).format('DD/MM/YYYY h:mm:ss a')}</span>
                                    </div>
                                ))
                                }
                            </div>
                        </>
                        : (<p>{getStatus(commentsLength)}</p>))
                }

            </div>
        </Grid>
    </>
}
export default ClaimComments;
