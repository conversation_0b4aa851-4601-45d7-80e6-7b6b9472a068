import React, { useState } from "react";
import { <PERSON>rid, <PERSON>alog, DialogContent, DialogActions, IconButton, Button } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { AssignLeadToGroup } from "../../../services/Common";
import { enqueueSnackbar } from "notistack";

const CustomerResearchComplete = (props) => {
    const [showDialog, setShowDialog] = useState(false);

    const handleApiCall = (surveyCompleted) => {
        const requestData = {
            "LeadId": props?.LeadID,
            "ProductId": props?.ProductID,
            "GroupId": props?.GroupId,
            "SurveyCompleted": surveyCompleted
        };

        AssignLeadToGroup(requestData).then((result) => {
            if (result?.status === true) {
                alert("Lead " + (props?.LeadID ? props.LeadID : '') + " moved for Sales Advisor Assignment");
                window.location.reload();
            } else {
                enqueueSnackbar("Some error occurred", {
                    variant: 'error',
                    autoHideDuration: 3000,
                });
            }
        });
    };

    return (
        <>
            <Grid item sm={12} md={12} xs={12}>
                <div className={`Foscityofflinestore`}>
                    <button
                        variant="contained"
                        color="secondary"
                        onClick={() => setShowDialog(true)}
                    >
                        Customer Survey Completed
                    </button>
                </div>
            </Grid>

            <Dialog
                open={showDialog}
                onClose={() => setShowDialog(false)}
                className="customerResearchDialog"
            >
                <div className="dialogHeader">
                    <p className="dialogTitle">Please confirm if the customer survey is complete</p>
                    <IconButton onClick={() => setShowDialog(false)}>
                        <CloseIcon />
                    </IconButton>
                </div>
                <DialogContent>
                    <DialogActions className="dialogActions">
                        <Button
                            onClick={() => { handleApiCall(1); setShowDialog(false); }}
                            className="blueButton"
                        >
                            Yes
                        </Button>
                        <Button
                            onClick={() => { handleApiCall(0); setShowDialog(false); }}
                            className="NotAbleToConnectBtn"
                        >
                            Not Able To Connect
                        </Button>
                    </DialogActions>
                </DialogContent>
            </Dialog>
        </>
    );
};

export default CustomerResearchComplete;
