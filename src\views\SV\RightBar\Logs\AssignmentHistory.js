import React, { useEffect, useState } from "react";
//import SelectDropdown from "../../../../components/SelectDropdown";
import { CALL_API } from "../../../../services/api.service";
import { useSelector } from "react-redux";
import rootScopeService from "../../../../services/rootScopeService";
import dayjs from "dayjs";
import User from "../../../../services/user.service";

export default function AssignmentHistory(props) {
  let [assHistory, setAssHistory] = useState([]);
  let [ParentLeadId, PrimaryLeadId, LeadIds] = useSelector((state) => {
    let { parentLeadId, primaryLeadId, leadIds } = state.salesview;
    return [parentLeadId, primaryLeadId, leadIds];
  });

  const ProductId = rootScopeService.getProductId();
  const CustomerId = rootScopeService.getCustomerId();


  useEffect(() => {
    getAssignmentHistory();
  }, []);

  const getAssignmentHistory = () => {
    if (!LeadIds)
      return;

    const input = {
      url: "coremrs/api/LeadDetails/GetCustomerLeadAssignment?CustomerId=" + CustomerId + "&ProductId=" + ProductId + "&Leads=" + LeadIds,
      method: 'GET',
      service: 'MatrixCoreAPI'
    }
    CALL_API(input).then((result) => {
      if (result && Array.isArray(result)) {
        setAssHistory(result);
      }
    });
  };

  // Row wise Log data
  let AssHistoryList = assHistory.map((logData, index) => {
    return (
      <div key={index} className="row">
        <span className="line"></span>
        <span className="dot"></span>
        <p className="row-time">
          {dayjs(logData.AssignedOn).format("DD/MM/YYYY h:mm:ss A")}
        </p>
        <p className="row-cust-name"></p>
        <div className="mid-row">
          <div className="communication-block">
            <p className="label">Lead ID</p>
            <p className="data">{logData.LeadID}</p>
          </div>
          <div className="communication-block">
            <p className="label">Assigned To</p>
            <p className="data">
              {logData.AssignedToUserName}
              <br />({logData.AssignedToEmployeeId})
            </p>
          </div>
          {((ProductId !== 131) || (ProductId === 131 && User.RoleId !== 13)) &&
            <div className="communication-block">
              <p className="label">ASSIGNED TO GROUP</p>
              <p className="data">{logData.AssignedToGroupName}</p>
            </div>
          }
          <div className="communication-block">
            <p className="label">ASSIGNED BY</p>
            <p className="data">
              {logData.AssignedByUserName} <br />({logData.AssignedByEmployeeId}
              )
            </p>
          </div>
        </div>
      </div>
    );
  });

  return (
    <>
      <div className="activity-feed">
        <div className="blocks-data">
          {/* Row started  */}

          {AssHistoryList}

          {/* Row started  */}

          {/* 
            <div className="row">
              <span className="line"></span>
              <span className="dot"></span>
              <p className="row-time">2:40:36 PM</p>
              <p className="row-cust-name">ET01708 (Manish Dhoundiyal)</p>
              <div className="mid-row">
                <div className="communication-block">
                  <p className="label">Lead ID</p>
                  <p className="data">123761299282</p>
                </div>
                <div className="communication-block">
                  <p className="label">Assigned To</p>
                  <p className="data">
                    Shyam Meena
                    <br />
                    (PW18293)
                  </p>
                </div>
                <div className="communication-block">
                  <p className="label">ASSIGNED TO GROUP</p>
                  <p className="data">Genies</p>
                </div>
                <div className="communication-block">
                  <p className="label">ASSIGNED BY</p>
                  <p className="data">
                    Rajesh Kumar <br />
                    (PW15404)
                  </p>
                </div>
              </div>
            </div>
             */}
        </div>
      </div>
    </>
  );
}