/* eslint-disable jsx-a11y/anchor-is-valid */
import { Accordion, AccordionDetails, Accordion<PERSON>ummary, <PERSON><PERSON>, <PERSON><PERSON>, IconButton, LinearProgress, Menu, MenuItem } from "@mui/material";
import { ExpandMore } from "@mui/icons-material";
import React, { useCallback, useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { CONFIG } from "../../../appconfig";
import Grid from '@mui/material/Grid';
import HourglassEmptyRoundedIcon from '@mui/icons-material/HourglassEmptyRounded';
import TodayRoundedIcon from '@mui/icons-material/TodayRounded';
import AddCircleRoundedIcon from '@mui/icons-material/AddCircleRounded';
import CheckCircleOutlineRoundedIcon from '@mui/icons-material/CheckCircleOutlineRounded';
import { CALL_API, GetIframeURL } from "../../../services";
import User from "../../../services/user.service";
import { SoftcopySendSuccess } from "./Modals/SoftcopySendSuccess";
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { getProductIcon } from "../Main/LeadCard";
import { GetBmsUrlService, GetIssuedPolicies, resendPolicyCopyService } from "../../../services/Common";
import { useSnackbar } from "notistack";
import SendRoundedIcon from '@mui/icons-material/SendRounded';
import { ScheduleCTCpopup } from "./Modals/ScheduleCTCpopup";
import { updateStateInRedux } from "../../../store/actions/SalesView/SalesView";
import { getTicketsService } from "../RightBlock/TicketSummary";
import rootScopeService from "../../../services/rootScopeService";
import dayjs from "dayjs";

export const openTicketStatus = [1, 2, 5];
export const closedTicketStatus = [3, 4];
export const HealthRenewalclosedTicketStatus = [3, 4];



const ToggleMenu = (props) => {
    const options = Array.isArray(props.options) ? props.options : [];
    const [anchorEl, setAnchorEl] = useState(null);
    const handleClick = (event) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };
    return <>
        <IconButton onClick={handleClick}>
            <MoreVertIcon />
        </IconButton>
        <Menu
            id="simple-menu"
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            anchorOrigin={{vertical:"bottom", horizontal:"center"}}
            transformOrigin={{vertical:"top", horizontal:"right"}}
        >
            {options.filter(m => m.show).map((option, i) => <MenuItem key={i} onClick={option.onClick}>{option.name}</MenuItem>)}
        </Menu>
    </>

}
/**
* Ticket Component: Single ticket card
* props.ticketType= "open" | "closed"
*/
const Ticket = (props) => {
    const { ticketData, AddUpdateTicket, getPolicyIdByBookingId, ticketType = 'open', resendPolicyCopy, openInGenie } = props;
    const [openScheduleCTCpopup, setOpenScheduleCTCpopup] = useState(false)
    // const [openNeedAttentionPopup, setOpenNeedAttentionPopup] = useState(false)

    const menus = {
        primary: [
            {
                name: 'Set follow up',
                icon: <TodayRoundedIcon />,
                onClick: () => {
                    setOpenScheduleCTCpopup(true);
                },
                show: (ticketType === 'open')
            },
            // {
            //     name: 'Needs Assistance',
            //     icon: <WarningRoundedIcon />,
            //     onClick: () => { setOpenNeedAttentionPopup(true) },
            //     show: (ticketType === 'open')
            // },
            {
                name: 'Create new ticket',
                icon: <AddCircleRoundedIcon />,
                onClick: AddUpdateTicket ? () => { AddUpdateTicket(1, ticketData) } : () => { },
                show: (ticketType !== 'open')
            },
            {
                name: 'Send Policy Soft Copy',
                icon: <SendRoundedIcon />,
                onClick: resendPolicyCopy ? () => { resendPolicyCopy(ticketData.LeadID, ticketData.ProductId) } : () => { },
                show: ticketData.ProductId > 0 && [42, 43, 44].indexOf(ticketData.LeadStatusID) !== -1,
            },
        ],
        secondary: [
            // {
            //     name: 'Send Policy Soft Copy',
            //     onClick: resendPolicyCopy ? () => { resendPolicyCopy(ticketData.LeadID, ticketData.ProductId) } : () => { },
            //     show: (ticketType === 'open'),
            // },
            // {
            //     name: 'Needs Assistance',
            //     onClick: () => { setOpenNeedAttentionPopup(true) },
            //     show: (ticketType !== 'open')
            // },
            {
                name: 'Create new ticket',
                onClick: AddUpdateTicket ? () => { AddUpdateTicket(1, ticketData) } : () => { },
                show: (ticketType === 'open')
            }
        ]
    }

    const status = <p className={"inProgress" + (ticketType === 'closed' ? ' green' : '')}>
        {ticketType === 'closed' ? <CheckCircleOutlineRoundedIcon /> : <HourglassEmptyRoundedIcon />}
        {ticketData.Status}
    </p>;


    return <>
        <div className="ticketContent">
            <div className="pd10">
                <Grid container spacing={1}>
                    <Grid item xs={1}>
                        <div className="productIcon"> <img src={getProductIcon(ticketData.ProductId)} alt='logo' /> </div>
                        {!openInGenie && 
                            <span className="prodcutName">{ticketData.ProductName}</span>
                        }
                    </Grid>
                    <Grid item xs={10} container spacing={2}>
                        <Grid item xs={8}>
                            <p className="pName">{ticketData.SupplierName}</p>
                            <p className="pNumber">{getPolicyIdByBookingId(ticketData.LeadID)}</p>
                        </Grid>
                        <Grid item xs={4}><p className="Itemlabel">Booking ID</p><p className="ItemDes">
                            <a href="#" onClick={(e) => { e.preventDefault(); fnOpenBMS(ticketData.LeadID, 'salesview') }} >{ticketData.LeadID}</a>
                        </p></Grid>
                        {openInGenie ?
                            <Grid item xs={8}><p className="Itemlabel">Issue</p><p className="pName">{ticketData.IssueName}</p></Grid> 
                        :
                            <Grid item xs={8}><p className="Itemlabel">Issue/ Sub Issue</p><p className="pName">{ticketData.IssueName} /{ticketData.SubIssueName}
                            </p></Grid>
                        }
                        <Grid item xs={4}><p className="Itemlabel">Ticket ID</p><p className="ItemDes">
                            <a href="#" onClick={(e) => { e.preventDefault(); AddUpdateTicket(2, ticketData) }}>{ticketData.TicketDetailsID}</a>
                        </p></Grid>
                        <Grid item xs={8}><p className="Itemlabel">Follow up date</p><p className="ItemDes">{ticketData.FollowUpOn ? ticketData.FollowUpOn : 'Not set'}
                        </p></Grid>
                        <Grid item xs={4}><p className="Itemlabel">Assigned to</p><p className="ItemDes">{ticketData.AssignToUserName}
                        </p></Grid>
                        <Grid item xs={8}><p className="Itemlabel">Status</p><p className="ItemDes">{ticketData.LeadStatus ? ticketData.LeadStatus : 'N/A'}
                        </p></Grid>
                        <Grid item xs={4}><p className="Itemlabel">Last UpdatedOn</p><p className="ItemDes">{ticketData.UpdatedOn ? dayjs(ticketData.UpdatedOn).format('DD/MM/YYYY') : 'N/A'}               
                        </p></Grid>
                    </Grid>

                    <Grid item xs={1}>
                        {Array.isArray(menus.secondary) && menus.secondary.filter(m => m.show).length > 0 && <ToggleMenu options={menus.secondary} />}
                    </Grid>
                </Grid>
            </div>
            <div className={"footerBox" + (ticketType === 'closed' ? ' greenbg' : '')}>
                {openInGenie ? 
                    <Grid container spacing={1}>
                        <Grid item xs={3.5}>
                            {status}
                        </Grid>
                        {menus.primary.filter(m => m.show).map((menuItem, i) => <Grid key={i} item xs={4.2}><p onClick={menuItem.onClick}>{menuItem.icon} {menuItem.name} </p></Grid>)}
                    </Grid>
                :
                    <Grid container spacing={2}>
                        <Grid item xs={3}>
                            {status}
                        </Grid>
                        <Grid item xs={3}><p className="cusPending">{ticketData.SubStatusName}</p> </Grid>
                        {menus.primary.filter(m => m.show).map((menuItem, i) => <Grid key={i} item xs={3}><p onClick={menuItem.onClick}>{menuItem.icon} {menuItem.name} </p></Grid>)}
                    </Grid>
                }
            </div>
        </div>
        <ScheduleCTCpopup data={ticketData} open={openScheduleCTCpopup} handleClose={() => { setOpenScheduleCTCpopup(false) }} />
        {/* <TicketNeedAttentionPopup data={ticketData} open={openNeedAttentionPopup} handleClose={() => setOpenNeedAttentionPopup(false)} /> */}
    </>
}


/*
BookingCard Component: Other booking card
*/
const BookingCard = (props) => {
    const policy = props.policy || {};
    const { AddUpdateTicket, resendPolicyCopy, openInGenie } = props;

    const menus = {
        primary: [
            {
                name: 'Create new ticket',
                icon: <AddCircleRoundedIcon />,
                onClick: AddUpdateTicket ? () => { AddUpdateTicket(1, { LeadID: policy.LeadId }) } : () => { },
                show: true
            },
            {
                name: 'Send Policy Soft Copy',
                icon: <SendRoundedIcon />,
                onClick: resendPolicyCopy ? () => { resendPolicyCopy(policy.LeadId, policy.ProductId) } : () => { },
                show: policy.ProductId > 0 && [42, 43, 44].indexOf(policy.StatusId) !== -1,
            },
        ],
        secondary: []
    }

    return <>
        <div className="ticketContent">
            <div className="pd10">
                <Grid container spacing={1}>
                    <Grid item xs={1}>

                        <div className="productIcon" > <img src={getProductIcon(policy.ProductId, false, policy.InvestmentTypeID)} alt=" " /> </div>
                        {!openInGenie && 
                            <span className="prodcutName">{policy.ProductName}</span>
                        }
                    </Grid>
                    <Grid item xs={11} container spacing={2}>
                        <Grid item xs={8}><p className="pName">{policy.SupplierName}</p><p className="pNumber">{policy.PolicyNumber}
                        </p></Grid>
                        <Grid item xs={4}><p className="Itemlabel">Booking ID</p><p className="ItemDes">
                            <a href="#" onClick={(e) => { e.preventDefault(); fnOpenBMS(policy.LeadId) }} >{policy.LeadId}</a>
                        </p></Grid>
                        {/* <Grid item xs={8}>
                            <p className="Itemlabel">Issuance date</p>
                            <p className="ItemDes">{policy.IssuanceDate ? dayjs(policy.IssuanceDate).format('DD/MM/YYYY') : 'N/A'}</p>

                        </Grid> */}
                        <Grid item xs={8}>
                            <p className="Itemlabel">Lead Status</p>
                            <p className="ItemDes">{policy.LeadStatusName ? policy.LeadStatusName : 'N/A'}</p>

                        </Grid>
                        <Grid item xs={4}>
                            <p className="Itemlabel">Status Updated On</p>
                            <p className="ItemDes">{policy.LeadStatusUpdatedOn ? policy.LeadStatusUpdatedOn : 'N/A'}</p>

                        </Grid>
                    </Grid>
                </Grid>
            </div>
            <div className="footerBox bluebg">
                <Grid container spacing={2}>
                    {/* <Grid item xs={12}><p><AddCircleOutlinedIcon /> Create new ticket </p></Grid> */}
                    {menus.primary.filter(m => m.show).map((menuItem, i) => <Grid key={i} item xs={6}><p onClick={menuItem.onClick}>{menuItem.icon} {menuItem.name} </p></Grid>)}
                </Grid>
            </div>
        </div>

    </>
}

export const isCreateTicketAllowed = async (ParentLeadId) => {
    let _res = {
        isAllowed: true,
        message: null
    }

    let ConnectCallSF = window.localStorage.getItem('ConnectCallSF');
    let onCall = window.localStorage.getItem("onCall") === "true" ? true : false;

    try {
        ConnectCallSF = JSON.parse(ConnectCallSF);
    }
    catch { ConnectCallSF = null; }

    if (onCall && ConnectCallSF && ParentLeadId === ConnectCallSF.LeadID) {
        _res = {
            isAllowed: true,
            message: null
        }
    }
    else {
        if (rootScopeService.getProductId() !== 131 
            && (User.ProductList && User.ProductList.length > 0 && !User.ProductList.some(product => product.ProductId == '2'))
        ) {
            let isAllowed = await IsCreateTicketAllowedService(ParentLeadId);
            _res = {
                isAllowed: isAllowed,
                message: isAllowed ? null : 'You can only create ticket to recently called customer'
            }
        }
    }

    return _res;
}


/*
Tickets Component: full panel
*/
export default function Tickets(props) {
    const { openInGenie = false } = props;
    const ticketsData = useSelector((state) => state.salesview.ticketsData) || [];
    const issuedPolicies = useSelector((state) => state.salesview.issuedPolicies) || [];
    const refreshTicketsData = useSelector((state) => state.salesview.refreshTicketsData);
    const ParentLeadId = useSelector((state) => state.salesview.parentLeadId);
    const IsRenewal = useSelector((state) => state.salesview.IsRenewal);
    const [openCreateTicketDrawer, setOpenCreateTicketPopup] = useState(false);
    const [createTicketUrl, setCreateTicketPopup] = useState('');
    const [openSendSoftCopySuccessPopup, setOpenSendSoftCopySuccessPopup] = useState(false);

    const [isErrorInDataApi, setIsErrorInDataApi] = useState(false);
    const [isTicketDataLoading, setIsTicketDataLoading] = useState(false);

    const { enqueueSnackbar } = useSnackbar();
    const dispatch = useDispatch();
    const closedTicketStatusesCheck = (rootScopeService.getProductId() === 2 && IsRenewal === 1) ? HealthRenewalclosedTicketStatus : closedTicketStatus;
    const openTickets = Array.isArray(ticketsData) ? ticketsData.filter(ticket => openTicketStatus.includes(ticket.StatusID)) : [];
    const closedTickets = Array.isArray(ticketsData) ? ticketsData.filter(ticket => closedTicketStatusesCheck.includes(ticket.StatusID)) : [];
    const otherBookings = Array.isArray(issuedPolicies) ? issuedPolicies.filter((policy) => {
        let ticketexists = openTickets.find((ticket) => policy.LeadId === ticket.LeadID);
        ticketexists = ticketexists || closedTickets.find((ticket) => policy.LeadId === ticket.LeadID);
        return !ticketexists;
    }) : [];
    const getTickets = useCallback(() => {
        dispatch(updateStateInRedux({ key: 'ticketsData', value: [] }))
        setIsTicketDataLoading(true);
        setIsErrorInDataApi(false);
        getTicketsService()
            .then((res) => {
                if (res === 'No Data found' || !res) {
                    setIsErrorInDataApi(true);
                    return;
                }
                const tickets = res.Data
                dispatch(updateStateInRedux({ key: 'ticketsData', value: tickets }))
                setIsErrorInDataApi(false);
            })
            .catch((err) => {
                dispatch(updateStateInRedux({ key: 'ticketsData', value: [] }))
                setIsErrorInDataApi(true);
            })
            .finally(() => {
                setIsTicketDataLoading(false);
            });
    }, [dispatch])
    const getPolicyIdByBookingId = (bookingId) => {
        let policy = issuedPolicies.find(policy => policy.LeadId === bookingId)
        return policy ? policy.PolicyNumber : '';
    }

    const AddUpdateTicket = async (type, request = {}) => {
        const { LeadID } = request;

        if (type === 1) {
            const _IsCreateTicketAllowed = await isCreateTicketAllowed(ParentLeadId);
            if (_IsCreateTicketAllowed.isAllowed) {
                getCreateTicketURLService(LeadID)
                    .then((url) => {
                        if (url && url.includes && url.includes('https://')) {
                            setOpenCreateTicketPopup(true);
                            setCreateTicketPopup(url);
                        }
                        else {
                            enqueueSnackbar('Unable to fetch create Ticket Url', { variant: 'error', autoHideDuration: 3000 });
                        }

                    })
                    .catch(() => {
                        enqueueSnackbar('Unable to fetch create Ticket Url', { variant: 'error', autoHideDuration: 3000 });
                    })
            }
            else {
                enqueueSnackbar(_IsCreateTicketAllowed.message, { variant: 'error', autoHideDuration: 3000 })
            }
        }
        else if (type === 2) {

            if (request.TicketViewPage) {
                const url = GetIframeURL('AddUpdateTicket', null, { encryptUrl: request.TicketViewPage });
                window.open(url);
            }
            else {
                enqueueSnackbar('No url found', { variant: 'error', autoHideDuration: 3000 });
            }

        }

    }
    const resendPolicyCopy = (leadId, ProductId) => {

        // Email SMS whatsapp
        resendPolicyCopyService(leadId, ProductId, 1)
            .then((result) => {
                result = result && result[0];
                const message = result && result.Message;
                if (result && result.IsSuccess) {
                    enqueueSnackbar(message || "Policy sent via Email", { variant: 'success', autoHideDuration: 3000 });
                    setOpenSendSoftCopySuccessPopup(true);
                    setTimeout(() => { setOpenSendSoftCopySuccessPopup(false) }, 10000);
                }
                else {
                    enqueueSnackbar(message || "Unable to send Policy via Email", { variant: 'error', autoHideDuration: 3000 });
                }
            })
            .catch((err) => {
                enqueueSnackbar(err ? err.Message : 'Some Error occurred while sending Email', { variant: 'error', autoHideDuration: 3000 });
            })
    }

    const handleCloseCreateTicket = () => {
        setOpenCreateTicketPopup(false);
        dispatch(updateStateInRedux({ key: 'refreshTicketsData', value: true }));
    }

    const getCustomerPolicies = () => {
        GetIssuedPolicies( rootScopeService.getCustomerId(), rootScopeService.getProductId() ).then((result) => {
            if (result) {
                dispatch(updateStateInRedux({ key: 'issuedPolicies', value: result }));
            }
        });
    }

    useEffect(() => {
        setIsTicketDataLoading(true);
        getTickets();
    }, [getTickets])
    useEffect(() => {
        if (refreshTicketsData) {
            getTickets();
        }
    }, [dispatch, getTickets, refreshTicketsData])
    useEffect(() => {
        getCustomerPolicies();
    }, [])
    return (
        <>
            <div className="ticketPanel">
                <h3>{'Tickets & Bookings'}</h3>
                {isTicketDataLoading && <LinearProgress />}
                {isErrorInDataApi ?
                    <p>
                        Unable to load tickets
                        <Button variant="text" onClick={getTickets}>Click here to retry</Button>
                    </p>
                    : null
                }
                <Accordion defaultExpanded={true}>
                    <AccordionSummary className="ticketheading"
                        expandIcon={<ExpandMore />}
                    >
                        <img src={CONFIG.PUBLIC_URL + "/images/salesview/otIcon.svg"} alt=" " />   Open Tickets
                    </AccordionSummary>
                    <AccordionDetails>
                        {openTickets.map((ticket) =>
                            <Ticket
                                key={ticket.TicketDetailsID}
                                ticketData={ticket}
                                AddUpdateTicket={AddUpdateTicket}
                                getPolicyIdByBookingId={getPolicyIdByBookingId}
                                resendPolicyCopy={resendPolicyCopy}
                                openInGenie={openInGenie}
                            />
                        )}
                        {(!isTicketDataLoading && !isErrorInDataApi && openTickets.length === 0) &&
                            <>
                                <img src={CONFIG.PUBLIC_URL + "/images/salesview/NoOpentickets.png"} alt=" " />
                                <p className="NodataText">No open tickets found</p>
                            </>
                        }
                    </AccordionDetails>
                </Accordion>
                <Accordion>
                    <AccordionSummary className="ticketheading"
                        expandIcon={<ExpandMore />}
                    >
                        <img src={CONFIG.PUBLIC_URL + "/images/salesview/rtIcon.svg"} alt=" " />  Recently Resolved Tickets
                    </AccordionSummary>
                    <AccordionDetails>

                        {closedTickets.map((ticket) =>
                            <Ticket
                                key={ticket.TicketDetailsID}
                                ticketData={ticket}
                                ticketType='closed'
                                getPolicyIdByBookingId={getPolicyIdByBookingId}
                                AddUpdateTicket={AddUpdateTicket}
                                resendPolicyCopy={resendPolicyCopy}
                                openInGenie={openInGenie}
                            />
                        )}
                        {(!isTicketDataLoading && !isErrorInDataApi && closedTickets.length === 0) ?
                            <>
                                <img src={CONFIG.PUBLIC_URL + "/images/salesview/noRecentlyClosedTtickets.png"} alt=" " />
                                <p className="NodataText">No recently resolved tickets found</p>
                            </>
                            : null
                        }

                    </AccordionDetails>
                </Accordion>
                <Accordion>
                    <AccordionSummary className="ticketheading"
                        expandIcon={<ExpandMore />}
                    >
                        <img src={CONFIG.PUBLIC_URL + "/images/salesview/obIcon.svg"} alt=" " />
                        Other bookings
                    </AccordionSummary>
                    <AccordionDetails >
                        {Array.isArray(otherBookings) && otherBookings.map((policy, index) => <BookingCard key={index} policy={policy} AddUpdateTicket={AddUpdateTicket} resendPolicyCopy={resendPolicyCopy} openInGenie={openInGenie}/>)}
                        {(!isTicketDataLoading && !isErrorInDataApi && otherBookings.length === 0) ?
                            <>
                                <img src={CONFIG.PUBLIC_URL + "/images/salesview/noBookingsFound.png"} alt=" " />
                                <p className="NodataText">No bookings found</p>
                            </>
                            : null
                        }
                    </AccordionDetails>
                </Accordion>
            </div>
            <Drawer open={openCreateTicketDrawer} onClose={handleCloseCreateTicket} anchor='right' style={{ zIndex: 1111 }} >
                <iframe src={createTicketUrl} height="100%" width="1200px" title=" " />
            </Drawer>
            <SoftcopySendSuccess open={openSendSoftCopySuccessPopup} handleClose={() => setOpenSendSoftCopySuccessPopup(false)} />
        </>
    )
}

const getCreateTicketURLService = (LeadId) => {
    const input = {
        url: `api/SalesView/GetTicketServiceURL?UId=${User.UserId}&ECode=${User.EmployeeId}&Name=${User.EmployeeId}&Type=2&Source=MATRIX&LId=${LeadId}&TId=0`,
        service: 'MatrixCoreAPI', timeout: 's'
    }

    return CALL_API(input);
}

const fnOpenBMS = (leadid) => {
    GetBmsUrlService(leadid).then((url) => {
        if (url && url.includes && url.includes('https://')) {
            window.open(url);
        }
        else {
            alert('Unable to open BMS');
        }
    });
}
export const UpdateTicketRemarksService = (data, comment) => {
    const requestData = {
        "LeadID": data.LeadID,
        "TicketId": data.TicketId,
        "Comment": comment,
        "CommentBy": "MatrixWeb",
        "ActionType": 2,
        "TicketdetailId": data.TicketDetailsID,
        "Source": "MyAccount",
        "SubSource": "Matrix",
        "EmployeeID": User.EmployeeId,
        "EmployeeName": User.UserName || User.EmployeeId
    }
    const input = {
        url: `api/Ticket/UpdateTicketRemarksMyAcc`,
        method: 'POST', service: 'MatrixCoreAPI', timeout: 's',
        requestData
    };
    return CALL_API(input)
}


export const IsCreateTicketAllowedService = (parentLeadId) => {
    const input = {
        url: `api/Ticket/IsCreateTicketAllowed/${parentLeadId}`,
        method: 'GET', service: 'MatrixCoreAPI', timeout: 's',
    };
    return CALL_API(input);
}