import { Grid, Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, Tooltip, TextField } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useSnackbar } from "notistack";
import { FetchPaymentRequestDetails, FetchUserPaymentRequest } from "../../../../services/Common";
import { TextInput } from '../../../../components';
import dayjs from 'dayjs';
import './RenewalPaymentLink.scss';

const RenewalPaymentLinkHistory = (props) => {
    const { enqueueSnackbar } = useSnackbar();
    const [history, sethistory] = useState(undefined);
    const [details, setdetails] = useState(undefined);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(5);

    const BackHistory = () => {
        setdetails(undefined)
    }

    const ViewAgentFile = (filepath) => {
        if (filepath && filepath != "")
            window.open(filepath)
        else
            enqueueSnackbar("No file attached", { variant: 'error', autoHideDuration: 3000, });
    }

    useEffect(() => {
        let HistoryList = [];
        FetchUserPaymentRequest().then((result) => {
            if (result) {
                for (let key in result) {
                    HistoryList.push(result[key])
                }
            }
        }).catch((e) => {
            console.log(e);
        })
        sethistory(HistoryList);

    }, []);

    const handleExpandChange = (Id) => {
        FetchPaymentRequestDetails(Id).then((result) => {
            if (result) {
                setdetails(result)
            }
        }).catch((e) => {
            console.log(e);
        })
    }

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    return (
        <>
            {
                history && !details && <>
                    <div className="RenewalLinkPopupHistory">
                        <TableContainer component={Paper} >
                            <Table aria-label="simple table">
                                <TableHead>
                                    <TableRow>
                                        <TableCell>Lead ID</TableCell>
                                        <TableCell>CreatedOn</TableCell>
                                        <TableCell align="center">Status</TableCell>
                                        <TableCell align="center">Action</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {Array.isArray(history) && history.length > 0 ? (
                                        history
                                            .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                                            .map((row, index) => (
                                                <TableRow key={index}>
                                                    <TableCell>{row.LeadId}</TableCell>
                                                    <TableCell>{row.CreatedOn === -62135616600000 || row.CreatedOn === undefined || row.CreatedOn <= 0 ? "N/A" : dayjs(row.CreatedOn).format("DD/MM/YYYY")}</TableCell>
                                                    <TableCell align="right">
                                                        <Button
                                                            variant="outlined"
                                                            className={
                                                                row.StatusId === 2 ? "status-button-txt disablebtn pending" :
                                                                    row.StatusId === 0 ? "status-button-txt disablebtn success" :
                                                                        "status-button-txt disablebtn error"
                                                            }
                                                        >
                                                            {row.Status}
                                                        </Button>
                                                    </TableCell>
                                                    <TableCell align="right">
                                                        <Button
                                                            onClick={() => handleExpandChange(row.Id)}
                                                            variant="outlined"
                                                            color="primary"
                                                            className="button"
                                                        >
                                                            View Details
                                                        </Button>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                    ) : (
                                        <TableRow>
                                            <TableCell colSpan={4} align="center">
                                                <b>No data available</b>
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                            {Array.isArray(history) && history.length > 5 && (
                                <TablePagination
                                    rowsPerPageOptions={[5, 10, 25]}
                                    component="div"
                                    count={history.length}
                                    rowsPerPage={rowsPerPage}
                                    page={page}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage}
                                    className="TablePagination"
                                />
                            )}
                        </TableContainer>
                    </div>
                </>
            }
            {
                details && <>
                    <div className="RenewalLinkPopupHistory">
                        {/* Lead ID & Agent Attached File */}
                        <Grid container spacing={3} >
                            <TextInput name="LeadId" label="Lead ID" value={details.LeadId} disabled sm={12} md={6} xs={12} />
                            {details.AgentAttachedFile && details.AgentAttachedFile !== "" && (
                                <Grid item sm={12} md={6} xs={12}>
                                    <Button onClick={() => ViewAgentFile(details.AgentAttachedFile)} variant="outlined" className="button-txt ">
                                        Agent Attached File
                                    </Button>
                                </Grid>
                            )}
                            {details.AgentAttachedFile == "" && (
                                <Grid item sm={12} md={12} xs={12}>
                                    <hr />
                                </Grid>
                            )}
                            <Grid item sm={12} md={6} xs={12}>
                                <Tooltip title={details.InsurerName} arrow>
                                    <TextField
                                        id="InsurerName"
                                        name="InsurerName"
                                        label="Insurer Name"
                                        value={details.InsurerName.length > 30 ? details.InsurerName.substring(0, 27) + "..." : details.InsurerName}
                                        fullWidth
                                        size="small"
                                        variant="outlined"
                                        disabled
                                        InputLabelProps={{ shrink: ['', undefined, null].includes(details.InsurerName) ? false : true }}
                                    />
                                </Tooltip>
                            </Grid>
                            <Grid item sm={12} md={6} xs={12}>
                                <Tooltip title={details.PlanName} arrow>
                                    <TextField
                                        id="PlanName"
                                        name="PlanName"
                                        label="Plan Name"
                                        value={details.PlanName.length > 30 ? details.PlanName.substring(0, 27) + "..." : details.PlanName}
                                        fullWidth
                                        size="small"
                                        variant="outlined"
                                        disabled
                                        InputLabelProps={{ shrink: ['', undefined, null].includes(details.PlanName) ? false : true }}
                                    />
                                </Tooltip>
                            </Grid>

                            <TextInput name="ProposerName" label="Proposer Name" value={details.ProposerName} disabled sm={12} md={6} xs={12} />
                            <TextInput name="NoticePremium" label="Notice Premium" value={details.PremiumAmount} disabled sm={12} md={6} xs={12} />

                            <TextInput name="PolicyNo" label="Policy Number" value={details.PolicyNo} disabled sm={12} md={6} xs={12} />
                            <TextInput name="SumInsured" label="Sum Insured" value={details.SumInsured} disabled sm={12} md={6} xs={12} />

                            <Grid item sm={12} md={12} xs={12}>
                                <Tooltip title={details.PaymentReason} arrow>
                                    <TextField
                                        id="ChangesReason"
                                        name="ChangesReason"
                                        label="Changes/Reason"
                                        value={details.PaymentReason && details.PaymentReason.length > 70 ? details.PaymentReason.substring(0, 65) + "..." : details.PaymentReason}
                                        fullWidth
                                        size="small"
                                        variant="outlined"
                                        disabled
                                        InputLabelProps={{ shrink: ['', undefined, null].includes(details.PaymentReason) ? false : true }}
                                    />
                                </Tooltip>
                            </Grid>

                            {/* Cancellation Reason (if exists) */}
                            {details.CancellationReason && details.CancellationReason !== "" && (
                                <>
                                    <TextInput name="CancellationReason" label="Cancellation Reason" value={details.CancellationReason} disabled sm={12} md={6} xs={12} />
                                    {details.CancellationAttachment && details.CancellationAttachment !== "" &&
                                        <Grid item sm={12} md={6} xs={12}>
                                            <Button onClick={() => ViewAgentFile(details.CancellationAttachment)} variant="outlined" className="CancellationAttechBtn">
                                                Cancellation Attached File
                                            </Button>
                                        </Grid>
                                    }
                                </>
                            )}
                            <Grid item sm={12} md={12} xs={12}>
                                <Button onClick={BackHistory} variant="outlined" className="SubmitBtn" sm={12} md={6} xs={12}>
                                    Back
                                </Button>
                            </Grid>
                        </Grid>

                    </div>
                </>
            }
        </>

    );
}
export default RenewalPaymentLinkHistory