import { getQueryParam, localStorageCache } from "../utils/utility";
import { getCookie } from "../store/token-store/Token-Store";
import { SV_CONFIG, CONFIG } from "../appconfig";

// DONOT import User, rootScopeService here, it will create circular dependency
// import User from "../services/user.service";
// import rootScopeService from "../services/rootScopeService";


// export { default as name } from './name';
function selectText(event) {
    var element = event.target
    var range;
    if (document.selection) {
        // IE
        range = document.body.createTextRange();
        range.moveToElementText(element);
        range.select();
    } else if (window.getSelection) {
        range = document.createRange();
        range.selectNode(element);
        window.getSelection().removeAllRanges();
        window.getSelection().addRange(range);
    }
}
export const copyToClipboard = (event) => {
    selectText(event);
    document.execCommand("copy");
}
export function IsProgressiveApp() {
    try {
        let _src = getQueryParam('IsProgressiveApp');
        if (_src && _src !== "") {
            localStorageCache.writeToCache(`IsProgressiveApp`, _src, 10 * 60 * 60 * 1000);
        }
        else {
            _src = localStorageCache.readFromCache(`IsProgressiveApp`);
        }
        return ((!_src || _src == "0") ? false : true);
    }
    catch {
        return false;
    }
    // try {
    //     const url = new URL(window.location.href);
    //     const params = new URLSearchParams(url.search);
    //     let src = params.get("IsProgressiveApp");

    //     if (!src) {
    //         return false;
    //     }
    //     return src=="0"?false:true;
    // }
    // catch {
    //     return false;
    // }
}

/* GoogleAnalytics helpers  */
export const gaEventTracker = (eventAction, eventLabel, leadId, eventCategory = "SalesView", eventValue = 1) => {
    if (CONFIG.BUILD_ENV !== 'live') {
        eventCategory = eventCategory + "_QA"
    }
    try {
        const data = {
            eventCategory: eventCategory,
            eventAction: eventAction,
            eventLabel: eventLabel,
            eventValue: eventValue,
            leadid: leadId,
            //customerid: ""  //todo
        };

        window.dataLayer = window.dataLayer || [];
        if (!window.gtag) {
            // eslint-disable-next-line no-undef
            window.gtag = () => { window.dataLayer.push(arguments); }
        }
        window.gtag("event", 'eventTracking', data);
    }
    catch (e) {
        // eslint-disable-next-line no-console
        console.error('gaError error: ', e);
    }
};

export const gaEventNames = {
    sendinvite: 'agentVCshareinvite',
    screenshare: 'agentStartScrensharing',
    videocall: 'agentStartVC',
    autoPopupOpen: 'agentVCPopup',
    VCButton: 'AgentclickVCbutton',
    errorFetchingURL: 'errorFetchingURLsalesview',
    errorBoundary: 'NEWSV_ERROR_BOUNDARY',
    apiError: 'NEWSV_API_ERROR',
    PBHelplineNumber: 'PBHelplineNumber',
    CustomerEmails: 'CustomerEmails',
    ClaimComments: 'ClaimComments',
    PBTap: 'PBTap',
    QualityCriteria: 'QualityCriteria',
    DocListing: 'DocListing',
    BookingDesk: 'BookingDesk'
}
/* GoogleAnalytics helpers end  */

export function pushNotification(message, title = 'New Notification from Matrix', icon) {
    const createPushNotificaton = (permission) => {
        // If the user accepts, let's create a notification
        if (permission === 'granted') {
            try {
                new Notification(title, {
                    body: message,
                    icon: icon
                });
            }
            catch (e) {
                // const swUrl = `${process.env.PUBLIC_URL}/service-worker.js`;

                // navigator.serviceWorker.register(swUrl);
                // navigator.serviceWorker.ready.then(function(registration)
                // {
                // 	registration.showNotification(title, {
                // 		body : message,
                // 		icon : icon
                // 	});
                // });
            }
            // new Notification(title, {
            // 	body : message,
            // 	icon : icon
            // });
        }
    };

    // Let's check if the browser supports notifications
    if (!('Notification' in window)) {
        return;
    }
    // Let's check whether notification permissions have already been granted
    else if (Notification.permission === 'granted') {
        createPushNotificaton(Notification.permission);
    }

    // Otherwise, we need to ask the user for permission
    else if (Notification.permission !== 'denied') {
        Notification.requestPermission().then(createPushNotificaton);
    }
}

export const getRoleNameFromRoleId = (RoleId) => {
    switch (RoleId) {
        case 13:
            return 'Agent';
        case 12:
            return 'Supervisor';
        case 11:
            return 'Manager';
        case 2:
            return 'Admin'
        default:
            return 'Agent'
    }
}
export const GetStatus = (x) => {
    if (x == '1') return 'New';
    else if (x == '2') return 'Valid';
    else if (x == '3') return 'Contacted';
    else if (x == '4') return 'Interested';
    else if (x == '11') return 'Prospect';
    else return '';
}

export const openingSources = {
    MatrixGoApp: "MatrixGoApp"
}
export function getSourcefromQuery() {
    try {
        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);
        let src = params.get("src");
        return src;
    }
    catch {
        return "";
    }


}

export function IsSourceCustomerWhatsapp() {
    try {
        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);
        let src = params.get("src");
        if (!src) {
            return false;
        }
        return (['customerwhatsapp', 'customersms'].indexOf(src.toLowerCase())) > -1 ? true : false
    }
    catch {
        return false;
    }


}

// export const checkPageSrc = (source) => {
export function checkPageSrc(source) {
    try {
        let _src = getQueryParam('src');
        if (_src && _src !== "") {
            localStorageCache.writeToCache(`pageOpeningSource`, _src);
        }
        else {
            _src = localStorageCache.readFromCache(`pageOpeningSource`);
        }
        return (_src.toLowerCase() === source.toLowerCase());
    }
    catch {
        return false;
    }
}
/**
 * 
 * @returns {Boolean} Flag to check if source is Matrix Go App
 */

export function IsMatrixGoApp() {
    try {
        let matGoApp;
        if (openingSources && openingSources.MatrixGoApp) {
            matGoApp = openingSources.MatrixGoApp
        }
        else {
            matGoApp = 'MatrixGoApp'
        }
        return checkPageSrc(matGoApp);
    }
    catch (e) {
        console.log(e);
        return false
    }
}
/**
 * 
 * @returns {Boolean} Flag to stop progressive and use non-progressive wfh calling, despite of configuration
 */
export function isNonProgressiveWfhCalling() {
    try {
        let matGoApp;
        if (openingSources && openingSources.MatrixGoApp) {
            matGoApp = openingSources.MatrixGoApp
        }
        else {
            matGoApp = 'MatrixGoApp'
        }
        return checkPageSrc(matGoApp);
    }
    catch (e) {
        console.log(e);
        return false
    }
}
// export const isNonProgressiveWfhCalling = () => {
//     try {
//         return checkPageSrc(openingSources.MatrixGoApp);
//     }
//     catch {
//         return false
//     }
// }


export const getAsteriskToken = () => {
    let asteriskToken = localStorage.getItem('AsteriskToken');

    if (!asteriskToken) {
        let MatrixToken = getCookie('MatrixToken');
        if (MatrixToken) {
            try {
                MatrixToken = JSON.parse(atob(MatrixToken));
                // let EmployeeId = MatrixToken.EmployeeId;
                asteriskToken = MatrixToken.AsteriskToken;
            } catch (e) {
                asteriskToken = getCookie('AsteriskToken');
            }
        }
        else {
            asteriskToken = getCookie('AsteriskToken');
        }
    }
    return asteriskToken;
}


export const hideCallButton = function (User) {
    var result = false;
    if (User.IsProgressive && User.IsOneLead) {
        result = true;
    } else if (User.RoleId !== 13) {
        result = false;
    }
    return result;
}

export const isFOSChurnAgent = function (User) {
    // group of agents, who handles lead churned from Dedicated-agents bucket

    let FOSChurnAgentGrps = SV_CONFIG.FOSChurnAgentGrps || [];
    let usergrp = User.UserGroupList || [];
    let isChurnAgent = false;

    usergrp.forEach((item, key) => {
        if (FOSChurnAgentGrps.indexOf(item.GroupId) > -1) {
            isChurnAgent = true;
        }
    });
    return isChurnAgent;
}

export const ChkIsFresh = function (allLeads) {
    var IsFresh = false;
    if (Array.isArray(allLeads) && allLeads.length > 0) {
        const isNotFresh = allLeads.find(
            (o) => o.LeadSource.toLowerCase() === "renewal");
        if (!isNotFresh) {
            IsFresh = true;
        }
    }
    return IsFresh;
}
export const ChkIsRenewal = (allLeads, activeRenewal = true) => {
    var IsRenewal = false;
    if (Array.isArray(allLeads) && allLeads.length > 0) {
        allLeads.forEach((vdata, index) => {
            if (vdata.LeadSourceId === 6 && (!activeRenewal || [1, 2, 3, 4, 11].indexOf(vdata.StatusId) !== -1)) {
                IsRenewal = true;
            }
        })
    }
    return IsRenewal;
}

export const last5VcLeadKey = 'last5VcLead';
export const updateVcLeadLS = (leadId, pbmeetLink, allLeads) => {
    let roomId = '', custName = '';
    try {
        const url = new URL(pbmeetLink);
        roomId = url.pathname.split('/').pop();

        if (Array.isArray(allLeads)) {
            allLeads.forEach((lead) => {
                if (!custName && lead.Name) {
                    custName = lead.Name
                }
                if (lead.LeadID === lead.ParentID && lead.Name) {
                    custName = lead.Name
                }
            })
        }
    }
    catch { }

    let last5VcLeadData = localStorageCache.readFromCache(last5VcLeadKey);
    if (!Array.isArray(last5VcLeadData)) {
        last5VcLeadData = [];
    }
    const existingLeadIndex = last5VcLeadData.findIndex((data) => data.leadId === leadId);

    if (existingLeadIndex !== -1) {
        // Update roomId for the existing leadId
        last5VcLeadData[existingLeadIndex].roomId = roomId;
    } else {
        // Add a new entry if leadId doesn't exist
        if (last5VcLeadData.length > 4) {
            last5VcLeadData.splice(0, 1);
        }
        last5VcLeadData.push({ leadId, roomId, custName });
    }
    localStorageCache.writeToCache(last5VcLeadKey, last5VcLeadData, 4 * 60 * 60 * 1000);
}

export const isShowScreenshareNotif = (connectCallSF) => {

    let matrixCallLeadId = connectCallSF && connectCallSF.LeadID;
    if (!matrixCallLeadId && connectCallSF && connectCallSF.lead) {
        matrixCallLeadId = connectCallSF.lead.LeadId;
    }

    // ongoing screenshare + not connected(matrix call) on samelead
    const currentSSCallId = getCookie('currentSSCallId');
    // currentSSCallId is set from pbmeet when screenshare call is ongoing
    let last5VcLeadData = localStorageCache.readFromCache(last5VcLeadKey);
    if (currentSSCallId && Array.isArray(last5VcLeadData)) {
        const room = last5VcLeadData.find(roomData => roomData.roomId === currentSSCallId);
        if (room && room.leadId != matrixCallLeadId) {
            return `${room.custName} - ${room.leadId}`;
        }
    }
    return false;
}

export const findLeadIdAndCallIdOfSS = () => {

    const currentSSCallId = getCookie('currentSSCallId'); // Getting screenshare callId
    let last5VcLeadData = localStorageCache.readFromCache(last5VcLeadKey);
    if (currentSSCallId && Array.isArray(last5VcLeadData)) {
        const room = last5VcLeadData.find(roomData => roomData.roomId === currentSSCallId);
        if (room) {
            return {callId: currentSSCallId, leadId: room.leadId};
        }
    }
    return null;
}

export function IsAppointmentDomain() {
    try {
        if (
            window.location.origin == 'https://appointmentsqa.policybazaar.com'
            || window.location.origin == 'https://appointment.policybazaar.com'
        ) {
            return true;
        }
        return false
    }
    catch {
        return false;
    }
}