import React, { useCallback, useEffect, useState } from "react";
import { useLocation } from "react-router";
import { CONFIG,SV_CONFIG } from "../../../appconfig";
import User from "../../../services/user.service";
import './SmeViewQuotesNew.scss'
import { Grid, LinearProgress, Button, InputAdornment, Switch } from "@mui/material";
import { useSnackbar } from "notistack";
import { CALL_API } from "../../../services";
import masterService from "../../../services/masterService";
import FileDropzone from "../../../components/Dropzone";
import { SelectDropdown, TextInput } from "../../../components";
import MultipleSelectDropdown from "../../../components/MultipleSelectDropdown";
import { ViewAdditionalFilePopup } from "../../SV/Main/Modals/ViewAdditionalFilePopup";
import { SmeQuoteHistoryPopup } from "../../SV/Main/Modals/SmeQuoteHistoryPopup";
import { ErrorBoundary } from "../../../hoc";
import Textarea from '@mui/material/TextareaAutosize';
import CloseIcon from "@mui/icons-material/Close";
import AttachmentIcon from '@mui/icons-material/Attachment';
import DeleteIcon from '@mui/icons-material/Delete';
import HistoryIcon from '@mui/icons-material/History';
import dayjs from "dayjs";
import { AddCircleOutline, ExpandMore } from "@mui/icons-material";
import rootScopeService from "../../../services/rootScopeService";
import { UploadQcrUrlPopup } from "../../SV/Main/Modals/UploadQcrUrlPopup";
import { default as Data } from "../../../assets/json/StaticData";

let DocTypeId = 805;
let DocumentType = 'RFQDocuments';
let QuoteSourceCJ = 'CJ';
let QueryRaisedId = 8;
let NonEB = "Non-EB";

const PolicyTypes = Data.PolicyType.SME;
const QuotesStatusMaster = [
    { "QuotesStatusId": 1, "Name": "Quotes Required", "RoleId": 13 },
    { "QuotesStatusId": 2, "Name": "Quotes Shared", "RoleId": 0 },
    { "QuotesStatusId": 3, "Name": "Revision Required", "RoleId": 13 },
    { "QuotesStatusId": 4, "Name": "Revision Shared", "RoleId": 0 },
    { "QuotesStatusId": 5, "Name": "RFQ Sent", "RoleId": 0 },
    { "QuotesStatusId": 6, "Name": "Rejected", "RoleId": 0 },
    { "QuotesStatusId": 8, "Name": "Query Raised", "RoleId": 0 },
    { "QuotesStatusId": 9, "Name": "Query Resolved", "RoleId": 0 }
];
const FreshPolicySubTypes = [
    { "Id": 1, "Name": "Proposed option" },
    { "Id": 2, "Name": "Graded quote" }
];
const RolloverPolicySubTypes = [
    { "Id": 1, "Name": "As per expiry term only" },
    { "Id": 2, "Name": "As per expiry term with proposed option" },
    { "Id": 3, "Name": "As per expiry term with Graded quote" },
    { "Id": 4, "Name": "As per expiry term with Graded Quote and proposed option" }
];
const FamilyType = [

    {
        "Id": 1,
        "Name": "E"
    },
    {
        "Id": 2,
        "Name": "ESK"
    },
    {
        "Id": 3,
        "Name": "ESKP"
    },
    {
        "Id": 4,
        "Name": "ES"
    },
    {
        "Id": 5,
        "Name": "EP"
    },
    {
        "Id": 6,
        "Name": "ESKEP"
    },
    {
        "Id": 7,
        "Name": "E+AD"
    }
];
const MultipleFamilyType = [

    ...FamilyType
];

const getQuotesService = (LeadId) => {
    const input = {
        url: `api/SalesView/GetSMEQuotes/${LeadId}`,
        method: 'GET', service: 'MatrixCoreAPI',

    };
    return CALL_API(input);
}

const saveQuotesService = (reqData) => {
    const _input = {
        url: `api/SalesView/AddUpdateSMEQuotes`,
        method: 'POST', service: 'MatrixCoreAPI',// Need to change internal
        requestData: reqData,
        timeout: "l"
    }
    return CALL_API(_input)
};

const commonuploadFileToUrlService = (reqData, url) => {
    const input = {
        url,
        method: 'POST', service: 'commonUploadFileToUrl',
        requestData: reqData
    };

    return CALL_API(input);
    
}

const updateSMEQuoteStatusService = (reqData) => {
    const input = {
        url: 'api/SalesView/UpdateSMEQuotesStatusV2',
        method: 'POST', service: 'MatrixCoreAPI',
        requestData: reqData,
        timeout: "l"
    };
    return CALL_API(input);
}

const OTCQuotes = [{ ID: 1, Name: "Yes" }, { ID: 2, Name: "No" }];

const initialSME = {
    SMEComment: "",
    QuotesStatus: {},
    SMEQuote: [],
    QuoteSubStatus: {},
    Revision: "",
    PolicyType: null,
    PolicySubType: null
}
const initialSMEQuote = { Supplier: {}, Price: "", OTC: "" };

function useQuery() {
    return new URLSearchParams(useLocation().search);
}

export default function SmeViewQuotesNew() {
    const query = useQuery();
    const allowAction = Boolean([1, "1", "true", true].includes(query.get('allowAction')));
    const [leadId, setLeadId] = useState(0);
    const [productId, setProductId] = useState(0);
    const [leadSourceId, setLeadSourceId] = useState(0);
    const [subProductTypeId, setSubProductTypeId] = useState(0);
    const [subProductTypeIds, setSubProductTypeIds] = useState([]);
    const RoleId = User.RoleId;
    const { enqueueSnackbar } = useSnackbar();
    const [loading, setLoading] = useState(true);
    const [IsfileUploading, setIsfileUploading] = useState(false);
    const [TotalFileCount, setTotalFileCount] = useState(0);
    const [QuotesStatusId, setQuotesStatusId] = useState(0);
    const [SMEQuote, setSMEQuote] = useState(initialSMEQuote);
    const [SMEQuotesList, setSMEQuotesList] = useState({});
    const [SMEQuotesNewList, setSMEQuotesNewList] = useState({ file: null });
    const [maxCtrl, setMaxCtrl] = useState(1);
    const [suppliers, setSuppliers] = useState([]);
    const [SMERevisionList, setSMERevisionList] = useState([]);
    const [SME, setSME] = useState(initialSME);
    const [SMEQuoteSubStatus, setSMEQuoteSubStatus] = useState([]);
    const [areChangesTemporary, setAreChangesTemporary] = useState(false);
    const [isQuoteHistoryPopupOpen, setIsQuoteHistoryPopupOpen] = useState(false);
    const [originalQuoteData, setOriginalQuoteData] = useState([]);
    const [SelectedSuppliers, setSelectedSuppliers] = React.useState([]);
    const [SelectedFamilyType, setSelectedFamilyType] = React.useState([]);
    const [MaxMasterID, setMaxMasterID] = useState(0)
    const [IsRFQUploaded, setIsRFQUploaded] = useState(false)
    const [IsAdditionalFile, setIsAdditionalFile] = useState(false)
    const [IsQuoteFileSaving, setIsQuoteFileSaving] = useState(false)
    const [AdditionalQuote, setAdditionalQuote] = useState({})
    const [MultipleSupplier, setMultipleSupplier] = useState([]);
    const [RFQCount, setRFQCount] = useState(0);
    const [IsToggleOpen, setIsToggleOpen] = useState([]);
    const [SumAssured, setSumAssured] = useState(0);
    const [LeadStatusId, setLeadStatusId] = useState(0);
    const [SubProductProperty, setSubProductProperty] = useState('');
    const [UploadQcrUrl, setUploadQcrUrl] = useState(null);
    const [IsUploadQcrUrlPopupOpen, setIsUploadQcrUrlPopupOpen] = useState(false);
    const [GHIRFQOpen, setGHIRFQOpen] = useState(false);
    const [UploadRFQUrl, setUploadRFQUrl] = useState(null);
    const [CustId, setCustId] = useState(0);
    const [SMEFosAgent, setSMEFosAgent] = useState(false);

    let MaxFileLimit = 12, MaxQuotesLimit = 18, RFQMaxLimit = 8;
    let IsDisabled = (RoleId !== 13 && productId == 131 && [1, 2, 3, 4, 11].indexOf(LeadStatusId) == -1);
    const isSMEFosAgent = () => {
        try{
            const fosGroups = SV_CONFIG["SMEFOSGroups"];
            const groupList = User.UserGroupList;
    
            if (Array.isArray(fosGroups) && Array.isArray(groupList)) {
                setSMEFosAgent(groupList.some(group => fosGroups.includes(group.GroupId)));
            }
        }
        catch{
            // Do nothing
        }
    };
    useEffect(() => {
        let reqIndex = CONFIG.PUBLIC_URL ? 4 : 2;
        let routeParams = window.location.pathname.split('/');
        let leadSourceid = parseInt(atob(routeParams[reqIndex]).split('/')[2]);
        if (!routeParams) {
            return 0;
        }

        setLeadId(parseInt(atob(routeParams[reqIndex]).split('/')[0]));
        setProductId(parseInt(atob(routeParams[reqIndex]).split('/')[1]));
        setSubProductTypeId(parseInt(atob(routeParams[reqIndex]).split('/')[3]));
        setSumAssured(parseInt(atob(routeParams[reqIndex]).split('/')[4]));
        setLeadStatusId(parseInt(atob(routeParams[reqIndex]).split('/')[5]));
        setLeadSourceId(leadSourceid);
        setSubProductProperty(atob(routeParams[reqIndex]).split('/')[6]);
        setCustId(parseInt(atob(routeParams[reqIndex]).split('/')[7]));

        if (leadSourceid === 6){
            QuotesStatusMaster.push({ "QuotesStatusId": 7, "Name": "Incumbent Quote Shared", "RoleId": 0 })
        }

        setSubProductTypeIds(SV_CONFIG['SmeEbSubProductTypeIds']['L']?SV_CONFIG['SmeEbSubProductTypeIds']['L']:[]);
        isSMEFosAgent();
    }, [])

    const closePopup = () => {
        setIsAdditionalFile(false);
    }
    const ViewAdditionalFile = (quote) => {
        setIsAdditionalFile(true);
        setAdditionalQuote(quote);
    }    

    const ResetSMEQuotes = () => {
        setLoading(true);
        setSME(initialSME);
        setTotalFileCount(0);
        setSMEQuotesList({});
        setSMEQuote(initialSMEQuote)
        setQuotesStatusId(0);
        setMaxCtrl(1);
        setAreChangesTemporary(false);
        getQuotes();
        clearFileInput();
        setSelectedSuppliers([]);
        setSelectedFamilyType([]);
    }

    const getQuotes = useCallback(() => {
        getQuotesService(leadId).then((response) => {
            setLoading(false);
            let resultData = [];
            let _SMEQuotesList = {};
            let _SME = {
                SMEComment: "",
                QuotesStatus: {},
                SMEQuote: [],
                QuoteSubStatus: {},
                Revision: "",
                PolicyType: null,
                PolicySubType: null
            }
            let _maxCtrl = 1;

            if (response) {
                resultData = response;
            }
            let TempRFQCount = 0;
            let tempIstoggleOpen = [];
            resultData.forEach((quote) => {
                setQuotesStatusId(quote.QuotesStatus.QuotesStatusId)
                _SME.QuotesStatus.QuotesStatusId = quote.QuotesStatus.QuotesStatusId;
                _SME.QuoteSubStatus.ID = quote.QuotesStatus.SubStatusId;

                let key = "SMEQuotes" + quote.ControlId + "_" + quote.QuoteType + "_" + quote.RowId;
                _SMEQuotesList[key] = quote;
                if (quote.QuoteType == 1) {
                    key = quote.ControlId
                    tempIstoggleOpen[key] = false;
                }


                if (quote.ControlId >= _maxCtrl)
                    _maxCtrl = quote.ControlId + 1;
                if (quote.QuoteType == 1) {
                    TempRFQCount++;
                }
            });

            setRFQCount(TempRFQCount);
            setSMEQuotesList(_SMEQuotesList);
            setMaxCtrl(_maxCtrl);
            setSMEQuotesNewList({ ControlId: _maxCtrl, file: null });
            let QuoteFilelength = 0;
            let _originalQuoteData = {};
            if (resultData.length > 0) {
                _SME.Revision = resultData[0].ReasonName;
                if (resultData[0] && Array.isArray(resultData[0].QuotePriceList) && resultData[0].QuotePriceList.length > 0) {
                    _SME.SMEQuote = [];
                    resultData[0].QuotePriceList.forEach(function (item, index) {
                        if (item.DocId || item.FilePath) {
                            QuoteFilelength++;
                        }
                        let IsAnySupplierEditable = false;
                        if (item.SupplierId == 0) {
                            IsAnySupplierEditable = true;
                        }
                        _SME.SMEQuote.push({ MasterID: index, FamilyId: item.FamilyId, PrimaryId: item.SMEQuoteId, Supplier: { OldSupplierId: item.SupplierId }, Price: item.Price, OTC: { ID: item.OTC }, QuoteId: item.QuoteId, UpdatedOn: item.UpdatedOn, IsEditable: false, UpdatedDate: item.UpdatedDate, ControlId: item.ControlId, FilePath: item.FilePath, CreatedBy: item.CreatedBy, IsAnySupplierEditable, UploadedBy:item.UploadedBy, DocId: item.DocId })
                        _originalQuoteData[item.QuoteId] = { Supplier: { OldSupplierId: item.SupplierId }, Price: item.Price, OTC: { ID: item.OTC }, QuoteId: item.QuoteId };
                    }
                    );
                    let l = resultData[0].QuotePriceList.length;
                    setMaxMasterID(l - 1);

                }
            }
            setTotalFileCount(resultData.length + QuoteFilelength);
            setSME(_SME);
            setOriginalQuoteData(_originalQuoteData);
        }).catch((e) => {
            setLoading(false);
        })

        setTimeout(function() {
            if(IsNewQcrPanelSubproduct())
                GetSetPolicyType(true);
        }, 1000);

    }, [leadId]);
  
    const handleChange = (e, Quote, MasterID) => {
        let val = e.target.value;
        let e_name = e.target.name;

        switch (e_name) {
            case 'insurer':
                let i = -1;
                SME.SMEQuote.forEach((item, index) => {
                    if (item.MasterID === MasterID) {
                        i = index;
                    }

                })
                let tempQuoteFileInputfamily = SME.SMEQuote[i].QuoteFileInput;
                let arr1 = JSON.parse(JSON.stringify(SME.SMEQuote))
                arr1[i].QuoteFileInput = tempQuoteFileInputfamily
                if (i !== -1 && arr1[i]) {
                    arr1[i].Supplier.OldSupplierId = e.target.value;
                }
                setSME({ ...SME, SMEQuote: arr1 })
                break;
            case 'family':
                let p = -1;
                SME.SMEQuote.forEach((item, index) => {
                    if (item.MasterID === MasterID) {
                        p = index;
                    }

                })
                let tempQuoteFileInputprice = SME.SMEQuote[p].QuoteFileInput;
                let arr2 = JSON.parse(JSON.stringify(SME.SMEQuote))
                arr2[p].QuoteFileInput = tempQuoteFileInputprice
                if (p !== -1 && arr2[p]) {
                    arr2[p].FamilyId = e.target.value;
                }
                setSME({ ...SME, SMEQuote: arr2 })
                break;

            case 'price':
                if (isNaN(+val))
                    return;
                let q = -1;
                SME.SMEQuote.forEach((item, index) => {
                    if (item.MasterID === MasterID) {
                        q = index;
                    }

                })
                let tempQuoteFileInput = SME.SMEQuote[q].QuoteFileInput;
                let arr3 = JSON.parse(JSON.stringify(SME.SMEQuote))
                arr3[q].QuoteFileInput = tempQuoteFileInput;
                if (q !== -1 && arr3[q]) {
                    arr3[q].Price = e.target.value;
                }
                setSME({ ...SME, SMEQuote: arr3 })
                break;

            case 'otcQuote':
                let o = -1;
                SME.SMEQuote.forEach((item, index) => {
                    if (item.MasterID === MasterID) {
                        o = index;
                    }

                })
                let tempQuoteFileInputotc = SME.SMEQuote[o].QuoteFileInput;
                let arr4 = JSON.parse(JSON.stringify(SME.SMEQuote))
                arr4[o].QuoteFileInput = tempQuoteFileInputotc
                if (o !== -1 && arr4[o]) {
                    arr4[o].OTC = e.target.checked ? OTCQuotes[0] : OTCQuotes[1];
                }
                setSME({ ...SME, SMEQuote: arr4 })
                break;
            case 'comment':
                setSME({ ...SME, SMEComment: val })
                break;
            case 'RevisionReason':
                setSME({ ...SME, Reason: val })
                break;
            case 'status':
                setSME({ ...SME, QuotesStatus: val });
                break;
            case 'subStatus':
                setSME({ ...SME, QuoteSubStatus: val })
                break;
            case 'PolicyType':
                setSME({ ...SME, PolicyType: val, PolicySubType: null})
                break;
            case 'PolicySubType':
                setSME({ ...SME, PolicySubType: val })
                break;
            default:
                break;
        }
    };

    const handleChangeMultiple = (event) => {

        let e_name = event.target.name;
        switch (e_name) {
            case 'insurer':
                let arr = event.target.value, flag = 0;
                if (event.target.value.length < 5) {
                    let obj = arr.find((o, i) => {
                        if (o.OldSupplierId == 0 && arr.length > 1) {
                            flag = 1;

                            enqueueSnackbar('You can either select "Any" OR some Insurer', {
                                variant: "error",
                                autoHideDuration: 3000,
                                style: { whiteSpace: 'pre-line' }
                            });
                            return null;

                        }

                    });
                    if (flag == 0) {
                        setSelectedSuppliers(arr);
                    }
                }
                else {
                    enqueueSnackbar('Maximum 4 insurer can be selected', {
                        variant: "error",
                        autoHideDuration: 3000,
                        style: { whiteSpace: 'pre-line' }
                    });

                }
                break;
            case 'FamilyType':
                let arr1 = event.target.value, flag1 = 0;                
                if (flag1 == 0) {
                    setSelectedFamilyType(arr1)
                }
                break;
            default:
                break;
        }
    };

    const CreateXMLFormat = () => {
        let a = "<root>";
        if(subProductTypeIds.includes(subProductTypeId)){
            SelectedSuppliers.forEach((s) => {
            SelectedFamilyType.forEach((f) => {
                let sub = "<q><supplierId>" + s.OldSupplierId + "</supplierId><FamilyId>" + f.Id + "</FamilyId><price>0</price><OTC>0</OTC></q>";
                a = a + sub;
            })
        })
        }else{
            SelectedSuppliers.forEach((s) => {
                    let sub = "<q><supplierId>" + s.OldSupplierId + "</supplierId><FamilyId>0</FamilyId><price>0</price><OTC>0</OTC></q>";
                    a = a + sub;
            })   
        }
        a = a + "</root>";
        return a;
    }

    const handleFileDrop = (file) => {
        setSMEQuotesNewList({ ...SMEQuotesNewList, ControlId: maxCtrl, file: file[0] });
        setIsRFQUploaded(true);
    }

    const clearFileInput = () => {
        setSMEQuotesNewList({ ...SMEQuotesNewList, ControlId: maxCtrl, file: null });
        setIsRFQUploaded(false);
    }

    const IsNewQcrPanelSubproduct = () => {
        let isNewQcrPanelSubproduct = false;
        try
        {
            isNewQcrPanelSubproduct = SV_CONFIG["SmeNewQcrPanelSubproducts"].indexOf(subProductTypeId) > -1;
        }
        catch
        {
            // Do nothing
        }
        return isNewQcrPanelSubproduct;
    }

    const ShowNewQcrPanel = () => {
        let isNewQcrPanelSubproduct = false;
        try
        {
            isNewQcrPanelSubproduct = IsNewQcrPanelSubproduct() &&
                                        SME.PolicyType &&
                                        SME.PolicySubType 
                                        //&&
                                        //SME.PolicySubType.Id === 1 &&
                                        //(SME.PolicyType.PolicyTypeId === 1 || SME.PolicyType.PolicyTypeId === 2);
        }
        catch
        {
            // Do nothing
        }
        return isNewQcrPanelSubproduct;
    }

    const GetSetPolicyType = (fetchData) => {
        let isValid = true;
        let policyTypeId = 0;
        let policySubTypeId = 0

        if(!fetchData) {
            if(SME.PolicyType && SME.PolicySubType) {
                policyTypeId = SME.PolicyType.PolicyTypeId
                policySubTypeId = SME.PolicySubType.Id
            }
            else {
                isValid = false;
                enqueueSnackbar("Please select Type of Case and Coverage Type", { variant: 'error', autoHideDuration: 3000, });
            }
        }

        if(isValid) {
            const _input = {
                url: "api/SalesView/GetSetRfqPolicyTypeData?LeadId=" + leadId + "&PolicyTypeId=" + policyTypeId + "&PolicySubTypeId=" + policySubTypeId + "&FetchData=" + fetchData,
                method: 'GET', 
                service: 'MatrixCoreAPI'
            };
            CALL_API(_input).then((response) => {
                if (response) {
                    let policySubType = null
                    let policyType = PolicyTypes.find((p) => p.PolicyTypeId == response.PolicyTypeId);

                    if(response.PolicyTypeId == 0) {
                        policySubType = FreshPolicySubTypes.find((p) => p.Id == response.PolicySubTypeId)
                    }
                    if(response.PolicyTypeId == 1 || response.PolicyTypeId == 2) {
                        policySubType = RolloverPolicySubTypes.find((p) => p.Id == response.PolicySubTypeId)
                    }

                    setSME(prevState => ({...prevState, PolicyType: policyType, PolicySubType: policySubType}));

                    if(!fetchData) {
                        enqueueSnackbar("Saved Successfully", { variant: 'success', autoHideDuration: 3000, });
                    }
                }
                else {
                    if(!fetchData) {
                        enqueueSnackbar("Error occured", { variant: 'error', autoHideDuration: 3000, });
                    }
                }
            })
        }
    }

    const handleAddNew = (ControlId) => {
        // check if exceeds Maximum Quotes Limit
        if (SME.SMEQuote.length >= MaxQuotesLimit) {
            enqueueSnackbar("There are already " + MaxQuotesLimit + " quotes available. Please delete one or more quotes to proceed", { variant: 'error', autoHideDuration: 3000, });
            return false;
        }

        let obj = { MasterID: MaxMasterID + 1, PrimaryId: "", FamilyId: "", Price: "", Supplier: { OldSupplierId: 0 }, OTC: OTCQuotes[1], IsEditable: true, ControlId: ControlId, QuoteFileInput: null, QuoteFileName: "", FilePath: "", DocId: "" }
        setMaxMasterID(prev => prev + 1);
        let arr1 = SME.SMEQuote;
        let arr2 = [...arr1, obj];
        setSME({ ...SME, SMEQuote: arr2 });
    }

    const DeleteSMEQuoteData = (Quote) => {
        if (!User.UserId) {
            return null;
        }

        let reqData =
        {
            "LeadID": leadId,
            "UserId": User.UserId,
            "ControlId": 0,
            "SMEQuotesId": Quote.PrimaryId 
        }

        const _input = {
            url: `api/SalesView/DeleteSMEQuoteData`,
            method: 'POST', service: 'MatrixCoreAPI',
            requestData: reqData,
            timeout: "l"
        };
        CALL_API(_input).then((response) => {
            if (response) {
                enqueueSnackbar("Delete Successfully", { variant: 'success', autoHideDuration: 3000, });
            }
        })
    }

    const handleDelete = (Quote) => {
        if (User.UserId !== Quote.CreatedBy && !Quote.IsEditable && !(SubProductProperty == NonEB && RoleId !== 13)) {
            enqueueSnackbar("you can delete only your quotes", {
                variant: "error",
                autoHideDuration: 3000
            });
            return null;
        }
        let i = -1;
        SME.SMEQuote.forEach((item, index) => {
            if (item.MasterID === Quote.MasterID) {
                i = index;
            }
        })
        let arr1 = JSON.parse(JSON.stringify(SME.SMEQuote))
        arr1.splice(i, 1);
        setSME({ ...SME, SMEQuote: arr1 })
        DeleteSMEQuoteData(Quote);
    }

    const AddFiletoQuote = (e, MasterID) => {
        if (TotalFileCount >= MaxFileLimit) {
            enqueueSnackbar("Maximum" + MaxFileLimit + "  files can be uploaded", {
                variant: "error",
                autoHideDuration: 3000
            });
            return false;
        }

        let i = -1;
        SME.SMEQuote && Array.isArray(SME.SMEQuote) &&
            SME.SMEQuote.forEach((item, index) => {

                if (item.MasterID === MasterID) {
                    i = index;
                    
                }

            })

        let arr1 = JSON.parse(JSON.stringify(SME.SMEQuote))
        if (i >= 0 && arr1[i]) {

            arr1[i].QuoteFileInput = e.target.files[0];
        
        }
        setSME({ ...SME, SMEQuote: arr1 })

    }

    const updateSMEQuoteSaveData = (Quote, docId, filePath = "") => {
        if (!User.UserId) {
            return null;
        }

        let reqData =
        {
            "LeadID": leadId,
            "UserId": User.UserId,
            "comment": SME.SMEComment,
            "QuotesStatusId": SME.QuotesStatus.QuotesStatusId,
            "RoleId": User.RoleId,
            "productId": rootScopeService.getProductId(),
            "ControlId": Quote.ControlId,
            "SMEQuote": "<root><q><PrimaryId>" + Quote.PrimaryId + "</PrimaryId><supplierId>" + Quote.Supplier.OldSupplierId + "</supplierId><FamilyId>" + Quote.FamilyId + "</FamilyId><price>" + Quote.Price + "</price><OTC>" + Quote.OTC.ID + "</OTC><FilePath>" + filePath + "</FilePath><DocId>" + docId + "</DocId></q></root>",
            "SubStatusId": 0 
        }

        const _input = {
            url: `api/SalesView/UpdateSMEQuoteList`,
            method: 'POST', service: 'MatrixCoreAPI',
            requestData: reqData,
            timeout: "l"
        };
        CALL_API(_input).then((response) => {
            if (response) {
                enqueueSnackbar("Save Successfully", { variant: 'success', autoHideDuration: 3000, });
                getQuotes();
            }

        }).finally(() => {
            setIsQuoteFileSaving(false);
        })
    }

    const handleOnSave = (Quote, Name) => {
        setIsQuoteFileSaving(true);
        if (!subProductTypeIds.includes(subProductTypeId) && (Quote.Supplier.OldSupplierId === 0 || Quote.Price == 0) ) {
            enqueueSnackbar("Supplier and quote price must be filled in order to save this", {
                variant: "error",
                autoHideDuration: 3000
            });
            setIsQuoteFileSaving(false);

            return false;
        }
        if (subProductTypeIds.includes(subProductTypeId) && (Quote.Supplier.OldSupplierId === 0 || Quote.FamilyId === "" || Quote.Price == 0)) {//Need to check in case of empty
            enqueueSnackbar("Supplier,family type and quote price must be filled in order to save this", {
                variant: "error",
                autoHideDuration: 3000
            });
            setIsQuoteFileSaving(false);
            return false;
        }
        if (SME.SMEQuote.length + (SelectedSuppliers.length * SelectedFamilyType.length) > MaxQuotesLimit) {
            enqueueSnackbar("There are already " + MaxQuotesLimit + " quotes available. Please delete one or more quotes to proceed", { variant: 'error', autoHideDuration: 3000, });
            setIsQuoteFileSaving(false);
            return false;
        }
        let flag = 0;
        for (var i = 0; (i < SME.SMEQuote.length) && Quote.IsEditable; i++) {
            let item = SME.SMEQuote[i];
            if (item.IsEditable == false && item.Supplier.OldSupplierId == Quote.Supplier.OldSupplierId && item.FamilyId === Quote.FamilyId && item.ControlId===Quote.ControlId ) {
                flag = 1;
                break;
            }
        }
        if (flag === 1) {
            enqueueSnackbar("This insurer and Family type combination already exists", { variant: 'error', autoHideDuration: 3000, });
            setIsQuoteFileSaving(false);
            return false;
        }
        else {
            let file = ""
            if (Quote.QuoteFileInput) 
            { 
                file = Quote.QuoteFileInput; 
            }
            if (!User.UserId) {
                return null;
            }
            const data = {
                UploadedFile: file,
                LeadId: leadId,
                CustomerId: CustId,
                ProductId: rootScopeService.getProductId(),
                EnquiryId: 0,
                Type: DocumentType
            }
            if (file) {
                commonuploadFileToUrlService(data, "api/UploadFile/UploadMiscDocument").then(function (result) {
                    if (result && result.ok && User.UserId > 0)
                    {                    
                        updateSMEQuoteSaveData(Quote, result.docId);
                    }
                    else
                    {
                        setIsQuoteFileSaving(false);
                        enqueueSnackbar("File upload failed!", {
                            variant: "error",
                            autoHideDuration: 3000
                        });
                    }
                }).catch(() => {
                    setIsQuoteFileSaving(false);
                })
            }
            else {
                updateSMEQuoteSaveData(Quote, Quote.DocId ? Quote.DocId : "")
            }
        }
    }
    const HandleAdditionalFileUpload = (file, filename, RowId, ControlId) => {
        if (TotalFileCount >= MaxFileLimit) {
            enqueueSnackbar("Maximum" + MaxFileLimit + "  files can be uploaded", {
                variant: "error",
                autoHideDuration: 3000
            });
            return false;
        }

        const data = {
            UploadedFile: file,
            LeadId: leadId,
            CustomerId: CustId,
            ProductId: rootScopeService.getProductId(),
            EnquiryId: 0,
            Type: DocumentType
        }
        if (User.UserId > 0) {
            commonuploadFileToUrlService(data, "api/UploadFile/UploadMiscDocument").then(function (result) {
                if (result && result.ok && User.UserId > 0) {
                    const reqData = {
                        LeadID: leadId,
                        filePath: result.ttlDocUrl,
                        UserId: User.UserId,
                        FileName: filename,
                        roleId: User.RoleId,
                        parentRowId: RowId,
                        ControlId: ControlId,
                        IsDelete: 0,
                        RowId: 0,
                        DocumentId: result.docId
                    };
                    saveAdditionalFile(reqData).then((data) => {

                        enqueueSnackbar("Successfully Uploaded", {
                            variant: "success",
                            autoHideDuration: 3000
                        }
                        );
                        getQuotes();
                    })
                }
            })
        }
        else {
            return;
        }
    };
    const saveAdditionalFile = (reqData) => {
        const _input = {
            url: `api/SalesView/SMEQuotesAdditionalUploadV2`,
            method: 'POST', service: 'MatrixCoreAPI',
            requestData: reqData,
            timeout: "l"
        }
        return CALL_API(_input)
    };

    const GetUploadQcrUrl = () => {
        const reqData = {
            LeadId: leadId,
            CustomerId: CustId,
            RFQId: 0,
            Token: '',
            PolicyTypeId: SME.PolicyType ? SME.PolicyType.PolicyTypeId : 0,
            PolicyTypeName: SME.PolicyType ? SME.PolicyType.PolicyTypeName : '',
            PolicySubTypeId: SME.PolicySubType ? SME.PolicySubType.Id : 0,
            PolicySubTypeName: SME.PolicySubType ? SME.PolicySubType.Name : ''
        };
        const _input = {
            url: "api/SalesView/GetUploadQcrUrl",
            method: 'POST', 
            service: 'MatrixCoreAPI',
            requestData: reqData
        }
        CALL_API(_input).then((response) => {
            if (response && response.StatusCode && response.StatusCode == 1) 
            {
                setUploadQcrUrl(response.URL);
                setIsUploadQcrUrlPopupOpen(true);
            }
            else
            {
                setUploadQcrUrl(null);
                setIsUploadQcrUrlPopupOpen(false);
                enqueueSnackbar("Quotes Summary upload panel url not found", { variant: 'error', autoHideDuration: 3000, });
            }
        }).catch((e) => {
            console.log(e);
        })
    };

    
    const GetUploadRFQUrl = () => {
        const reqData = {
            LeadId: leadId,
            CustomerId: CustId,
            Token: '',
            PolicyTypeId: 0
        };
        const _input = {
            url: "api/SalesView/GetUploadRFQUrl",
            method: 'POST', 
            service: 'MatrixCoreAPI',
            requestData: reqData
        }
        CALL_API(_input).then((response) => {
            if (response && response.StatusCode && response.StatusCode == 1) 
            {
                setUploadRFQUrl(response.URL);
                setGHIRFQOpen(true);
            }
            else
            {
                setUploadRFQUrl(null);
                setGHIRFQOpen(false);
                enqueueSnackbar("URL not found", { variant: 'error', autoHideDuration: 3000, });
            }
        }).catch((e) => {
            console.log(e);
        })
    };

    const handleFileUpload = (typeid, ControlId, file) => {

        setIsfileUploading(true)
        let SMEQuotexml = "", TrackingId = "", ReasonId;

        // typeid==1 for RFQ typeId==2 for Upload Quote Summary
        if (typeid === 1) {
            if (RFQCount >= RFQMaxLimit) {
                enqueueSnackbar("You can upload maximum " + RFQMaxLimit + " RFQs", { variant: 'error', autoHideDuration: 3000, });
                setIsfileUploading(false);

                return false;
            }
            if (SME.SMEQuote.length + (SelectedSuppliers.length * SelectedFamilyType.length) > MaxQuotesLimit) {
                enqueueSnackbar("There are already " + MaxQuotesLimit + " quotes available. Please delete one or more quotes to proceed", { variant: 'error', autoHideDuration: 3000, });
                setIsfileUploading(false);

                return false;
            }
            if (TotalFileCount >= MaxFileLimit) {
                enqueueSnackbar("Maximum" + MaxFileLimit + "  files can be uploaded", {// need to change
                    variant: "error",
                    autoHideDuration: 3000
                });
                setIsfileUploading(false);

                return false;
            }
            if (!subProductTypeIds.includes(subProductTypeId) && SelectedSuppliers.length < 1 ) {
                enqueueSnackbar("Please select atleast one supplier", {
                    variant: "error",
                    autoHideDuration: 3000
                });
                setIsfileUploading(false);

                return false;
            }
            if (subProductTypeIds.includes(subProductTypeId) && (SelectedSuppliers.length < 1 || SelectedFamilyType.length < 1)) {
                enqueueSnackbar("Please select atleast one supplier and one family type", {
                    variant: "error",
                    autoHideDuration: 3000
                });
                setIsfileUploading(false);

                return false;
            }

            SMEQuotexml = CreateXMLFormat();
            TrackingId = leadId + "RFQ";
            var key = "SMEQuotes" + SMEQuotesNewList.ControlId;
            if (SMEQuotesNewList.file && SMEQuotesList[key] !== SMEQuotesNewList.file) {
                file = SMEQuotesNewList.file;
            }

            if (SME && RoleId === 13 && [2, 3, 4].indexOf(SME.QuotesStatus.QuotesStatusId) !== -1) {
                if (!SME.Reason) {
                    enqueueSnackbar("Reason Missing", {
                        variant: "error",
                        autoHideDuration: 3000
                    });
                    setIsfileUploading(false);
                    return false;
                }
                else {
                    ReasonId = SME.Reason;
                }
            }
        }

        else if (typeid === 2) {
            if (TotalFileCount >= MaxFileLimit) {
                enqueueSnackbar("Maximum" + MaxFileLimit + "  files can be uploaded", {// need to change
                    variant: "error",
                    autoHideDuration: 3000
                });
                return false;
            }
            TrackingId = leadId + "QuoteSummary" + ControlId;
        }
        const data = {
            UploadedFile: file,
            LeadId: leadId,
            CustomerId: CustId,
            ProductId: rootScopeService.getProductId(),
            EnquiryId: 0,
            Type: DocumentType
        }
        if (!User.UserId) {
            return null;
        }
        if (file) {
            commonuploadFileToUrlService(data, "api/UploadFile/UploadMiscDocument").then(function (result) {
                setIsfileUploading(false);

                if (result && result.ok && User.UserId > 0) {

                    if (result.docId) {
                        var reqData = {
                            LeadID: leadId,
                            ControlId,
                            Path: result.ttlDocUrl,
                            UserId: User.UserId,
                            typeid,
                            fileName: file.name,
                            roleId: RoleId,
                            ReasonId: ReasonId,
                            SMEQuote: SMEQuotexml,
                            DocumentId: result.docId
                        }
                        saveQuotesService(reqData).then((data) => {
                            ResetSMEQuotes();
                            enqueueSnackbar("Successfully Uploaded", {
                                variant: "success",
                                autoHideDuration: 3000
                            });
                            setIsfileUploading(false);                            
                        }, function () {
                            ResetSMEQuotes();
                            enqueueSnackbar("Upload Failed", {
                                variant: "error",
                                autoHideDuration: 3000
                            });

                        });
                    }
                } else {
                    enqueueSnackbar("Upload Failed", {
                        variant: "error",
                        autoHideDuration: 3000
                    });
                }
            })
                .catch(() => {
                    setIsfileUploading(false);
                    enqueueSnackbar("Upload Failed", {
                        variant: "error",
                        autoHideDuration: 3000
                    });
                });
        } else {
            setIsfileUploading(false);
            enqueueSnackbar("Upload a valid File Format", {
                variant: "error",
                autoHideDuration: 3000
            });
        };
    };

    const DownloadDocument = (documentId, filePath = "") => {   
        if(documentId)
        {    
            var request = {
                custId: CustId,
                docId: documentId,
                docTypeId: DocTypeId
            }
            GetDocumentPath(request).then
                ((response) => {
                    if (response && response.ttlDocUrl) {
                        window.open(response.ttlDocUrl);
                    }
                    else {
                        window.alert("Error: " + response.statusMsg);
                    }
                });
        }
        else if(filePath)
        {
            window.open(filePath);
        }
        else
        {
            window.alert("Document Not Found!");
        }
    };

    const GetDocumentPath = (request) => {
        const _input = {
            url: `api/SalesView/GetDocumentUrl`,
            method: 'POST',
            service: 'MatrixCoreAPI',
            requestData: request
        }

        return CALL_API(_input);
    };

    const updateSMEQuoteStatus = () => {
        if (!User.UserId) {
            return null;
        }
        
        if (!(SME.QuotesStatus && SME.QuotesStatus.QuotesStatusId)) {
            alert("Please select Quote Status.");
            return false;
        }

        if(RoleId !== 13 && SME.QuotesStatus.QuotesStatusId === 7){
            var filtered = SME.SMEQuote.filter(function (el) {
                return el.Price === 0 || el.Price === "";
              });
            if(filtered.length > 0){  
            alert("Please enter Quote Price.");
            return false;
            }
        }

        let QuotesSubStatusId = 0;
        if (SME.QuotesStatus.QuotesStatusId === 6) {
            if (["", undefined, null].includes(SME.SMEComment)) {
                alert("Comments are mandatory to mark Rejection Status.");
                return false;
            }
            if (!(SME.QuoteSubStatus && SME.QuoteSubStatus.ID)) {
                alert("Rejection reason is missing.");
                return false;
            }
            else {
                QuotesSubStatusId = SME.QuoteSubStatus.ID;
            }
        }

        if (SME.QuotesStatus.QuotesStatusId == QueryRaisedId) 
        {
            if (["", undefined, null].includes(SME.SMEComment)) 
            {
                alert("Comments are mandatory to mark Query Raised Status.");
                return false;
            }
            else 
            {
                QuotesSubStatusId = SME.QuoteSubStatus.ID;
            }         
        }
        
        let latestQuoteControlId = null;
        let quotePriceList = null;
        let rowId = null;
        if(Object && Object.keys(SMEQuotesList)){
            {Object.keys(SMEQuotesList).map((key, index) => {
                const innerquote = SMEQuotesList[key];
                if(innerquote.QuoteType == 1)
                {
                    latestQuoteControlId = innerquote.ControlId;
                    rowId = innerquote.RowId;
                }
                if(index == 0)
                {
                    quotePriceList = innerquote.QuotePriceList;
                }
            })}
        }

        if(Object && Object.keys(SMEQuotesList)){
            {Object.keys(SMEQuotesList).map((key, index) => {
                const innerquote = SMEQuotesList[key];
                if(innerquote.QuoteType == 2 && innerquote.ControlId == latestQuoteControlId && innerquote.parentRowId && rowId == innerquote.parentRowId)
                {
                    latestQuoteControlId = null;
                }
            })}
        }

        if(RoleId !== 13 &&
           (SME.QuotesStatus.QuotesStatusId == 2 || SME.QuotesStatus.QuotesStatusId == 4)
           && [1, 2, 3].indexOf(subProductTypeId) > -1 &&
           latestQuoteControlId && Array.isArray(quotePriceList) && quotePriceList.length > 2)
        {
            let selectedSupplier = 0;        
            quotePriceList.forEach((quotePrice) => {
                if(quotePrice.ControlId == latestQuoteControlId && quotePrice.SupplierId > 0)
                {
                    ++selectedSupplier;
                }
            });

            if(selectedSupplier > 2)
            {
                alert("Please upload Quotes Summary on latest RFQ");
                return false;
            }
        }

        var reqData = {
            LeadID: leadId,
            UserId: User.UserId,
            comment: SME.SMEComment,
            QuotesStatusId: SME.QuotesStatus.QuotesStatusId,
            RoleId: RoleId,
            productId: rootScopeService.getProductId(),
            SubStatusId: QuotesSubStatusId
        }
        if (!User.UserId) {
            return null;
        }

        // SME.SMEComment = "";
        updateSMEQuoteStatusService(reqData).then(() => {            
            ResetSMEQuotes();
            enqueueSnackbar("Successfully Updated", {
                autoHideDuration: 3000,
                variant: "success"
            });
        }).catch(() => {
            ResetSMEQuotes();
            enqueueSnackbar("Error in updating SME quote status", {
                autoHideDuration: 3000,
                variant: "error"
            });
        })
    }
    const getSuppliers = useCallback(() => {
        masterService.getSupplierByProduct(productId).then((response) => {
            let uniqueSuppliers = [], map = {}, uniqueMultipleSuppliers = [];
            uniqueMultipleSuppliers.push({ OldSupplierId: 0, SupplierDisplayName: 'Any' })
            response.forEach((supplier) => {
                let valid = (supplier.ProductId === productId && !map[supplier.OldSupplierId]);
                if(subProductTypeId && subProductTypeId > 0)
                {
                    valid = (supplier.ProductId === productId && !map[supplier.OldSupplierId] && supplier.SubProductId == subProductTypeId);
                }
                if (valid) 
                {
                    uniqueSuppliers.push(supplier);
                    uniqueMultipleSuppliers.push(supplier);
                    map[supplier.OldSupplierId] = 1;
                }
            });
            setSuppliers(uniqueSuppliers);
            setMultipleSupplier(uniqueMultipleSuppliers);
        });
    }, [productId]);
    const getSMERevisionList = () => {
        masterService.getMasterTypeList(22).then((response) => {
            setSMERevisionList(response);
        });
    }
    const getSMEQuoteSubStatus = () => {
        masterService.getMasterTypeList(23).then((response) => {
            setSMEQuoteSubStatus(response);
        });
    }
    useEffect(() => {
        if (leadId) {
            setAreChangesTemporary(false);
            getSuppliers();
            getQuotes();
            getSMEQuoteSubStatus()

            if (RoleId === 13) {
                getSMERevisionList()
            }            
        }
    }, [RoleId, getQuotes, getSuppliers, leadId])

    const handleToggle = (quote) => {
        let key = quote.ControlId;
        let value = IsToggleOpen[key];
        let toggle = value === true ? false : true
        let arr = JSON.parse(JSON.stringify(IsToggleOpen))
        arr[key] = toggle
        setIsToggleOpen(arr);

        if(IsNewQcrPanelSubproduct() && toggle)
            GetSetPolicyType(true);
    }
    const handleUploadRFQButton = () =>{
        GetUploadRFQUrl();
    }

    const closeGHIRfq =()=>{
        ResetSMEQuotes();
        setGHIRFQOpen(false);
    }
    return <>

        {loading && <LinearProgress color="secondary" />}
        <Grid container spacing={2} className="newSmeViewQuotes">
        <Grid item sm={12} md={12} xs={12}>
            <label className="label leadId"><b>Lead Id : {leadId}</b></label>
            {productId == 131 && <label className="label leadId SumAssuerdTxt"><b>Sum Assured : {(SumAssured && SumAssured > 0) ? SumAssured : "N/A"}</b></label>}
            </Grid>
            {/* fos sme agent will have matrix rfq panel for group health Insurance else will have the cj rfq panel */}
            {(RoleId === 13 || (RoleId !== 13 && leadSourceId === 6)) && (subProductTypeId != 1 || (subProductTypeId == 1 && SMEFosAgent)) &&
                <Grid item sm={6} md={6} xs={12}>
                    <label className="label">RFQ upload--</label>
                    <div className={IsDisabled ? "fileUpload disabled" : "fileUpload"}>
                        <FileDropzone
                            onDrop={handleFileDrop}
                        />
                    </div>
                </Grid>
            }
            {(RoleId === 13 || (RoleId !== 13 && leadSourceId === 6)) && (subProductTypeId == 1 && !SMEFosAgent) &&
                <Grid item sm={6} md={6} xs={12}>
                    <label className="label">RFQ upload--</label>
                    <div className={IsDisabled ? "rfqUpload disabled" : "rfqUpload"} >
                        <Button onClick={()=> handleUploadRFQButton()}> <p><strong> Upload </strong> your files here</p>       </Button>
                    <ErrorBoundary name="UploadQcrUrl">
                        <UploadQcrUrlPopup
                            open={GHIRFQOpen}
                            handleClose={closeGHIRfq}
                            url={UploadRFQUrl}
                        />
                    </ErrorBoundary>
                    </div>
                </Grid>
            }

            <Grid item sm={6} md={6} xs={12}>
                <div className="commentSection">
                    <label>Comments--</label>
                    <Textarea
                        name="comment"
                        minRows={1}
                        placeholder="Enter Comments here…"
                        value={SME.SMEComment}
                        onChange={handleChange}
                        disabled={IsDisabled}
                    />
                    {/* <button class="commentSave">Save</button> */}
                </div></Grid>


            {SMEQuotesNewList.file ?
                <>
                    <hr className="horizentalLine" />
                    <Grid item sm={12} md={12} xs={12} >
                        <label className="label">Upload--</label>
                    </Grid>
                    {IsfileUploading ?
                        <Grid item xs={5}>
                            <p>File Uploading...</p>
                            <LinearProgress title="uploading" />
                        </Grid>
                        :
                        <>
                            <Grid item sm={4} md={4} xs={4}>
                                <AttachmentIcon className="attechIcon" />
                                <p className="fileName" title={SMEQuotesNewList.file.name}>{SMEQuotesNewList.file.name}</p>
                                <button className="removeBtn" onClick={clearFileInput}><CloseIcon /></button>
                            </Grid>

                        </>
                    }
                </>
                : null
            }

            <MultipleSelectDropdown
                name="insurer"
                label="Select Insurer"
                value={SelectedSuppliers}
                options={MultipleSupplier}
                labelKeyInOptions='SupplierDisplayName'
                valueKeyInOptions='_all'
                handleChangeMultiple={handleChangeMultiple}
                renderValue={(selected) => selected.map((s) => { return s['SupplierDisplayName'] }).join(', ')}
                show={IsRFQUploaded}
                sm={3} md={3} xs={12}
            />
            <MultipleSelectDropdown
                name="FamilyType"
                label="Select family type"
                value={SelectedFamilyType}
                options={MultipleFamilyType}
                labelKeyInOptions='Name'
                valueKeyInOptions='_all'
                handleChangeMultiple={handleChangeMultiple}
                renderValue={(selected) => selected.map((s) => { return s['Name'] }).join(', ')}
                show={IsRFQUploaded && SubProductProperty !== NonEB}
                sm={3} md={3} xs={12}    
                disabled={!subProductTypeIds.includes(subProductTypeId)}            
            />
            <SelectDropdown
                name="RevisionReason"
                label="Revision Reason"
                value={SME.Reason}
                options={SMERevisionList}
                labelKeyInOptions='Name'
                valueKeyInOptions='ID'
                handleChange={handleChange}
                show={IsRFQUploaded && (RoleId === 13 && [2, 3, 4, 5, 6, 7].indexOf(SME.QuotesStatus.QuotesStatusId) !== -1)}
                sm={3} md={3} xs={12}
            />
            <Grid item sm={2} md={2} xs={12} >
                {!IsfileUploading && IsRFQUploaded && <Button className="editBtn" onClick={() => { handleFileUpload(1, SMEQuotesNewList.ControlId) }}
                    color="secondary"
                    variant="outlined">Request quote</Button>}
            </Grid>
            <Grid item sm={12} md={12} xs={12}>
                <Button
                    className="HistoryBtn"
                    size="small" color="secondary"
                    onClick={() => setIsQuoteHistoryPopupOpen(true)}                                >
                    <HistoryIcon /> Insurer History
                </Button>
            </Grid>

            <hr className="horizentalLine" />
            <Grid item sm={12} md={12} xs={12} >
                {IsAdditionalFile && <ViewAdditionalFilePopup open={IsAdditionalFile} handleClose={closePopup} HandleAdditionalFileUpload={HandleAdditionalFileUpload} AdditionalQuote={AdditionalQuote} SMEQuotesList={SMEQuotesList} RoleId={RoleId} DownloadDocument={DownloadDocument}/>}

                <label className="label">Quote Requests--</label>
                {/* {Object.keys(SMEQuotesList).map((key, index) => { */}
                {Object.keys(SMEQuotesList).filter(key => SMEQuotesList[key].QuoteType === 1).map((key, index) => {

                    const quote = SMEQuotesList[key];
                    
                    const PriceList = Array.isArray(quote.QuotePriceList) ? quote.QuotePriceList : [];

                    // const PriceList2 = Object.entries(quote.QuotePriceList)
                    console.log('quotee',quote);
                    console.log(PriceList)
                    // console.log(PriceList2)
                    // console.log(Object.entries(PriceList))
                    // console.log(PriceList.SupplierId)
                    return (
                        <Grid item sm={12} md={12} xs={12} >
                            <div className="quoteRequest">
                                <Grid container spacing={2}>
                                    {/* <Grid item sm={8} md={8} xs={12}><span className="numbering">{SMEQuotesList.map((row,i)=><h3>{row.ControlId}</h3>)}</span> */}
                                    <Grid item sm={7} md={7} xs={12}><span className="numbering">{index + 1}</span>
                                        {/* <h3>RFQ1_500 Lives 23_form.xls</h3> */}
                                        <h3>{quote.FileName}</h3>
                                        {/* <h3>{quote.QuotePriceList}</h3> */}
                                        {/* <p className="downloadFile"><a href="">Download RFQ</a></p> */}
                                        <button 
                                            className="rfqBtn" 
                                            onClick={() => { DownloadDocument(quote.DocumentId, quote.Path) }}
                                        >
                                            {(quote.IsUploadedByAgent)?'Download RFQ':'Download Incumbent Quote'}
                                        </button>
                                    </Grid>


                                    {/* {IsAdditionalFile && <ViewAdditionalFilePopup open={IsAdditionalFile} handleClose={closePopup} HandleAdditionalFileUpload={HandleAdditionalFileUpload} RowId={quote.RowId} ControlId={quote.ControlId} />} */}
                                    <Grid item sm={5} md={5} xs={12}>
                                        {/* <button onClick={() => { ViewAdditionalFile() }} className="rfqBtn">View Additional File</button> */}
                                        <h3 className="rfqBtn"><span onClick={() => { ViewAdditionalFile(quote) }} ><AddCircleOutline /> {RoleId===13 ?"Add or View Attachments": "View Attachments"}</span></h3>
                                        <span className="lastDate">Last updated on : {quote.UploadedOn === -62135616600000 ? "N/A" : dayjs(quote.UploadedOn).format('DD/MM/YYYY hh:mm a')}</span>
                                        <span className="lastDate">
                                            {
                                                quote.UploadedBy ? "Uploaded by : " + quote.UploadedBy : ""
                                            }
                                        </span>
                                    </Grid>

                                </Grid>

                                <div className="expandmoreIcon">
                                    <ExpandMore onClick={() => handleToggle(quote)} style={{ transform: IsToggleOpen[quote.ControlId] ? 'rotate(180deg)' : 'rotate(0deg)' }} /></div>
                                {IsToggleOpen[quote.ControlId] &&
                                    <div className="midContent">
                                    <Grid container spacing={2}>
                                            <Grid item sm={6} md={6} xs={12}>
                                                <Grid container>
                                                    {}
                                                    {}
                                                </Grid>
                                            </Grid>
                                            <Grid item sm={6} md={6} xs={12}>                                                
                                                {RoleId != 13 && 
                                                    <h3 className={IsDisabled ? "addnew disabled" : "addnew"}>
                                                        <span onClick={() => { handleAddNew(quote.ControlId) }}
                                                        className={IsDisabled ? "addNewDisable" : null}
                                                        >
                                                            <AddCircleOutline disabled={RoleId === 13}/> Add New
                                                        </span>
                                                    </h3>
                                                }
                                            </Grid>
                                        </Grid>
                                        {SME.SMEQuote && Array.isArray(SME.SMEQuote) && SME.SMEQuote.filter(Quote => Quote.ControlId === quote.ControlId).map((Quote, index1) => {
                                            return <>
                                                <hr className="horizentalLine" style={{marginBottom: '14px'}}/>
                                                <Grid container spacing={2} className={IsDisabled ? "disabled" : null}>
                                                    <SelectDropdown
                                                        name="insurer"
                                                        label="Select insurer type"
                                                        value={Quote.Supplier ? Quote.Supplier.OldSupplierId : ''}
                                                        options={suppliers}
                                                        labelKeyInOptions='SupplierDisplayName'
                                                        valueKeyInOptions='OldSupplierId'
                                                        handleChange={(e) => { handleChange(e, Quote, Quote.MasterID) }}
                                                        show={true}
                                                        shrink={{ shrink: true }}
                                                        disabled={((Quote.IsAnySupplierEditable && RoleId != 13) || (Quote && Quote.IsEditable && RoleId != 13)) ? false : true}
                                                        sm={2} md={2} xs={12}
                                                        InputProps={{
                                                            startAdornment: (
                                                                <InputAdornment position="start">
                                                                    <img src={CONFIG.PUBLIC_URL + "/images/salesview/insure.svg"} />
                                                                </InputAdornment>
                                                            ),
                                                        }}
                                                    />
                                                    <SelectDropdown
                                                        name="family"
                                                        label="Select family type"
                                                        value={Quote.FamilyId ? Quote.FamilyId : ''}
                                                        options={FamilyType}
                                                        labelKeyInOptions='Name'
                                                        valueKeyInOptions='Id'
                                                        handleChange={(e) => { handleChange(e, Quote, Quote.MasterID) }}
                                                        show={SubProductProperty !== NonEB}
                                                        disabled={(Quote && Quote.IsEditable && RoleId != 13 && subProductTypeIds.includes(subProductTypeId)) ? false : true}
                                                        sm={2} md={2} xs={12}
                                                        InputProps={{
                                                            startAdornment: (
                                                                <InputAdornment position="start">
                                                                    <img src={CONFIG.PUBLIC_URL + "/images/salesview/family_restroom.svg"} />
                                                                </InputAdornment>
                                                            ),
                                                        }}
                                                    />
                                                    <TextInput
                                                        name="price"
                                                        label="Enter Quote price"
                                                        value={Quote.Price === 0 ? "" : Quote.Price}
                                                        maxLength={50}
                                                        sm={2} md={2} xs={12}
                                                        handleChange={(e) => { handleChange(e, Quote, Quote.MasterID) }}
                                                        disabled={RoleId === 13}
                                                        InputProps={{
                                                            startAdornment: (
                                                                <InputAdornment position="start">
                                                                    <img src={CONFIG.PUBLIC_URL + "/images/salesview/quoteprice.svg"} />
                                                                </InputAdornment>
                                                            ),
                                                        }}
                                                    />
                                                    <Grid item sm={2} md={2} xs={12}>
                                                        <div className="uploadBtn">
                                                            {Quote.DocId == "" && Quote.FilePath == "" && <><input
                                                                //accept="image/*"
                                                                className=""
                                                                id={"Uploadfile" + Quote.MasterID}
                                                                type="file"
                                                                onChange={(e) => { AddFiletoQuote(e, Quote.MasterID) }}
                                                                disabled={RoleId === 13}
                                                            />
                                                                <label htmlFor={"Uploadfile" + Quote.MasterID}>
                                                                    <Button
                                                                        variant="contained"
                                                                        component="span"
                                                                        startIcon={<img src={CONFIG.PUBLIC_URL + "/images/salesview/attechment.svg"} />}
                                                                        className={RoleId === 13 ? "btn cursorNone" : "btn"}
                                                                        color="inherit"
                                                                        >
                                                                        {Quote.QuoteFileInput != null ? <span className="rfqBtn grayColor"> {Quote.QuoteFileInput.name}</span> : 'Upload'}
                                                                    </Button>
                                                                </label>
                                                            </>
                                                            }
                                                            {
                                                                (Quote.DocId || Quote.FilePath) && 
                                                                <button 
                                                                    className={IsDisabled ? "rfqBtn rfqBtnEnable" : "rfqBtn"}
                                                                    onClick={() => 
                                                                                { 
                                                                                    DownloadDocument(Quote.DocId, Quote.FilePath)
                                                                                }
                                                                            }
                                                                >
                                                                    Download Quote
                                                                </button>
                                                            }
                                                        </div>

                                                    </Grid>
                                                    <Grid item sm={1} md={1} xs={12}>
                                                        <div className="otcBtn">
                                                            <label>OTC?</label>
                                                            <Switch
                                                                name="otcQuote"
                                                                // value={(Quote.OTC && Quote.OTC.ID === 1) ? true : false}
                                                                checked={(Quote.OTC && Quote.OTC.ID === 1) ? true : false}
                                                                // onChange={handleChange}
                                                                onChange={(e) => { handleChange(e, Quote, Quote.MasterID) }}
                                                                color="primary"
                                                                disabled={RoleId === 13}
                                                                inputProps={{ 'aria-label': 'primary checkbox' }}
                                                            />
                                                        </div>
                                                    </Grid>
                                                    <Grid item sm={1} md={1} xs={12}>

                                                        {RoleId != 13 && <button onClick={() => { handleOnSave(Quote, leadId + Quote.ControlId + "Q" + index1) }}
                                                            className={IsDisabled ? "save disabledBg" : "save"}
                                                            disabled={IsQuoteFileSaving === true}
                                                        // disabled={IsQuoteFileSaving(true)}
                                                        >{(IsQuoteFileSaving === true) ? "Saving" : "Save"}</button>}
                                                        {/* <CheckCircleIcon className="blue mt" onClick={() => { handleOnSave(Quote, leadId + Quote.ControlId + "Q" + index1) }} /> */}
                                                        {RoleId != 13 && <DeleteIcon onClick={() => handleDelete(Quote)} className="red mt" />}</Grid>
                                                    <Grid item sm={2} md={2} xs={12}> <span className="lastDate">Last Updated  {Quote.UpdatedDate === -62135616600000 ? "N/A" : dayjs(Quote.UpdatedDate).format('DD/MM/YYYY hh:mm a')}</span>
                                                    </Grid>
                                                </Grid>
                                            </>;
                                        })}
                                        <hr className="horizentalLine"/>
                                        {IsNewQcrPanelSubproduct() &&
                                            <Grid container spacing={2} style={{marginLeft: "1px", marginTop: '15px'}}>
                                                <Grid container spacing={2} className={IsDisabled ? "disabled" : null} style={{marginBottom: '5px'}}>
                                                    <SelectDropdown
                                                        name="PolicyType"
                                                        label="Type of Case"
                                                        value={SME.PolicyType || null}
                                                        options={PolicyTypes}
                                                        labelKeyInOptions="PolicyTypeName"
                                                        valueKeyInOptions="_all"
                                                        handleChange={handleChange}
                                                        sm={2} md={2} xs={12}
                                                        disabled={RoleId === 13}
                                                    />
                                                    <SelectDropdown
                                                        name="PolicySubType"
                                                        label="Coverage Type"
                                                        value={SME.PolicySubType || null}
                                                        options={(SME.PolicyType && SME.PolicyType.PolicyTypeId === 0) ? FreshPolicySubTypes : RolloverPolicySubTypes}
                                                        labelKeyInOptions="Name"
                                                        valueKeyInOptions="_all"
                                                        handleChange={handleChange}
                                                        sm={2} md={3} xs={12}
                                                        disabled={RoleId === 13}
                                                    />
                                                    <Button 
                                                        variant="contained"
                                                        color="inherit"
                                                        onClick={() => { GetSetPolicyType(false) }}
                                                        className="rfqTypeSave"
                                                        disabled={RoleId === 13}
                                                    >
                                                        {'Save'}
                                                    </Button>
                                                </Grid>
                                                {ShowNewQcrPanel() &&
                                                    <p className="mt15" style={{ fontWeight: 'bold', textAlign: 'center', marginTop:'10px', marginBottom: '5px'}}> 
                                                        {IsDisabled
                                                            ?
                                                            <a className="addNewDisable"> Upload Quotes Summary </a>                                                   
                                                            :
                                                            <a onClick={GetUploadQcrUrl}> Upload Quotes Summary </a>
                                                        }
                                                    </p>
                                                }
                                            </Grid>
                                        }
                                        {!ShowNewQcrPanel() &&
                                            <Grid container spacing={2}>
                                                <Grid item sm={2} md={2} xs={12}> <p className="mt15">Upload Quotes Summary</p></Grid>
                                                <Grid item sm={2} md={2} xs={12}>

                                                    <div className="uploadBtn">
                                                        <input
                                                            className=""
                                                            id={"contained-button-file" + quote.ControlId}
                                                            onChange={(e) => { handleFileUpload(2, quote.ControlId, e.target.files[0]) }}
                                                            type="file"
                                                            disabled={RoleId === 13 || IsDisabled}
                                                        />
                                                        <label htmlFor={"contained-button-file" + quote.ControlId}>
                                                            <Button
                                                                variant="contained"
                                                                component="span"
                                                                startIcon={<img src={CONFIG.PUBLIC_URL + "/images/salesview/attechment.svg"} />}
                                                                className={RoleId === 13 ? "btn cursorNone" : "btn"}
                                                                color="inherit"

                                                            >
                                                                {Object.keys(SMEQuotesList).map((key, index) => {
                                                                    const innerquote = SMEQuotesList[key];

                                                                    if (innerquote.ControlId === quote.ControlId && innerquote.QuoteType === 2) {

                                                                        let QuoteSummary = innerquote.FileName

                                                                        return (
                                                                                <button 
                                                                                    className="rfqBtn" 
                                                                                    onClick={() => { DownloadDocument(innerquote.DocumentId, innerquote.Path) }}>
                                                                                        {QuoteSummary}
                                                                                </button>
                                                                            )
                                                                    }
                                                                    return null;
                                                                })}
                                                            </Button>
                                                        </label>
                                                    </div>
                                                </Grid>
                                            </Grid>
                                        }
                                    </div>
                                }
                            </div>
                        </Grid>
                    );
                })}
            </Grid>
            <div className="midContent">
                {SME.SMEQuote && Array.isArray(SME.SMEQuote) && SME.SMEQuote.filter(Quote => Quote.ControlId === 0).map((Quote, index1) => {
                    return (
                        <>
                            <hr className="horizentalLine" />
                            <Grid container spacing={2}>
                                <SelectDropdown
                                    name="insurer"
                                    label="Select insurer type"
                                    value={Quote.Supplier ? Quote.Supplier.OldSupplierId : ''}
                                    options={suppliers}
                                    labelKeyInOptions='SupplierDisplayName'
                                    valueKeyInOptions='OldSupplierId'
                                    handleChange={(e) => { handleChange(e, Quote, Quote.MasterID) }}
                                    show={true}
                                    shrink={{ shrink: true }}
                                    disabled={((Quote.IsAnySupplierEditable && RoleId != 13) || (Quote && Quote.IsEditable && RoleId != 13)) ? false : true}
                                    sm={2} md={2} xs={12}
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <img src={CONFIG.PUBLIC_URL + "/images/salesview/insure.svg"} />
                                            </InputAdornment>
                                        ),
                                    }}
                                />
                                <SelectDropdown
                                    name="family"
                                    label="Select family type"
                                    value={Quote.FamilyId ? Quote.FamilyId : ''}
                                    options={FamilyType}
                                    labelKeyInOptions='Name'
                                    valueKeyInOptions='Id'
                                    handleChange={(e) => { handleChange(e, Quote, Quote.MasterID) }}
                                    show={SubProductProperty !== NonEB}
                                    disabled={(Quote && Quote.IsEditable && RoleId != 13 && subProductTypeIds.includes(subProductTypeId)) ? false : true}
                                    sm={2} md={2} xs={12}
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <img src={CONFIG.PUBLIC_URL + "/images/salesview/family_restroom.svg"} />
                                            </InputAdornment>
                                        ),
                                    }}
                                />
                                <TextInput
                                    name="price"
                                    label="Enter Quote price"
                                    value={Quote.Price === 0 ? "" : Quote.Price}
                                    maxLength={50}
                                    sm={2} md={2} xs={12}
                                    handleChange={(e) => { handleChange(e, Quote, Quote.MasterID) }}
                                    disabled={RoleId === 13}
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <img src={CONFIG.PUBLIC_URL + "/images/salesview/quoteprice.svg"} />
                                            </InputAdornment>
                                        ),
                                    }}
                                />
                                <Grid item sm={2} md={2} xs={12}>
                                    <div className="uploadBtn">
                                        {Quote.FilePath == "" && Quote.DocId == "" && <><input
                                            //accept="image/*"
                                            className=""
                                            id={"Uploadfile" + Quote.MasterID}
                                            type="file"
                                            onChange={(e) => { AddFiletoQuote(e, Quote.MasterID) }}
                                            disabled={RoleId === 13}
                                        />
                                            <label htmlFor={"Uploadfile" + Quote.MasterID}>
                                                <Button
                                                    variant="contained"
                                                    component="span"
                                                    startIcon={<img src={CONFIG.PUBLIC_URL + "/images/salesview/attechment.svg"} />}
                                                    className={RoleId === 13 ? "btn cursorNone" : "btn"}>
                                                    {Quote.QuoteFileInput != null ? <span className="rfqBtn grayColor"> {Quote.QuoteFileInput.name}</span> : 'Upload'}
                                                </Button>
                                            </label>
                                        </>
                                        }
                                        {
                                            (Quote.DocId || Quote.FilePath) && 
                                            <button 
                                                className="rfqBtn" 
                                                onClick={() => 
                                                            { 
                                                                DownloadDocument(Quote.DocId, Quote.FilePath)
                                                            }
                                                        }
                                            >
                                                Download Quote
                                            </button>
                                        }
                                    </div>

                                    </Grid>
                                    <Grid item sm={1} md={1} xs={12}>
                                        <div className="otcBtn">
                                            <label>OTC?</label>
                                            <Switch
                                                name="otcQuote"
                                                checked={(Quote.OTC && Quote.OTC.ID === 1) ? true : false}
                                                onChange={(e) => { handleChange(e, Quote, Quote.MasterID) }}
                                                color="primary"
                                                disabled={RoleId === 13}
                                                inputProps={{ 'aria-label': 'primary checkbox' }}
                                            />
                                        </div>
                                    </Grid>
                                    <Grid item sm={1} md={1} xs={12}>

                                        {RoleId != 13 && <button onClick={() => { handleOnSave(Quote, leadId + Quote.ControlId + "Q" + index1) }}
                                            className="save"
                                            disabled={IsQuoteFileSaving === true}
                                        >{(IsQuoteFileSaving === true) ? "Saving" : "Save"}</button>}
                                        {RoleId != 13 && <DeleteIcon onClick={() => handleDelete(Quote)} className="red mt" />}</Grid>
                                    <Grid item sm={2} md={2} xs={12}> <span className="lastDate">Last Updated  {Quote.UpdatedDate === -62135616600000 ? "N/A" : dayjs(Quote.UpdatedDate).format('DD/MM/YYYY hh:mm a')}</span>
                                    </Grid>                              
                                </Grid>                          
                        </>
                    )
                })}
            </div>
            {
                (RFQCount > 0) &&
                allowAction &&

                <>
                    <div className="bottomSection">
                        <Grid container spacing={1}>
                            <Grid item sm={2} md={2} xs={12}>
                                <label className="status">Status/ Response</label>
                            </Grid>
                            <SelectDropdown
                                name="status"
                                label="Select Status"
                                value={QuotesStatusMaster.find((_status) => _status.QuotesStatusId == SME.QuotesStatus.QuotesStatusId) || SME.QuotesStatus}
                                options={QuotesStatusMaster.filter((_status) => {
                                    if (_status.RoleId === RoleId || _status.QuotesStatusId === QuotesStatusId) {
                                        return true;
                                    }
                                    if (RoleId !== 13 && _status.RoleId === 0) {
                                        return true;
                                    }
                                    return false
                                })}
                                labelKeyInOptions='Name'
                                valueKeyInOptions='_all'
                                handleChange={handleChange}
                                show={true}
                                disabled={RoleId === 13 || (RoleId !== 13 && SME.QuotesStatus.QuotesStatusId === 7) || IsDisabled}
                                sm={3} md={3} xs={12}
                            />
                            <SelectDropdown
                                name="subStatus"
                                label="Select SubStatus"
                                value={SMEQuoteSubStatus.find((_substatus) => _substatus.ID == SME.QuoteSubStatus.ID)}
                                options={SMEQuoteSubStatus}
                                labelKeyInOptions='Name'
                                valueKeyInOptions='_all'
                                handleChange={handleChange}
                                show={SME.QuotesStatus.QuotesStatusId === 6}
                                disabled={RoleId === 13 || IsDisabled}
                                sm={4} md={4} xs={12}
                            />

                            <Grid item sm={2} md={2} xs={12}>
                                <button
                                    className={IsDisabled ? "submitBtn disabledBg" : "submitBtn"}
                                    onClick={updateSMEQuoteStatus}
                                    title={areChangesTemporary ? "You have unsaved changes, Please submit to update" : null}
                                    disabled={IsDisabled}
                                >
                                    Submit {areChangesTemporary ? '*' : null}
                                </button>
                            </Grid>
                        </Grid>
                    </div>
                </>
            }
        </Grid>

        <ErrorBoundary name="SmeQuoteHistoryPopup">
            <SmeQuoteHistoryPopup
                open={isQuoteHistoryPopupOpen}
                handleClose={() => { setIsQuoteHistoryPopupOpen(false) }}
                leadId={leadId}
            />
        </ErrorBoundary>
        <ErrorBoundary name="UploadQcrUrl">
            <UploadQcrUrlPopup
                open={IsUploadQcrUrlPopupOpen}
                handleClose={() => { setIsUploadQcrUrlPopupOpen(false) }}
                url={UploadQcrUrl}
            />
        </ErrorBoundary>
    </>;
}