import React, { useEffect, useState } from "react";
import forEach from "lodash/forEach";
import filter from "lodash/filter";
import uniqBy from "lodash/uniqBy";
import SelectDropdown from "../../../../components/SelectDropdown";
import { CALL_API } from "../../../../services/api.service";
import { useSelector } from "react-redux";
import rootScopeService from "../../../../services/rootScopeService";
import dayjs from "dayjs";
import { ViewInteraction } from "../../RightBlock/Modals/ViewInteraction";
import { SV_CONFIG } from "../../../../appconfig";
import { CommonModalPopUp } from "../../Main/Modals/CommonModalPopUp";
import User from "../../../../services/user.service";

export default function CommunicationLogs(props) {
  let [classNames, setclassNames] = useState("textdisplay");
  let [commLogs, setCommLogs] = useState([]);
  let [rowData, setRowData] = useState([]);
  let [OpenViewInteraction, setOpenViewInteraction] = useState(false);
  let [ViewInteractionData, setViewInteractionData] = useState([]);


  let [actionByOptions, setActionByOptions] = useState([
    { label: "Action By", value: "0" },
  ]);
  let [commByOptions, setCommByOptions] = useState([
    { label: "Comm By", value: "0" },
  ]);
  let [leadOptions, setLeadOptions] = useState([{ label: "Lead ID", value: "0" }]);
  let [selectedLead, setSelectedLead] = useState("0");
  let [selectedAction, setSelectedAction] = useState("0");
  let [selectedCommBy, setSelectedCommBy] = useState("0");
  let [ParentLeadId] = useSelector((state) => {
    //console.log(state.salesview);
    let { parentLeadId } = state.salesview;
    return [parentLeadId];
  });
  let [ChatUrl, setChatUrl] = useState("");
  let [OpenCommonModalPopUp, setOpenCommonModalPopUp] = useState(false);

  const ProductId = rootScopeService.getProductId();
  //const CustomerId = rootScopeService.getCustomerId();

  //Call functions to be called on component load
  useEffect(() => {
    getCommunicationLogs();
  }, []);

  useEffect(() => {
    if (commLogs.length > 0) {
      formattedCommLogsData({
        leadId: "0",
        actionBy: "0",
        commBy: "0",
      });
    }
  }, [commLogs]);

  //Get log data from the API
  const getCommunicationLogs = () => {
    if (!ParentLeadId) 
      return; // hit api only after LeadIds is fetched from redux store

    let endpoint = 'api/MRSCore/GetCommLogs';
    // if((window.SV_CONFIG_UNCACHED["GetCommLogsCoreApiTest"] &&  window.SV_CONFIG_UNCACHED["GetCommLogsCoreApiTest"] == 1) || (SV_CONFIG["GetCommLogsCoreApiTest"] && SV_CONFIG["GetCommLogsCoreApiTest"] == 1)){
    //   endpoint = 'coremrs/api/MRSCore/GetCommLogsTest'
    // }
    // else if(SV_CONFIG["GetCommLogsCoreApi"] && SV_CONFIG["GetCommLogsCoreApi"] == 1)
    // {
    //   endpoint = 'api/MRSCore/GetCommLogs';
    // }
    
    let input = {
      url: endpoint,
      method: 'POST', service: 'MatrixCoreAPI',
      requestData: { ProductId, LeadId: ParentLeadId }
    }
    CALL_API(input).then((result) => {
      if (result && result.GetCommLogsResult && result.GetCommLogsResult.Data) {
        setCommLogs(result.GetCommLogsResult.Data);
        getFilterOptions(result.GetCommLogsResult.Data);
      }
    });
  };


  //Get log data from the API
  const getViewInteractionChat = (InteractionId, LeadId) => {
    let NewGetInteractionPrd = Array.isArray(SV_CONFIG.NewGetInteractionPrd)? SV_CONFIG.NewGetInteractionPrd : [3, 101];
    let GetInteractionOld = SV_CONFIG.GetInteractionOld;
    if (!GetInteractionOld || NewGetInteractionPrd.includes(ProductId)) {
      let url = SV_CONFIG["agentchat"][SV_CONFIG["environment"]] + "?inc=" + btoa(JSON.stringify({ "leadId": LeadId, "roomId": InteractionId, ProductId, CustomerId:rootScopeService.getCustomerId() }));
      setChatUrl(url);
      setOpenCommonModalPopUp(true);
    }
    else {
      const input = {
        url: "Chat/GetInteraction",
        method: "POST",
        service: "core",
        requestData: { InteractionId: InteractionId },
      };
      CALL_API(input).then((response) => {
        if (response && response.GetInteractionResult) {
          setViewInteractionData(response.GetInteractionResult);
          setOpenViewInteraction(true);
        }
      });
    }
  };
  //Format log comment on basis of CommType
  const getLogComment = (log) => {
    let comment;
    switch (log.CommType) {
      case 1:
        comment =
          log.CommTypeValue +
          " " +
          log.ConversationTypeValue +
          " - " +
          log.TriggerName;
        break;
      case 2:
        comment =
          log.CommTypeValue +
          " " +
          log.ConversationTypeValue +
          " - " +
          log.TriggerName;
        break;
      case 3:
        comment = "";
        if (Array.isArray(log.DispositionDetails)) {
          log.DispositionDetails.forEach(function (v, k) {
            comment = comment + v.StatusName + " at " + dayjs(v.CreatedOn).format("DD/MM/YYYY hh:mm:ss") + " | ";
          });
        }
        if (log.CallDuration > 0) {
          var minutes = log.CallDuration / 60;
          comment =
            comment +
            "Total call duration: " +
            minutes.toFixed(2) +
            " minutes (" +
            log.CallDuration +
            " seconds)";
          if (log.Context === "ASWATINDIA") {
            comment = comment + " | Call dialed by ASWAT."
          }
          if (log.CallingNo != undefined && log.CallingNo > 0) {
            comment = comment + " | CallingNo - " + log.CallingNo + "."
          }
        }
        if(log.Status == "98"){
          comment += " | Disconnected by Agent."
        }else if(log.Status == "99"){
          comment += " | Disconnected by Customer."
        }
        break;
      case 6:
        comment = "";
        if (Array.isArray(log.DispositionDetails)) {
          log.DispositionDetails.forEach(function (v, k) {
            comment = comment + v.StatusName + " at " + dayjs(v.CreatedOn).format("DD/MM/YYYY hh:mm:ss") + " | ";
          });
        }
        if (log.CallDuration > 0) {
          var minutes = log.CallDuration / 60;
          comment =
            comment +
            "Total call duration: " +
            minutes.toFixed(2) +
            " minutes (" +
            log.CallDuration +
            " seconds)";
        }
        comment = comment.replace(
          "Outbound Call INITIATED",
          "Inbound Call INITIATED"
        );
        if(log.CallDisconnected == "COMPLETEAGENT"){
          comment += " | Disconnected by Agent."
        }else if(log.CallDisconnected == "COMPLETECALLER"){
          comment += " | Disconnected by Customer."
        }
        break;
      case 4:
        comment = <a onClick={() => { getViewInteractionChat(log.InteractionId, log.LeadID) }} style={{ cursor: "pointer" }}>View Interaction</a>;
        break;
      case 7:
        comment = "";
        if (Array.isArray(log.DispositionDetails)) {
          log.DispositionDetails.forEach(function (v, k) {
            comment = comment + v.StatusName + " at " + dayjs(v.CreatedOn).format("DD/MM/YYYY hh:mm:ss") + " | ";
          });
        }
        if (log.CallDuration > 0) {
          var minutes = log.CallDuration / 60;
          comment =
            comment +
            "Total call duration: " +
            minutes.toFixed(2) +
            " minutes (" +
            log.CallDuration +
            " seconds)";
        }
        comment = comment.replace(
          "Outbound Call INITIATED",
          "C2C Call INITIATED"
        );
        if(log.CallDisconnected == "COMPLETEAGENT"){
          comment += " | Disconnected by Agent."
        }else if(log.CallDisconnected == "COMPLETECALLER"){
          comment += " | Disconnected by Customer."
        }
        break;
      case 8:
        log.CommTypeValue = "WhatsApp";
        comment =
          log.CommTypeValue +
          " " +
          log.ConversationTypeValue +
          " - " +
          log.TriggerName;
        break;
      case 9:
        comment = "";
        if (Array.isArray(log.DispositionDetails)) {
          log.DispositionDetails.forEach(function (v, k) {
            comment = comment + v.StatusName + " at " + dayjs(v.CreatedOn).format("DD/MM/YYYY hh:mm:ss") + " | ";
          });
        }
        if (log.CallDuration > 0) {
          var minutes = log.CallDuration / 60;
          comment =
            comment +
            "Total call duration: " +
            minutes.toFixed(2) +
            " minutes (" +
            log.CallDuration +
            " seconds).";
        }
        comment = comment.replace(
          "Outbound Call INITIATED",
          "VIDEOMEET INITIATED"
        );
        break;
      case 10:
          comment = "";
          if (Array.isArray(log.DispositionDetails)) {
            log.DispositionDetails.forEach(function (v, k) {
              comment = comment + v.StatusName + " at " + dayjs(v.CreatedOn).format("DD/MM/YYYY hh:mm:ss") + " | ";
            });
          }
          if (log.CallDuration > 0) {
            var minutes = log.CallDuration / 60;
            comment =
              comment +
              "Total call duration: " +
              minutes.toFixed(2) +
              " minutes (" +
              log.CallDuration +
              " seconds).";
          }
          comment = comment.replace(
            "Outbound Call INITIATED",
            "SCREENSHARE INITIATED"
          );
          break;
      case 21:
        comment = "";
        if (Array.isArray(log.DispositionDetails)) {
          log.DispositionDetails.forEach(function (v, k) {
            comment = comment + v.StatusName + " at " + dayjs(v.CreatedOn).format("DD/MM/YYYY hh:mm:ss") + " | ";
          });
        }
        if (log.CallDuration > 0) {
          var minutes = log.CallDuration / 60;
          comment =
            comment +
            "Total call duration: " +
            minutes.toFixed(2) +
            " minutes (" +
            log.CallDuration +
            " seconds).";
        }comment = comment.replace(
          "Outbound Call INITIATED",
          "Transfer Call INITIATED"
        );
        break;
        case 22:
        comment = "Appointment Recording Call Initiated at " + dayjs(log.CommunicationDate).format("DD/MM/YYYY hh:mm:ss");
        if (log.CallDuration > 0) {
          var minutes = log.CallDuration / 60;
          comment =
            comment +
            ", Total Duration: " +
            minutes.toFixed(2) +
            " minutes (" +
            log.CallDuration +
            " seconds).";
        }
        break;
      default:
        comment = "";
        break;
    }
    if (log && (log.Context === "matrixgo" || log.Context === "virtualNumberCalling")) {
        if (comment) {
            comment +=  log.Context + " | ";
        } else {
            comment = log.Context;
        }
    }
    return comment;
  };

  // Set list of values for the filters
  const getFilterOptions = (listData) => {
    setclassNames("text");
    let lOption = [{ label: "All", value: "0" }];
    let AOption = [{ label: "All", value: "0" }];
    let COption = [{ label: "All", value: "0" }];

    forEach(listData, (log, key) => {
      lOption.push({ label: log.LeadID, value: log.LeadID });
      AOption.push({ label: log.AgentName, value: log.AgentName });
      COption.push({ label: log.CommTypeValue, value: log.CommTypeValue });
    });

    //LeadId dropdown values
    setLeadOptions(
      uniqBy(lOption, "value").filter((log, key) => {
        return log.value != "";
      })
    );

    //ActionBy dropdown values
    setActionByOptions(
      uniqBy(AOption, "value").filter((log, key) => {
        return log.value != "";
      })
    );

    //ActionBy dropdown values
    setCommByOptions(
      uniqBy(COption, "value").filter((log, key) => {
        return log.value != "";
      })
    );
  }

  // Row wise Log data
  const formattedCommLogsData = (params = {}) => {
    let listingData = getFilteredList(commLogs, params);
    setRowData(
      listingData.map((logData, index) => {
        return (
          <div key={index} className="row">
            <span className="line"></span>
            <span className="dot"></span>
            {logData.EncrptyCallID && logData.CommType !== 10 && (
              <a
                href={`http://ai.policybazaar.com/transcript/matrix/transcript/v2/${logData.EncrptyCallID}/?matrix=1&agentId=${logData.EncrptAgentID}&calltrackingId=${logData.EncryptCallDataID}`}
                className="play-recording"
                target="_blank"
              >
                <i></i>Play Recording
              </a>
            )}
            <p className="row-time">
              {dayjs(logData.CommunicationDate).format("DD/MM/YYYY h:mm:ss A")}
            </p>
            <p className="row-cust-name">
              {logData.AgentId} ({logData.AgentName})
            </p>
            <div className="mid-row">
              <div className="communication-block">
                <p className="label">Lead ID</p>
                <p className="data">{logData.LeadID}</p>
              </div>
              <div className="communication-block">
                <p className="label">Communication By</p>
                <p className="data">{logData.CommTypeValue}</p>
              </div>
              <div className="communication-block">
                <p className="label">Calling Number</p>
                <p className="data">{!logData.MaskMobileNo ? "" : logData.MaskMobileNo}</p>
              </div>
              <div className="communication-block" style={{ width: '100%' }}>
                <p className="label">Comment</p>
                <p className="data">{getLogComment(logData)}</p>
              </div>
            </div>
          </div>
        );
      })
    );
  };

  //Get list after filters selection 
  const getFilteredList = (listingData, params) => {

    const logData = listingData.filter((item) => ( ![3,6].includes(item.CommType) || item.Context !== "lifepreissuanceverification" || !SV_CONFIG || !Array.isArray(SV_CONFIG["TermVerifCallLogsEmpId"]) || SV_CONFIG["TermVerifCallLogsEmpId"].includes(User.EmployeeId)))

    let filterBy = {};
    if (
      params.leadId != "0" ||
      params.actionBy != "0" ||
      params.commBy != "0"
    ) {
      if (params.leadId != "0") {
        filterBy.LeadID = parseInt(params.leadId);
      }
      if (params.actionBy != "0") {
        filterBy.AgentName = params.actionBy;
      }
      if (params.commBy != "0") {
        filterBy.CommTypeValue = params.commBy;
      }
    }
    return filter(logData, filterBy);
  };

  //Dropdown On change event function
  const selectLeadOption = (event) => {
    setSelectedLead(event.target.value);
    formattedCommLogsData({
      leadId: event.target.value,
      actionBy: selectedAction,
      commBy: selectedCommBy,
    });
  };

  //Dropdown On change event function
  const selectActionType = (event) => {
    setSelectedAction(event.target.value);
    formattedCommLogsData({
      leadId: selectedLead,
      actionBy: event.target.value,
      commBy: selectedCommBy,
    });
  };

  //Dropdown On change event function
  const selectCommBy = (event) => {
    setSelectedCommBy(event.target.value);
    formattedCommLogsData({
      leadId: selectedLead,
      actionBy: selectedAction,
      commBy: event.target.value,
    });
  };

  return (
    <>
      <div className="activity-feed">
        <div className="filter-bar">
          <div className="filter-text">Filter by</div>
          <div className={classNames}>Lead ID</div>
          <div className="field">
            <SelectDropdown
              name="leadId"
              label="Lead ID"
              handleChange={selectLeadOption}
              options={leadOptions}
              labelKeyInOptions="label"
              valueKeyInOptions="value"
              value={selectedLead}
              xs={12}
              className="filter-menu"
            />
          </div>
          <div className={classNames}>Action By</div>
          <div className="field">
            <SelectDropdown
              name="actionBy"
              label="Action By"
              handleChange={selectActionType}
              options={actionByOptions}
              labelKeyInOptions="label"
              valueKeyInOptions="value"
              value={selectedAction}
              xs={12}
              className="filter-menu"
            />
          </div>
          <div className={classNames}>Communication By</div>
          <div className="field">
            <SelectDropdown
              name="communicationBy"
              label="Communication By"
              handleChange={selectCommBy}
              options={commByOptions}
              labelKeyInOptions="label"
              valueKeyInOptions="value"
              value={selectedCommBy}
              xs={12}
              className="filter-menu"
            />
          </div>
        </div>
        <div className="blocks-data">
          {/* Row started  */}

          {rowData}

          {/* Row started  */}
        </div>
      </div>
      <ViewInteraction open={OpenViewInteraction} handleClose={() => { setOpenViewInteraction(false) }} ViewInteractionData={ViewInteractionData} />

      <CommonModalPopUp
        open={OpenCommonModalPopUp}
        URL={ChatUrl}
        className='AppointmentHistory'
        handleClose={() => {
          setOpenCommonModalPopUp(false);
        }}
      />
    </>
  );
}