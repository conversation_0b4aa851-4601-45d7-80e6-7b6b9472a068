import React, { useState, useEffect } from "react";
import {
  Box,
  TextField,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormLabel,
  Button,
  MenuItem,
  Select,
} from "@mui/material";
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from "dayjs";
import ModalPopup from "./ModalPopup";
import CloseIcon from "@mui/icons-material/Close";
import { CONFIG } from "../../appconfig";
import { useSnackbar } from "notistack";
import { CALL_API } from "../../services";
import masterService from "../../services/masterService";
import User from "../../services/user.service";

const MotorChecklistPopup = ({ open, handleClose, leadId, productId }) => {
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    productId: 117, // Added ProductId field with default value 117
    vehicleReg: "",
    vehicletype: "",
    fueltype: "",
    claimLastYear: "",
    lastYearNCB: "",
    ownershipTransfer: "",
    isCNG: "",
    cngInstallation: "",
    lastYearIDV: "",
    lastYearIDVValue: "",
    lastInsurer: "",
    callDisposition: "",
    commentBox: "",
    UpdatedOn: "",
    lastInsurerId: 0,
    expiryDate: "",
  });
  const [insurerList, setInsurerList] = useState([]);
  const [isAdmin, setIsAdmin] = useState(false);

  // Fetch motor checklist data when popup opens
  useEffect(() => {
    if (open && leadId && productId) {
      fetchMotorChecklistData();
      fetchInsurerList(productId);
      setIsAdmin(User && User.RoleId === 2); 
    }
  }, [open, leadId, productId]);

  const fetchMotorChecklistData = async () => {
    if (!leadId) {
      enqueueSnackbar("Lead ID is missing", { variant: 'error', autoHideDuration: 3000 });
      return;
    }

    setLoading(true);
    try {
      const input = {
        url: `coremrs/api/MRSCore/GetMotorChecklist/${leadId}`,
        method: 'GET',
        service: 'MatrixCoreAPI',
        timeout: 's'
      };

      const response = await CALL_API(input);
      
      if (response) {
        // Map API response to form data
        const apiData = response;
        let idvValue = "";
        let idvType = "";

        if (apiData.LastYearIDV) {
          if (apiData.LastYearIDV === "notSure") {
            idvType = "notSure";
            idvValue = "";
          } else {
            idvType = "known";
            idvValue = apiData.LastYearIDV;
          }
        }

        setFormData({
          productId: apiData.ProductId || 117,
          vehicleReg: apiData.VehicleRegNo || "",
          vehicletype: apiData.Vehicletype || "",
          fueltype: apiData.Fueltype || "",
          claimLastYear: apiData.ClaimLastYear === 1 ? "yes" : apiData.ClaimLastYear === 0 ? "no" : "",
          lastYearNCB: apiData.LastYearNCB || "",
          ownershipTransfer: apiData.OwnershipTransfer === 1 ? "yes" : apiData.OwnershipTransfer === 0 ? "no" : "",
          isCNG: apiData.IsCNG === 1 ? "yes" : apiData.IsCNG === 0 ? "no" : "",
          cngInstallation: apiData.cngInstallation || "",
          lastYearIDV: idvType,
          lastYearIDVValue: idvValue,
          lastInsurer: apiData.LastInsurer || "",
          lastInsurerId: apiData.LastInsurerId || 0,
          commentBox: apiData.CommentBox || "",
          UpdatedOn: apiData.UpdatedOn || "",
          expiryDate: apiData.ExpiryDate && apiData.ExpiryDate !== "0001-01-01T00:00:00" 
            ? apiData.ExpiryDate.split('T')[0] 
            : "",
        });
        
        enqueueSnackbar("Motor checklist data loaded successfully", { variant: 'success', autoHideDuration: 3000 });
      } else {
        enqueueSnackbar("No data found for this lead", { variant: 'info', autoHideDuration: 3000 });
      }
    } catch (error) {
      console.error("Error fetching motor checklist data:", error);
      enqueueSnackbar("Failed to load motor checklist data", { variant: 'error', autoHideDuration: 3000 });
    } finally {
      setLoading(false);
    }
  };

  const fetchInsurerList = async (productId) => {
    try {
      masterService.getSuppliers().then(res => {
        const filteredSuppliers = res.filter(sup => sup.ProductId === productId);
        setInsurerList(filteredSuppliers);
    });
    } catch (error) {
      console.error("Error fetching insurer list:", error);
    }
  };

  const handleChange = (field, value) => {
    if (field === "lastInsurer") {
      const selectedInsurer = insurerList.find(insurer => insurer.SupplierDisplayName === value);
      setFormData({
        ...formData,
        [field]: value,
        lastInsurerId: selectedInsurer ? selectedInsurer.OldSupplierId : 0
      });
    } else if (field === "lastYearIDV") {
      setFormData({
        ...formData,
        [field]: value,
        lastYearIDVValue: value === "notSure" ? "" : formData.lastYearIDVValue
      });
    } else if (field === "lastYearIDVValue") {
      setFormData({
        ...formData,
        [field]: value,
        lastYearIDV: "known"
      });
    } else if (field === "expiryDate") {
      // Handle date change
      const formattedDate = value ? dayjs(value).format('YYYY-MM-DD') : "";
      setFormData({
        ...formData,
        [field]: formattedDate,
      });
    } else {
      setFormData({
        ...formData,
        [field]: value,
      });
    }
  };

  const validateVehicleReg = (regNo) => {
    const regNoRegex = /^([a-zA-Z0-9]{6,11})$/;
    if (!regNo) {
      return "Please enter Registration Number.";
    }
    if (regNo.trim().length < 6 || regNo.trim().length > 11) {
      return "Registration number should be 6-11 alphanumeric characters.";
    }
    if (!regNoRegex.test(regNo.trim())) {
      return "Registration number should be alphanumeric (e.g., HR26AB1234 or DL01CD5678).";
    }
    return "";
  };

  const handleSubmit = () => {
    // Prevent submission for admin users
    if (isAdmin) {
      enqueueSnackbar("Admin users can only view the form, not edit it.", { variant: 'info', autoHideDuration: 3000 });
      return;
    }

    const vehicleRegError = validateVehicleReg(formData.vehicleReg);
    if (vehicleRegError) {
      enqueueSnackbar(vehicleRegError, { variant: 'error', autoHideDuration: 3000 });
      return;
    }
    if (!formData.vehicletype) {
      enqueueSnackbar("Please select vehicle type.", { variant: 'error', autoHideDuration: 3000 });
      return;
    }
    if (!formData.fueltype) {
      enqueueSnackbar("Please select fueltype type.", { variant: 'error', autoHideDuration: 3000 });
      return;
    }
    if (!formData.claimLastYear) {
      enqueueSnackbar("Please select if claim was filed last year.", { variant: 'error', autoHideDuration: 3000 });
      return;
    }
    if (!formData.lastYearNCB) {
      enqueueSnackbar("Please select last year's NCB.", { variant: 'error', autoHideDuration: 3000 });
      return;
    }
    if (!formData.ownershipTransfer) {
      enqueueSnackbar("Please select ownership transfer status.", { variant: 'error', autoHideDuration: 3000 });
      return;
    }
    if (!formData.expiryDate) {
      enqueueSnackbar("Please select expiry date.", { variant: 'error', autoHideDuration: 3000 });
      return;
    }

    // Call the save function instead of just logging
    saveMotorChecklist();
  };

  const saveMotorChecklist = async () => {
    if (!leadId || !productId) {
      enqueueSnackbar("Lead ID or Product ID is missing", { variant: 'error', autoHideDuration: 3000 });
      return;
    }

    setLoading(true);
      const selectedInsurer = insurerList.find(insurer => insurer.SupplierDisplayName === formData.lastInsurer);

      const requestData = {
        LeadId: leadId,
        VehicleRegNo: formData.vehicleReg,
        Vehicletype: formData.vehicletype,
        Fueltype: formData.fueltype,
        ClaimLastYear: formData.claimLastYear === "yes" ? 1 : 0,
        LastYearNCB: formData.lastYearNCB ? formData.lastYearNCB : null,
        OwnershipTransfer: formData.ownershipTransfer === "yes" ? 1 : 0,
        IsCNG: formData.isCNG === "yes" ? 1 : 0,
        LastYearIDV: formData.lastYearIDV === "known" ? formData.lastYearIDVValue : "notSure",
        LastInsurer: selectedInsurer ? selectedInsurer.SupplierDisplayName : formData.lastInsurer,
        LastInsurerId: selectedInsurer ? selectedInsurer.OldSupplierId : 0,
        CommentBox: formData.commentBox,
        ExpiryDate: formData.expiryDate || "",
      };

      const input = {
        url: "coremrs/api/MRSCore/SaveAndUpdateMotorChecklist",
        method: 'POST',
        service: 'MatrixCoreAPI',
        requestData: requestData,
        timeout: 's'
      };

      const response = await CALL_API(input);
      
      if (response && response.status) {
        enqueueSnackbar(response.message, { variant: 'success', autoHideDuration: 3000 });
        // Refresh data after successful save
        await fetchMotorChecklistData();
      } else {
        enqueueSnackbar(response?.message || "Failed to save motor checklist", { variant: 'error', autoHideDuration: 3000 });
      }
  
  };

  return (
    <ModalPopup
      className="motorChecklistPopup"
      open={open}
      handleClose={handleClose}
    >
      <div className="motor-header">
        <div className="motor-header-left">
          <img alt="" src={CONFIG.PUBLIC_URL + "/images/salesview/checkList.svg"} />
          <span className="motor-title">
            Motor Checklist Form
          </span>
          {formData.UpdatedOn && (
            <span className="motor-updated">
              Updated on {dayjs(formData.UpdatedOn).format('DD/MM/YYYY')}
            </span>
          )}
          {isAdmin && <span style={{ color: '#FF5630', fontSize: '14px', marginLeft: '-11px', fontWeight:'bold' }}>(View Only)</span>}
          {loading && <span className="loading-text">Loading...</span>}
        </div>
        <button className="motor-close-btn" onClick={handleClose}>
          <CloseIcon />
        </button>
      </div>
      <Box className="popupWrapper">
        <form className="checklist-form" onSubmit={(e) => e.preventDefault()}>
          <FormControl fullWidth className="form-control">
            <FormLabel component="legend">
              Vehicle Registration Number <span className="required">*</span>
            </FormLabel>
            <TextField
              id="vehicleReg"
              fullWidth
              variant="outlined"
              value={formData.vehicleReg}
              onChange={(e) => handleChange("vehicleReg", e.target.value)}
              disabled={loading || isAdmin}
            />
          </FormControl>
          
          <FormControl fullWidth className="form-control">
            <FormLabel component="legend">
              Please select Expiry Date? <span className="required">*</span>
            </FormLabel>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DatePicker
                value={formData.expiryDate ? dayjs(formData.expiryDate) : null}
                onChange={(newValue) => {
                  handleChange("expiryDate", newValue ? newValue.format('YYYY-MM-DD') : "");
                }}
                disabled={loading || isAdmin}
                format="DD/MM/YYYY"
                minDate={dayjs().subtract(180, 'day')}
                maxDate={dayjs().add(90, 'day')}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    variant="outlined"
                    helperText="Select date between 180 days ago and 90 days from today"
                    error={formData.expiryDate !== ""}
                  />
                )}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    variant: "outlined",
                  },
                }}
              />
            </LocalizationProvider>
          </FormControl>

          <FormControl className="form-control">
            <FormLabel component="legend">
              Please select the Vehicle type? <span className="required">*</span>
            </FormLabel>
            <RadioGroup
              row
              value={formData.vehicletype}
              onChange={(e) => handleChange("vehicletype", e.target.value)}
            >
              <FormControlLabel value="Private" control={<Radio />} label="Private" disabled={loading || isAdmin} />
              <FormControlLabel value="Commercial" control={<Radio />} label="Commercial" disabled={loading || isAdmin} />
            </RadioGroup>
          </FormControl>
          
          <FormControl className="form-control">
            <FormLabel component="legend">
              Please select the Fuel Type?<span className="required">*</span>
            </FormLabel>
            <RadioGroup
              row
              value={formData.fueltype}
              onChange={(e) => handleChange("fueltype", e.target.value)}
            >
              <FormControlLabel value="Petrol" control={<Radio />} label="Petrol" disabled={loading || isAdmin} />
              <FormControlLabel value="PetrolCng" control={<Radio />} label="Petrol + CNG" disabled={loading || isAdmin} />
              <FormControlLabel value="EV" control={<Radio />} label="EV" disabled={loading || isAdmin} />
              <FormControlLabel value="Diesel" control={<Radio />} label="Diesel" disabled={loading || isAdmin} />
            </RadioGroup>
          </FormControl>

          {(formData.fueltype === "Petrol") && <FormControl className="form-control">
            <FormLabel component="legend">
              Is it a CNG vehicle externally fitted?
            </FormLabel>
            <RadioGroup
              row
              value={formData.isCNG}
              onChange={(e) => handleChange("isCNG", e.target.value)}
            >
              <FormControlLabel value="yes" control={<Radio />} label="Yes" disabled={loading || isAdmin} />
              <FormControlLabel value="no" control={<Radio />} label="No" disabled={loading || isAdmin} />
            </RadioGroup>
          </FormControl>}

          <FormControl className="form-control">
            <FormLabel component="legend">
              Did the customer file a claim last year? <span className="required">*</span>
            </FormLabel>
            <RadioGroup
              row
              value={formData.claimLastYear}
              onChange={(e) => handleChange("claimLastYear", e.target.value)}
            >
              <FormControlLabel value="yes" control={<Radio />} label="Yes" disabled={loading || isAdmin} />
              <FormControlLabel value="no" control={<Radio />} label="No" disabled={loading || isAdmin} />
            </RadioGroup>
          </FormControl>

          <FormControl className="form-control">
            <FormLabel component="legend">
              What was last year's NCB? <span className="required">*</span>
            </FormLabel>
            <RadioGroup
              row
              value={formData.lastYearNCB}
              onChange={(e) => handleChange("lastYearNCB", e.target.value)}
            >
              <FormControlLabel value="0%" control={<Radio />} label="0%" disabled={loading || isAdmin} />
              <FormControlLabel value="20%" control={<Radio />} label="20%" disabled={loading || isAdmin} />
              <FormControlLabel value="25%" control={<Radio />} label="25%" disabled={loading || isAdmin} />
              <FormControlLabel value="35%" control={<Radio />} label="35%" disabled={loading || isAdmin} />
              <FormControlLabel value="45%" control={<Radio />} label="45%" disabled={loading || isAdmin} />
              <FormControlLabel value="50%" control={<Radio />} label="50%" disabled={loading || isAdmin} />
              <FormControlLabel value="notSure" control={<Radio />} label="Not Sure" disabled={loading || isAdmin} />
            </RadioGroup>
          </FormControl>

          <FormControl className="form-control">
            <FormLabel component="legend">
              Ownership transfer in the last 12 months? <span className="required">*</span>
            </FormLabel>
            <RadioGroup
              row
              value={formData.ownershipTransfer}
              onChange={(e) => handleChange("ownershipTransfer", e.target.value)}
            >
              <FormControlLabel value="yes" control={<Radio />} label="Yes" disabled={loading || isAdmin} />
              <FormControlLabel value="no" control={<Radio />} label="No" disabled={loading || isAdmin} />
            </RadioGroup>
          </FormControl>

          <FormControl className="form-control">
            <FormLabel component="legend">
              What was last year's IDV?
            </FormLabel>
            <RadioGroup
              row
              value={formData.lastYearIDV}
              onChange={(e) => handleChange("lastYearIDV", e.target.value)}
            >
              <FormControlLabel
                value="known"
                control={<Radio />}
                label={
                  <TextField
                    id="lastYearIDV"
                    fullWidth
                    variant="outlined"
                    className="custom-input"
                    placeholder="₹"
                    value={formData.lastYearIDVValue}
                    onChange={(e) => handleChange("lastYearIDVValue", e.target.value)}
                    disabled={loading || isAdmin}
                  />
                }
                disabled={loading || isAdmin}
              />
              <FormControlLabel value="notSure" control={<Radio />} label="Not Sure" disabled={loading || isAdmin} />
            </RadioGroup>
          </FormControl>

          <FormControl fullWidth className="form-control">
            <FormLabel component="legend">
              Last Year Insurer
            </FormLabel>
            <Select
              id="lastInsurer"
              value={formData.lastInsurer}
              onChange={(e) => handleChange("lastInsurer", e.target.value)}
              fullWidth
              disabled={loading || isAdmin}
            >
              <MenuItem value="">Select Insurer</MenuItem>
              {insurerList.map((insurer) => (
                <MenuItem key={insurer.OldSupplierId} value={insurer.SupplierDisplayName}>
                  {insurer.SupplierDisplayName}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth className="form-control">
            <FormLabel component="legend">
              Comment Box
            </FormLabel>
            <TextField
              id="commentBox"
              multiline
              rows={3}
              value={formData.commentBox}
              onChange={(e) => handleChange("commentBox", e.target.value)}
              disabled={loading || isAdmin}
            />
          </FormControl>

          {!isAdmin && <Button
            variant="contained"
            color="primary"
            className="savechangeBtn"
            onClick={handleSubmit}
            disabled={loading || isAdmin}
          >
            {loading ? "Loading..." : "Save Changes"}
          </Button>}
        </form>
      </Box>
    </ModalPopup>
  );
};

export default MotorChecklistPopup;