import React, { useEffect, useState } from "react";
import { IconButton } from "@mui/material";
import ModalPopup from '../../../../components/Dialogs/ModalPopup';
import { CALL_API } from "../../../../services/api.service";
import { gaEventTracker } from '../../../../../src/helpers/index';
import User from "../../../../services/user.service";

export const LeadAdditionalInfoPopup = (props) => {
    const { lead } = props;
    const [AdditionalInfo, SetAdditionalInfo] = useState([]);
    const [loading, setLoading] = useState(false); 

    useEffect(() => {
        if (lead && lead.LeadID && lead.LeadID > 0) {
            let commaseperatedGroupList = "";
            if(User && User.GroupList && Array.isArray(User.GroupList) && User.RoleId === 13)
            {
                commaseperatedGroupList = User.GroupList.map((group) => group.GroupId).join(',');
                commaseperatedGroupList = commaseperatedGroupList.toString();
            }
            gaEventTracker("LeadAdditionalInfoPopup-PopUpOpen", JSON.stringify({ GroupId: commaseperatedGroupList, empId: User.EmployeeId }), lead.LeadID);
            setLoading(true);
            GetLeadAdditionalInfoFromCJ(lead.LeadID).then((res) => { 
                if (res && res.Status && res.Data && Array.isArray(res.Data) && res.Data.length > 0) {
                    SetAdditionalInfo(res.Data);
                } else {
                    SetAdditionalInfo([]);
                }  
            }).catch((err) => {
                console.log("Error in GetLeadAdditionalInfoFromCJ" + err.message);
                SetAdditionalInfo([]);
            }).finally(() => {
                setLoading(false);
            });
        }
      }, []);

      const GetLeadAdditionalInfoFromCJ = async (LeadID) => {
        const input =
        {
            url: 'api/WebSiteService/GetAdditionalLeadDetailsFromCJ/' + LeadID,
            method: "GET",
            service: "MatrixCoreAPI"
        }
        return CALL_API(input);
    }


    return (
        <>
            <ModalPopup open={props.open} title="Additional Lead Info" className="AdditionaInfoPopup" handleClose={props.handleClose}>
                    <p className="leadId"> Lead ID - {lead.LeadID}</p>
                    <IconButton onClick={props.handleClose} className="crossIcon">
                    </IconButton>
                    {
                        loading ? ( // Show loading while data is being fetched
                            <div className="loading">Loading...</div>
                        ) : AdditionalInfo && AdditionalInfo.length === 0 ? ( // Show no data if API returns empty
                            <div className="no-data">No additional information available.</div>
                        ) : (
                            <div className="qna-container">
                                {AdditionalInfo.map((item, index) => (
                                    <div key={index} className="qna-item">
                                        <div className="question">
                                            <strong>Q:</strong> {item.Key}
                                        </div>
                                        <div className="answer">
                                            <strong>A:</strong> {item.Value}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )
                    }
            </ModalPopup>
        </>
    )
}