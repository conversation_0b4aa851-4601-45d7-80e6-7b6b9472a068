import { Container, Grid, Tooltip,IconButton } from "@mui/material";
import React,{useEffect, useState} from "react";
import makeStyles from '@mui/styles/makeStyles';
import { CONFIG } from "../../../appconfig";
import LastPageIcon from '@mui/icons-material/LastPage';
import FirstPageIcon from '@mui/icons-material/FirstPage';
import { GetSelfInforcementRatingData,GetHierarchialInforcementRatingData,GetSelfInforcementRatingDataMonthWise,FetchBHRBookingDataTLWise} from "../../../../src/services/Common";
import {DownLevelBizEnforcementData} from "../../SV/Main/DownLevelBizEnforcementData";
import User from "../../../../src/services/user.service";
import { v4 as uuid } from 'uuid';
import SaveAltIcon from '@mui/icons-material/SaveAlt';
import { enqueueSnackbar } from "notistack";
import ExcelJS from 'exceljs';
import { saveAs } from "file-saver";


const useStyles = makeStyles({
    table: {
        minWidth: 700,
    },
});

export default function UpperHierarchyBizBaseData(props){

    const classes = useStyles();
    let [SelfEmployeeCode, setSelfEmployeeCode] = useState('');
    let [SelfEmployeeID, setSelfEmployeeID] = useState('');
    let [SelfEmployeeName, setSelfEmployeeName] = useState('');
    let [SelfEmployeeRole, setSelfEmployeeRole] = useState('');
    let [MinYear, setMinYear] = useState(2022);//As panel created for bookings after apr 2022
    let [MinMonth, setMinMonth] = useState(4);//As panel created for bookings after apr 2022
    let [MaxYear, setMaxYear] = useState(parseInt(new Date().getFullYear()));
    let [MaxCurrentMonth, setMaxCurrentMonth] = useState(parseInt(new Date().getMonth() + 1));
    let [DownLevelEmployeeRole, setDownLevelEmployeeRole] = useState('');
    let [SelfBizRating, setSelfBizRating] = useState('');
    let [DownLevelEmployeeCount, setDownLevelEmployeeCount] = useState('');
    let [DownLevelTotalPolicies, setDownLevelTotalPolicies] = useState('');
    let [DownLevelInforceCount, setDownLevelInforceCount] = useState('');
    let [DownLevelNotIssuedCount, setDownLevelNotIssuedCount] = useState('');
    let [DownLevelLapsedCount, setDownLevelLapsedCount] = useState('');
    let [DownLevelCPICount, setDownLevelCPICount] = useState('');
    let [Year, setYear] = useState(String(new Date().getFullYear()));
    let [Month, setMonth] = useState('0');
    let [MonthWiseData, setMonthWiseData] = useState([]);
    let [DownLevelBaseData, setDownLevelBaseData] = useState([]);
    let [IsDataLoading, setIsDataLoading] = useState(false);
    

    useEffect(() => {
        let productId = getProductIdFromCurrentUrl();
        setIsDataLoading(true);
        GetHierarchialInforcementRatingData(User.UserId,'Self',productId).then((result) => {
            setIsDataLoading(false);
            if (result) {
                if(Array.isArray(result))
                    {  let consData = JSON.parse(JSON.stringify(result));
                      if(Array.isArray(consData) && consData.length>=2)
                      {
                        if(Array.isArray(result[0])){
                            result[0].forEach((row) => {row.uuid = uuid();});
                            setDownLevelBaseData(result[0]);
                        }
                        if(Array.isArray(result[1])){
                        setSelfEmployeeCode(result[1][0].SelfEmployeeCode);
                        setSelfEmployeeID(result[1][0].SelfEmployeeID);
                        setSelfEmployeeName(result[1][0].SelfEmployeeName);
                        setSelfEmployeeRole(result[1][0].SelfEmployeeRole);
                        if(result[1][0].SelfEmployeeRole === 'SalesHead')
                        {
                            setDownLevelEmployeeRole('Managers');
                        }
                        if(result[1][0].SelfEmployeeRole === 'Manager')
                        {
                            setDownLevelEmployeeRole('AMs');
                        }
                        if(result[1][0].SelfEmployeeRole === 'AM')
                        {
                            setDownLevelEmployeeRole('TLs');
                        }
                        if(result[1][0].SelfEmployeeRole === 'TL')
                        {
                            setDownLevelEmployeeRole('Agents');
                        }
                        setSelfBizRating(result[1][0].SelfBizRating);
                        setDownLevelEmployeeCount(result[1][0].DownLevelEmployeeCount);
                        setDownLevelTotalPolicies(result[1][0].DownLevelTotalPolicies);
                        setDownLevelInforceCount(result[1][0].DownLevelInforceCount);
                        setDownLevelNotIssuedCount(result[1][0].DownLevelNotIssuedCount);
                        setDownLevelLapsedCount(result[1][0].DownLevelLapsedCount);
                        setDownLevelCPICount(result[1][0].DownLevelCPICount);
                        }
                    }
                    }
                
                // GetHierarchialInforcementRatingData(User.UserId,result.SelfRoleName).then((res) => {
                //     if(Array.isArray(res)){
                //         res.forEach((row) => {row.uuid = uuid();});
                //         setDownLevelBaseData(res);}
                // });
                let PercentageColor = parseFloat(result[1][0].SelfBizRating);
                
                if(productId == "115")
                {
                    // change color dynamically
                    if(PercentageColor > 55 && PercentageColor <= 70){ //yellow
                        const root = document.documentElement;
                        root.style.setProperty('--background-color','#FFFBEA' );
                        root.style.setProperty('--button-color','#E6E6E6' );
                        root.style.setProperty('--box-color','#D9B000' );
                    }
                    else if(PercentageColor > 70){ //green
                        const root = document.documentElement;
                        root.style.setProperty('--background-color','#EDFFEA' );
                        root.style.setProperty('--button-color','#E6E6E6' );
                        root.style.setProperty('--box-color','#00B222' );
                    }
                    else if(PercentageColor <= 55 && PercentageColor >= 0){ //red
                        const root = document.documentElement;
                        root.style.setProperty('--background-color','#FFEFEF' );
                        root.style.setProperty('--button-color','#E6E6E6' );
                        root.style.setProperty('--box-color','#E6695F' );
                    }
                }
            }
        });
    }, []);

    const handleFetchBHRBookingDataTLWise = () => {
        let ProductID = getProductIdFromCurrentUrl();
        FetchBHRBookingDataTLWise(SelfEmployeeCode,SelfEmployeeRole,ProductID).then(async (result) => {
            if(result && result[0] &&  Array.isArray(result[0]) && result[0].length > 0)
            {
                var data = result[0];
                const workbook = new ExcelJS.Workbook();
                const worksheet = workbook.addWorksheet('Sheet1');

                worksheet.columns = [
                  { header: "EmployeeId", key: 'EmployeeId', width: 12 },
                  { header: "Booking Id", key: 'LeadID', width: 18 },
                  { header: "Booking Date", key: "BookingDate", width: 12 },
                  { header: "Final Status", key: "Remark", width: 28 },
                ];
            
                data.forEach((item) => {
                  worksheet.addRow(item);
                });
            
                const buffer = await workbook.xlsx.writeBuffer();
                saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `${SelfEmployeeName}'s Agents Report.xlsx`);
            }
            else{
                enqueueSnackbar("Data not found.", { variant: 'error', autoHideDuration: 3000 });
            }
        });
    }

    function getProductIdFromCurrentUrl() {
        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);
        return params.get('ProductID');
    }

      const YearInc = () =>
      {
          let currentyear = parseInt(new Date().getFullYear());
          if(!(parseInt(Year) >= currentyear))
          setYear(String(parseInt(Year) + 1)); 
          setMonth('0');
      }
      const YearDec = () =>
      {
          if(!(parseInt(Year) <= 2022))
          setYear(String(parseInt(Year) - 1)); 
          setMonth('0');
      }
      const MonthChange = (month) =>
      {
          setMonth(month);
          GetSelfInforcementRatingDataMonthWise(month,Year).then((Result) => {
            if (Result) {
              let MonthWiseConsolidatedData = {
                  "TotalBooking": Result.TotalPolicies ? Result.TotalPolicies:0,
                  "InforceRating": Result.MonthRating ? Result.MonthRating:0,
                  "InforceCount": Result.InForcePolicies ? Result.InForcePolicies:0,
                  "LapsedCount": Result.LapsedPolicies ? Result.LapsedPolicies:0,
                  "NotIssuedCount": Result.NotIssuedPolicies ? Result.NotIssuedPolicies:0,
                  "CancelledPostIssuanceCount" : Result.CPIPolicies ? Result.CPIPolicies:0
              };
              setMonthWiseData(MonthWiseConsolidatedData);
            }
            else
            {
                let MonthWiseConsolidatedData = {
                    "TotalBooking": 0,
                    "InforceRating": 0,
                    "InforceCount": 0,
                    "LapsedCount": 0,
                    "NotIssuedCount": 0,
                    "CancelledPostIssuanceCount" : 0
                };
                setMonthWiseData(MonthWiseConsolidatedData);
            }
          });
      }
      function isWithinOneYearAndOneMonth(year, month) {
        let productId = getProductIdFromCurrentUrl();
        if(productId !=7)
            return true;
        
        // Get the current date
        const currentDate = new Date();
        
        // Adjust the current date to be one month back
        currentDate.setMonth(currentDate.getMonth() - 1);
      
        // Get the date one year and one month back from the current date
        const oneYearAndOneMonthAgoDate = new Date(currentDate);
        oneYearAndOneMonthAgoDate.setFullYear(currentDate.getFullYear() - 1);
        oneYearAndOneMonthAgoDate.setMonth(currentDate.getMonth() - 1);
      
        // Create a date object for the given year and month
        const givenDate = new Date(year, month - 1); // Month is 0-based in JavaScript Date
      
        // Check if the given date is within the range
        return givenDate >= oneYearAndOneMonthAgoDate && givenDate <= currentDate;
      }

      return (
        <>
        {IsDataLoading == true ? 
            <><h5 className="Headingstyle">Loading....</h5></>
            :
            <>
            {(SelfBizRating !==undefined && SelfBizRating !=='') ?
                <div className="RatingContainer">
                    <Container maxWidth="lg">
                        <Grid container spacing={2}>
                            <Grid item sm={7} md={7} xs={12}>
                                <h3 className="heading">Business Health Rating {SelfEmployeeRole === 'TL' && 
                                    <IconButton onClick={() => handleFetchBHRBookingDataTLWise()}>
                                        <SaveAltIcon/>
                                    </IconButton>}
                                </h3>
                            </Grid>
                            <Grid item sm={12} md={12} xs={12}>
                                <div className="RatingBox">
                                    <div className="Box">  
                                    <img alt="Leads" src={CONFIG.PUBLIC_URL + "/images/forceRating/rating.svg"} />   
                                    <Tooltip className="forceRatingTooltip" classes={{ popper: "forceRatingTooltipPopup" }} title={<div><p>How this is calculated?</p>
                                    <p className="msg">Business health rating = Inforce bookings / Total bookings for e.g if total bookings are 100 and Inforce bookings are 45 then Business Health rating = 45/100 = 45%</p>
                                    <ul><li>Total Booking:</li> <li>100</li>
                                            <li>InForce Bookings:</li> <li>45</li>
                                            <li>Rating:</li> <li className="textred">45%</li>
                                            <li>45/100*100</li>
                                        </ul></div>} arrow>
                                            <img alt="Leads" src={CONFIG.PUBLIC_URL + "/images/forceRating/info.svg"} />
                                        </Tooltip>                          
                                        <p>Combined Business Health Rating (team)</p>
                                        <h3>{SelfBizRating}% </h3>
                                    </div>
                                    <div className="Box">  
                                    <img alt="Leads" src={CONFIG.PUBLIC_URL + "/images/forceRating/icon1.svg"} />                                   
                                        <p>Total {DownLevelEmployeeRole}</p>
                                        <h3>{DownLevelEmployeeCount}</h3>
                                    </div>
                                    <div className="Box">  
                                    <img alt="Leads" src={CONFIG.PUBLIC_URL + "/images/forceRating/Icon.svg"} />                                   
                                        <p>Total Policies</p>
                                        <h3>{DownLevelTotalPolicies}</h3>
                                    </div>
                                    <div className="Box">    
                                    <img alt="Leads" src={CONFIG.PUBLIC_URL + "/images/forceRating/icon2.svg"} />                                 
                                        <p>In Forced</p>
                                        <h3>{DownLevelInforceCount}</h3>
                                    </div>
                                    <div className="Box">  
                                    <img alt="Leads" src={CONFIG.PUBLIC_URL + "/images/forceRating/icon3.svg"} />                                   
                                        <p>Not Issued</p>
                                        <h3>{DownLevelNotIssuedCount}</h3>
                                    </div>
                                    <div className="Box">   
                                    <img alt="Leads" src={CONFIG.PUBLIC_URL + "/images/forceRating/icon4.svg"} />                                  
                                        <p>Lapsed</p>
                                        <h3>{DownLevelLapsedCount}</h3>
                                    </div>
                                    <div className="Box"> 
                                    <img alt="Leads" src={CONFIG.PUBLIC_URL + "/images/forceRating/icon5.svg"} />                                    
                                        <p>Cancelled Post Issuance</p>
                                        <h3>{DownLevelCPICount}</h3>
                                    </div>
                                </div>
                            </Grid>
                            <Grid item sm={4} md={3} xs={12}>
                                <div className="calendarBox">   
                                <div className="MonthYearcalendar">
                                    <div className="yearBox">
                                        <span onClick={() => { YearDec() }} className="filterIcon">
                                            <FirstPageIcon className={parseInt(Year) <= 2022 ? 'disabledIcon' : ''}/>
                                        </span>{Year}<span onClick={() => { YearInc() }} className="filterIcon">
                                            <LastPageIcon className={(parseInt(Year) >= parseInt(new Date().getFullYear())) ? 'disabledIcon' : ''}/>
                                        </span>
                                    </div>
                                    <ul className="MonthBox">
                                        {((parseInt(Year) >= MaxYear && MaxCurrentMonth < 1) || (parseInt(Year) === MinYear && MinMonth > 1) || !isWithinOneYearAndOneMonth(parseInt(Year),1)) ?
                                            <li className="disabled">Jan</li>
                                            :
                                            <li onClick={() => { MonthChange('01') }} className={Month === '01' ? 'active' : ''}>Jan</li>
                                        }
                                        {((parseInt(Year) >= MaxYear && MaxCurrentMonth < 2) || (parseInt(Year) === MinYear && MinMonth > 2) || !isWithinOneYearAndOneMonth(parseInt(Year),2)) ?
                                            <li className="disabled">Feb</li>
                                            :
                                            <li onClick={() => { MonthChange('02') }} className={Month === '02' ? 'active' : ''}>Feb</li>
                                        }
                                        {((parseInt(Year) >= MaxYear && MaxCurrentMonth < 3) || (parseInt(Year) === MinYear && MinMonth > 3) || !isWithinOneYearAndOneMonth(parseInt(Year),3)) ?
                                            <li className="disabled">Mar</li>
                                            :
                                            <li onClick={() => { MonthChange('03') }} className={Month === '03' ? 'active' : ''}>Mar</li>
                                        }
                                        {((parseInt(Year) >= MaxYear && MaxCurrentMonth < 4) || (parseInt(Year) === MinYear && MinMonth > 4) || !isWithinOneYearAndOneMonth(parseInt(Year),4)) ?
                                            <li className="disabled">Apr</li>
                                            :
                                            <li onClick={() => { MonthChange('04') }} className={Month === '04' ? 'active' : ''}>Apr</li>
                                        }
                                        {((parseInt(Year) >= MaxYear && MaxCurrentMonth < 5) || (parseInt(Year) === MinYear && MinMonth > 5) || !isWithinOneYearAndOneMonth(parseInt(Year),5)) ?
                                            <li className="disabled">May</li>
                                            :
                                            <li onClick={() => { MonthChange('05') }} className={Month === '05' ? 'active' : ''}>May</li>
                                        }
                                        {((parseInt(Year) >= MaxYear && MaxCurrentMonth < 6) || (parseInt(Year) === MinYear && MinMonth > 6) || !isWithinOneYearAndOneMonth(parseInt(Year),6)) ?
                                            <li className="disabled">Jun</li>
                                            :
                                            <li onClick={() => { MonthChange('06') }} className={Month === '06' ? 'active' : ''}>Jun</li>
                                        }
                                        {((parseInt(Year) >= MaxYear && MaxCurrentMonth < 7) || (parseInt(Year) === MinYear && MinMonth > 7) || !isWithinOneYearAndOneMonth(parseInt(Year),7)) ?
                                            <li className="disabled">Jul</li>
                                            :
                                            <li onClick={() => { MonthChange('07') }} className={Month === '07' ? 'active' : ''}>Jul</li>
                                        }
                                        {((parseInt(Year) >= MaxYear && MaxCurrentMonth < 8) || (parseInt(Year) === MinYear && MinMonth > 8) || !isWithinOneYearAndOneMonth(parseInt(Year),8)) ?
                                            <li className="disabled">Aug</li>
                                            :
                                            <li onClick={() => { MonthChange('08') }} className={Month === '08' ? 'active' : ''}>Aug</li>
                                        }
                                        {((parseInt(Year) >= MaxYear && MaxCurrentMonth < 9) || (parseInt(Year) === MinYear && MinMonth > 9) || !isWithinOneYearAndOneMonth(parseInt(Year),9)) ?
                                            <li className="disabled">Sep</li>
                                            :
                                            <li onClick={() => { MonthChange('09') }} className={Month === '09' ? 'active' : ''}>Sep</li>
                                        }
                                        {((parseInt(Year) >= MaxYear && MaxCurrentMonth < 10) || (parseInt(Year) === MinYear && MinMonth > 10) || !isWithinOneYearAndOneMonth(parseInt(Year),10)) ?
                                            <li className="disabled">Oct</li>
                                            :
                                            <li onClick={() => { MonthChange('10') }} className={Month === '10' ? 'active' : ''}>Oct</li>
                                        }
                                        {((parseInt(Year) >= MaxYear && MaxCurrentMonth < 11) || (parseInt(Year) === MinYear && MinMonth > 11) || !isWithinOneYearAndOneMonth(parseInt(Year),11)) ?
                                            <li className="disabled">Nov</li>
                                            :
                                            <li onClick={() => { MonthChange('11') }} className={Month === '11' ? 'active' : ''}>Nov</li>
                                        }
                                        {((parseInt(Year) >= MaxYear && MaxCurrentMonth < 12) || (parseInt(Year) === MinYear && MinMonth > 12) || !isWithinOneYearAndOneMonth(parseInt(Year),12)) ?
                                            <li className="disabled">Dec</li>
                                            :
                                            <li onClick={() => { MonthChange('12') }} className={Month === '12' ? 'active' : ''}>Dec</li>
                                        }
                                        <li onClick={() => { MonthChange('0') }} className={Month === '0' ? 'active' : ''}>All</li>
                                    </ul>                               
                                    </div>                           
                                    <hr />
                                    <div className="RatingList">
                                        <p>Rating of the Month <span>{Month === "0" ? SelfBizRating : MonthWiseData.InforceRating}%</span></p>
                                        <p>Total Bookings <span>{Month === "0" ? DownLevelTotalPolicies : MonthWiseData.TotalBooking}</span></p>                                   
                                        <p>Inforce <span>{Month === "0" ? DownLevelInforceCount : MonthWiseData.InforceCount}</span></p>
                                        <p>Lapsed <span>{Month === "0" ? DownLevelLapsedCount : MonthWiseData.LapsedCount}</span></p>
                                        <p>Not Issued <span>{Month === "0" ? DownLevelNotIssuedCount : MonthWiseData.NotIssuedCount}</span></p>
                                        <p>Cancelled Post Issuance <span>{Month === "0" ? DownLevelCPICount : MonthWiseData.CancelledPostIssuanceCount}</span></p>
                                    </div>
                                </div>
                            </Grid>
                            <Grid item sm={8} md={9} xs={12}>
                            <DownLevelBizEnforcementData data ={DownLevelBaseData !== undefined ? DownLevelBaseData : []} expandeddata = {[]} TLDataExport={handleFetchBHRBookingDataTLWise}/>
                            </Grid> 
                        </Grid>
                    </Container>

                </div>
                :
                <><h5 className="Headingstyle">No Data Found</h5></>
            }
            </>
        }
        </>
    )

}