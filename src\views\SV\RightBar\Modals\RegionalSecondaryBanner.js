import React from 'react'
import ModalPopup from '../../../../components/Dialogs/ModalPopup'
import { CONFIG } from '../../../../appconfig'
import './RegionalSecondaryBanner.css';

const RegionalSecondaryBanner = ({ name, amount, leadId, isOpen, handleClose }) => {

  return (
    <ModalPopup open={isOpen} handleClose={handleClose} className="regional-banner-modal">
      <div className="regional-banner-container">
        {/* Left Side - Regional Transfer Image */}
        <div className="regional-banner-image">
          <img
            src={`${CONFIG.PUBLIC_URL}/images/regional_transfer.png`}
            alt="Regional Transfer"
          />
        </div>

        {/* Right Side - Content */}
        <div className="regional-banner-content">
          <img
            src={`${CONFIG.PUBLIC_URL}/images/congratulations.png`}
            alt="Congratulations"
            className="regional-banner-congrats"
          />

          <p className="regional-banner-user-name">{name}</p>
          <p className="regional-banner-secondary-text">for earning a secondary</p>
          <p className="regional-banner-amount">APE of ₹{Math.round(amount)}</p>
          <p className="regional-banner-transfer-text">
            from the call transfer you made!
          </p>

          <h3 className="regional-banner-lead-id">
            <strong>Lead ID:</strong> {leadId}
          </h3>

          <img
            src={`${CONFIG.PUBLIC_URL}/images/bottom_bar_language.png`}
            alt="Language Bar"
            className="regional-banner-language-bar"
          />
        </div>
      </div>
    </ModalPopup>
  )
}

export default RegionalSecondaryBanner  