import React, { useEffect, useState } from "react";
import ScrollMenu from 'react-horizontal-scrolling-menu';
import { ChevronRight, ChevronLeft } from '@mui/icons-material';
import { connect, useSelector } from "react-redux";
import { CONFIG, SV_CONFIG } from "../../../appconfig";
import pluck from 'underscore/modules/pluck';
import Common from "../Main/helpers/Common";
import { setRefreshLead } from "../../../store/actions/SalesView/SalesView";
const Arrow = ({ text, className }) => {
    return (
        <div className={className}>{text}</div>
    );
};

const ArrowLeft = Arrow({ text: <ChevronLeft />, className: 'arrow-prev' });
const ArrowRight = Arrow({ text: <ChevronRight />, className: 'arrow-next' });

const BookedList = (props) => {
    //let [parentLeadId] = useSelector(({ salesview }) => [salesview.parentLeadId]);
    // const [Customers, setCustomers] = useState([{ 'Key': 'test1' }, { 'Key': 'test2' }, { 'Key': 'test3' }]);
    // const { showCaller, setShowCaller } = props;
    // let [Filterarr, setFilterarr] = useState({});
    let [Page, setPage] = useState([]);
    // const [AgentBookedLeads, setAgentBookedLeads] = useState([]);
    const [BMSUserToken, setBMSUserToken] = useState("");
    const isMobile = useSelector(state => state.common.isMobile);

    let [AgentStats] = useSelector(state => {
        let { AgentStats } = state.salesview;
        return [AgentStats];
    });
    //let arr = [];
    let arr = AgentStats[0] && Array.isArray(AgentStats[0].BookedLeads) ? AgentStats[0].BookedLeads : [];
    // let arr = [
    //     {
    //         "BookingId": 42885086,
    //         "Name": "karishma",
    //         "EncryptedLeadId": null,
    //         "EmailOnly": false,
    //         "CallStatus": null,
    //         "Counter": 12,
    //         "ts": "2021-03-23T17:39:35.1242265+05:30",
    //         "LeadId": 42885086,
    //         "Priority": 0,
    //         "Reason": "New Lead",
    //         "ReasonId": 6,
    //         "ProductId": 7,
    //         "ProposalNo": null,
    //         "CustomerId": 10507478,
    //         "IsReligare": false
    //     },
    //     {
    //         "BookingId": 0,
    //         "Name": "Dh f",
    //         "EncryptedLeadId": null,
    //         "EmailOnly": false,
    //         "CallStatus": null,
    //         "Counter": 21,
    //         "ts": "2021-03-24T11:08:05.2036245+05:30",
    //         "LeadId": 42948012,
    //         "Priority": 0,
    //         "Reason": "Unanswered Lead",
    //         "ReasonId": 15,
    //         "ProductId": 2,
    //         "ProposalNo": null,
    //         "CustomerId": 10683054,
    //         "IsReligare": false
    //     },
    // ]


    const OpenBMS = (LeadId) => {
        if (AgentStats && AgentStats.length > 0) {
            var arr = AgentStats[0].BookedLeads.find(x => x.LeadId === LeadId);
            if (arr) {
                let BmsUrl = SV_CONFIG["BmsSalesUrl"][SV_CONFIG["environment"]];
                let link = BmsUrl + arr.EncryptedLeadId + "?token=" + BMSUserToken + "&source=OneLead";
                window.open(link);
            }
        }
        else return;

    }

    const OpenSvView = (e, data) => {
        OpenBMS(data.LeadId);
        Common.OpenSalesView(data.CustomerId, data.ProductId, data.LeadId, 's', data.Reason, data.ReasonId)
        props.setRefreshLeadToRedux(true);
    }

    // const fnOpenBMS = function () {

    //     //if (ServiceLead && ServiceLead.leadId && ServiceLead.callType == "Inbound") {

    //         var userid = User.UserId;
    //         var leadid = ServiceLead.leadId;
    //         const input = {
    //             url: url + `/api/User/GetServiceUrlForLead`,
    //             method: "POST",
    //             service: "BMSURL",
    //             timeout: 'l',
    //         };
    //         CALL_API(input).then((result) => {
    //             if (Array.isArray(result)) {
    //var url = result.data.RedirectUrl;
    //window.open(url);
    //             }
    //         });

    //     //}
    // };

    // const groupBy = (arr, property) => {
    //     return arr.reduce(function (memo, x) {
    //         if (!memo[x[property]]) { memo[x[property]] = []; }
    //         memo[x[property]].push(x);
    //         return memo;
    //     }, {});
    // }

    useEffect(() => {
        if (arr.length > 0) {
            //  setFilterarr(groupBy(arr, 'Page'));
            setPage(pluck(arr, 'Page'));
        }
    }, [AgentStats]);

    // const Settitle = (str) => {
    //     let acronym = "";
    //     if (str) {
    //         let matches = str.match(/\b(\w)/g);
    //         acronym = matches.join('').toUpperCase();
    //     }
    //     return acronym
    // }

    // const SetPageColor = (Index, CurrrentPage) => {
    //     let _Page = Page.indexOf(CurrrentPage);
    //     switch (_Page) {
    //         case 0:
    //             return 'green';
    //         case 1:
    //             return 'violet';
    //         default:
    //             return 'orange';
    //     }
    // }


    let CustomersHTML = [];


    CustomersHTML.push(
        <div>
            <div className="footer-list">
                {arr.map((vdata, innerIdx) => (
                    <div key={innerIdx} className="footer-bl">
                        <div className="right-block">
                            {/* <p className="name">{vdata.Name}</p> */}
                            {/* <p onClick={(e) => { OpenSvView(e, vdata) }} className="id-date">{vdata.LeadId} | {dayjs(vdata.ts).format('h:mm a')}</p> */}
                            <p onClick={(e) => { OpenSvView(e, vdata) }} className="id-date">{vdata.LeadId} </p>
                            <span className="bookLead">{vdata.Reason} </span>


                        </div>
                    </div>
                ))}

            </div>
        </div>
    )



    return (
        <div id="OnlineCustomers">
            {arr.length > 0 ?
                (!isMobile)
                    ? < ScrollMenu
                        data={CustomersHTML}
                        arrowLeft={ArrowLeft}
                        arrowRight={ArrowRight}
                        translate={1}
                        alignCenter={false}
                        hideSingleArrow={true}
                        useButtonRole={false}
                        disableTabindex
                        wheel={false}
                    />
                    : CustomersHTML
                : <h1 className="no-customer"><img alt='' src={CONFIG.PUBLIC_URL + "/images/salesview/no-customeronline.svg"} /> No Booked Lead <p>Check back again after sometime</p></h1>
            }
        </div>
    )
}

const mapStateToProps = state => {
    return {

    };
};

const mapDispatchToProps = dispatch => {

    return {
        setRefreshLeadToRedux: (value) => dispatch(setRefreshLead({ RefreshLead: value }))
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(BookedList);

