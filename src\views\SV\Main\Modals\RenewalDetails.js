import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import dayjs from "dayjs";
import { Button, Grid, useMediaQuery, useTheme } from '@mui/material';
import { FetchProposalFormUrl } from "../../../../services/Common";
import { useSnackbar } from "notistack";

export const RenewalDetails = (props) => {

    const RenewDetails = props.RenewDetail;
    const { enqueueSnackbar } = useSnackbar();

    const [show, setShow] = useState(false);
    const [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);

    useEffect(() => {
        if (RenewDetails) {
            setShow(true);
        }
    }, [props.RenewDetail]);

    const FetchProposalForm = (LeadId) => {
        FetchProposalFormUrl(LeadId).then((result) => {
            if (result.indexOf('://') !== -1) {
                window.open(result)
            }
            else {
                enqueueSnackbar(result, { variant: 'success', autoHideDuration: 3000, });
            }
        }).catch((e) => {
            console.log(e);
        })
    }

    return <>
        {show && <>
            <h4 className="heading">Renewal Details</h4>
            <div>
                <Grid container className="container" spacing={1}>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Proposer Name:</label>
                        <span>{RenewDetails.ProposerName}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Old Policy No:</label>
                        <span>{RenewDetails.OldPolicyNo}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Policy Expiry Date:</label>
                        <span>{RenewDetails.PolicyExpiryDate === -62135616600000 || RenewDetails.PolicyExpiryDate === undefined || RenewDetails.PolicyExpiryDate <= 0 ? "N/A" : dayjs(RenewDetails.PolicyExpiryDate).format("DD/MM/YYYY")}</span>
                    </Grid>

                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Plan Name:</label>
                        <span>{RenewDetails.Plan}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Supplier Name: </label>
                        <span>{RenewDetails.SupplierName}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Basic Sum Insured:</label>
                        <span>{RenewDetails.SumInsured}</span>
                    </Grid>

                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>No Claim Bonus:</label>
                        <span>{RenewDetails.NCB}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Notice Premium:</label>
                        <span>{RenewDetails.Premium}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Grace Period:</label>
                        <span>{RenewDetails.GracePeriod}</span>
                    </Grid>

                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>IsHealthCheckUpTaken:</label>
                        <span>{RenewDetails.FreeHealthCheckup === "1" ? "Yes" : "No"}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Previous policy term:</label>
                        <span>{RenewDetails.PlanTerm}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>WellnessPoint:</label>
                        <span>{RenewDetails.WellnessPoint}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>MonthlyMode:</label>
                        <span>{RenewDetails.MonthlyMode === "1" ? "Yes" : "No"}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>ACH:</label>
                        <span>{RenewDetails.ACH === 1 ? "Yes" : (RenewDetails.ACH === "Yes" ? "Yes" : "No")}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>PolicyInceptionDate:</label>
                        <span>{RenewDetails.PolicyInceptionDate == -62135616600000 || RenewDetails.PolicyInceptionDate === undefined || RenewDetails.PolicyInceptionDate <= 0 ? "N/A" : dayjs(RenewDetails.PolicyInceptionDate).format("DD/MM/YYYY")}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Was Port:</label>
                        <span>{RenewDetails.WasPort === 1 || RenewDetails.WasPort ? "Yes" : "No"}</span>
                    </Grid>

                    <Grid className="item" item xs={12}>
                        <label>Remarks:</label>
                        <span>{RenewDetails.Remarks}</span>
                    </Grid>
                </Grid>

            </div>
            <h4 className="heading">Upsell Details</h4>
            <div>

                <Grid container className="container" spacing={1}>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Renewal Year :</label>
                        <span>{RenewDetails.RenewalYear}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Upsell Sum Insured :</label>
                        <span>{RenewDetails.UPSellSI}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Upsell Premium :</label>
                        <span>{RenewDetails.UpsellPremium}</span>
                    </Grid>

                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Two year Premium :</label>
                        <span>{RenewDetails.TwoYrPremium}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Three year Premium : </label>
                        <span>{RenewDetails.ThreeYrPremium}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Add on Product:</label>
                        <span>{RenewDetails.addOnProduct}</span>
                    </Grid>

                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Add On BasePremium:</label>
                        <span>{RenewDetails.AddOnBasePremium}</span>
                    </Grid>
                </Grid>
            </div>

            <h4 className="heading">{RenewDetails.Feedback ? "Claims/Feedback Details" : "Claims Details"}</h4>
            <div>
                <Grid container className="container" spacing={1}>

                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Is Claims Taken: </label>
                        <span>{RenewDetails.IsPreviousClaimsTaken == 1 || RenewDetails.IsPreviousClaimsTaken ? "Yes" : "No"}</span>
                    </Grid>
                    {RenewDetails.Feedback && <>
                        <Grid className="item" item xs={12} md={6} lg={4}>
                            <label>Feedback : </label>
                            <span>{RenewDetails.Feedback.LevelOneFeedback ? RenewDetails.Feedback.LevelOneFeedback : RenewDetails.Feedback.Message}</span>
                        </Grid>
                        {RenewDetails.Feedback.LevelTwoFeedback &&
                            <>
                                <Grid className="item" item xs={12} md={6} lg={4}>
                                    <label>Details : </label>
                                    <span>{RenewDetails.Feedback.LevelTwoFeedback}</span>
                                </Grid>
                            </>
                        }
                    </>}
                </Grid>
            </div>

            {RenewDetails.PolicyMembers &&
                <>
                    <h4 className="heading">Insured Details</h4>
                    <table className="MemberDetails">
                        <thead>
                            <tr>
                                <th>Name of the Insured</th>
                                <th>DOB</th>
                                <th>Relationship</th>
                                <th>SumInsured</th>
                                <th>CumulativeBonus</th>
                            </tr>
                        </thead>
                        <tbody>
                            {RenewDetails.PolicyMembers.map((item, index) => (
                                <tr>
                                    <td>{item.InsuredName}</td>
                                    <td>{item.DOB}</td>
                                    <td>{item.RelationshipToPolicyHolder}</td>
                                    <td>{item.SumInsured}</td>
                                    <td>{item.CumulativeBonus}</td>
                                </tr>
                            ))}
                        </tbody>

                    </table>
                </>
            }

            {RenewDetails.CoveredMembers &&
                <>
                    <h4 className="heading">Proposer's Details</h4>
                    <table className="MemberDetails">
                        <thead>
                            <tr>
                                <th>RelationShipName</th>
                                <th>Name</th>
                                <th>Occupation</th>
                                <th>DateOfBirth</th>
                                <th>Height</th>
                                <th>Weight</th>
                            </tr>
                        </thead>
                        <tbody>
                            {RenewDetails.CoveredMembers.map((item, index) => (
                                <tr>
                                    <td>{item.Relation.RelationShipName}</td>
                                    <td>{item.Name}</td>
                                    <td>{item.OccupationValue}</td>
                                    <td>{item.DateOfBirth}</td>
                                    <td>{item.Height}</td>
                                    <td>{item.Weight}</td>

                                </tr>
                            ))}
                        </tbody>

                    </table>
                </>
            }

            <h4 className="heading">Loading Details</h4>
            <div>

                <Grid container className="container" spacing={1}>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Loading:</label>
                        <span>{RenewDetails.Loading}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>LoadingReason:</label>
                        <span>{RenewDetails.LoadingReason}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>LoadingPremium:</label>
                        <span>{RenewDetails.LoadingPremium}</span>
                    </Grid>

                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>IsLoading:</label>
                        <span>{RenewDetails.IsLoading == 1 || RenewDetails.IsLoading ? "Yes" : "No"}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>LoadingPercentage:</label>
                        <span>{RenewDetails.LoadingPercentage}</span>
                    </Grid>
                </Grid>
            </div>
            <h4 className="heading">Other Details</h4>
            <div>
                {RenewDetails.InceptionBookingId > 0 && <>
                    <Grid container className="container" spacing={1}>
                        <Button
                            className="viewBtn"
                            onClick={() => { FetchProposalForm(RenewDetails.InceptionBookingId) }}
                            title={'Renewal Details'}
                        >
                            Proposal Form
                        </Button>
                    </Grid>
                </>}

                <Grid container className="container" spacing={1}>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>PEDInfo :</label>
                        <span>{RenewDetails.PEDInfo}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>NSTPReason :</label>
                        <span>{RenewDetails.NSTPReason}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>STPStatus :</label>
                        <span>{RenewDetails.STPStatus}</span>
                    </Grid>

                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Address :</label>
                        <span>{RenewDetails.Address}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Pincode : </label>
                        <span>{RenewDetails.Pincode}</span>
                    </Grid>
                    <Grid className="item" item xs={12} md={6} lg={4}>
                        <label>Premium Inflation Reason:</label>
                        <span>{RenewDetails.PremiumInflationReason}</span>
                    </Grid>

                </Grid>
            </div>
        </>
        }
    </>
}