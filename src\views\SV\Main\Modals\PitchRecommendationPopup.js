import React from "react";
import ModalPopup from '../../../../components/Dialogs/ModalPopup';
import { Typography, IconButton, Tooltip } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import './PitchRecommendationPopup.scss';
import { CONFIG } from "../../../../appconfig";
import WarningRoundedIcon from '@mui/icons-material/WarningRounded';
import { FetchAgentAssist } from "../../../../services/Common";
import { useSnackbar } from "notistack";
import { useEffect, useState } from "react";

export const PitchRecommendationPopup = (props) => {
    const { enqueueSnackbar } = useSnackbar();
    const open = props.open;
    
    const [recommendations, setRecommendations] = useState([]);
    const [analysisData, setAnalysisData] = useState([]);
    const [loading, setLoading] = useState(false);


    const truncateText = (text, maxLength = 100) => {
        if (!text) return '';
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    };

    const parseIssueText = (issueText) => {
        if (!issueText) return { subtitle: '', description: '' };
        
        const colonIndex = issueText.indexOf(':');
        if (colonIndex === -1) {
            return { subtitle: '', description: issueText };
        }
        
        const subtitle = issueText.substring(0, colonIndex).trim();
        const description = issueText.substring(colonIndex + 1).trim();
        
        return { subtitle, description };
    };

    useEffect(() => {
        if (open && props.LeadID) {
            setLoading(true);
            FetchAgentAssist(props.LeadID)
                .then((result) => {
                    if (result) {
                        if (result.Messages && Array.isArray(result.Messages)) {
                            setRecommendations(result.Messages);
                        }

                        if (result.AnalysisList && Array.isArray(result.AnalysisList)) {
                            const filteredAnalysis = result.AnalysisList
                                .filter(item => item.Data && item.Data.Issue)
                                .slice(0, 5);
                            setAnalysisData(filteredAnalysis);
                        }
                    } else {
                        setRecommendations([]);
                        setAnalysisData([]);
                        enqueueSnackbar('No recommendations available', { variant: 'info' });
                    }
                })
                .catch((error) => {
                    console.error('Error fetching agent assist:', error);
                    enqueueSnackbar('Failed to fetch recommendations', { variant: 'error' });
                    setRecommendations([]);
                    setAnalysisData([]);
                })
                .finally(() => {
                    setLoading(false);
                });
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [open, props.LeadID]);

    return (
        <ModalPopup
            open={props.open}
            title=""
            handleClose={props.handleClose}
            className="pitchRecommendationPopup"
            showCloseButton={false}
        >
            <IconButton
                className="custom-close-button"
                onClick={props.handleClose}
                size="large"
            >
                <CloseIcon />
            </IconButton>

            <div className="pitch-header">
                <div className="pitch-icon">
                    <img src={CONFIG.PUBLIC_URL + "/images/salesview/communication.svg"} alt="pitch-icon" />
                </div>
                <Typography variant="h5" className="pitch-title">
                    Pitch Recommendations
                </Typography>

                {loading ? (
                    <Typography variant="body1" className="pitch-content">
                        Loading recommendations...
                    </Typography>
                ) : recommendations.length > 0 ? (
                    <div className="recommendations-list">
                        {recommendations.map((recommendation, index) => (
                            <div key={index} className="recommendation-item">
                                <Typography variant="body1" className="recommendation-text">
                                    {recommendation}
                                </Typography>
                            </div>
                        ))}
                    </div>
                ) : (
                    <Typography variant="body1" className="pitch-content">
                        No recommendations available
                    </Typography>
                )}
            </div>

            <div className="customer-concern-section">
                <div className="concern-header">
                    <WarningRoundedIcon className="concern-icon" />
                    <Typography variant="h6" className="concern-title">
                        Customer Concerns
                    </Typography>
                </div>

                <div className="concern-content">
                    {analysisData.length > 0 ? (
                        <ul className="analysis-list">
                            {analysisData.map((analysis, index) => {
                                const { subtitle, description } = parseIssueText(analysis.Data.Issue);
                                return (
                                    <li key={index} className="analysis-item">
                                        {subtitle && (
                                            <Typography variant="h6" className="concern-subtitle">
                                                {subtitle}
                                            </Typography>
                                        )}
                                        {description && (
                                            <Tooltip 
                                                title={description} 
                                                arrow 
                                                placement="top-start"
                                                classes={{ tooltip: 'custom-tooltip' }}
                                            >
                                                <Typography variant="body2" className="concern-description">
                                                    {truncateText(description, 50)}
                                                </Typography>
                                            </Tooltip>
                                        )}
                                    </li>
                                );
                            })}
                        </ul>
                    ) : (
                        <Typography variant="body2" className="no-concerns">
                            No customer concerns identified.
                        </Typography>
                    )}
                </div>
            </div>
        </ModalPopup>
    );
};
