const path = require('path');
const webpack = require('webpack');
require('dotenv').config();

const HtmlWebpackPlugin = require('html-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');

module.exports = {
  mode: 'development',
  devtool: 'inline-source-map',
  entry: './src/index.js',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'new.js',
    publicPath: '/'
  },
  devServer: {
    port: 3100,
    host: 'localhost',
    allowedHosts: 'all',
    historyApiFallback: true,
    client: {
      overlay: false,
    },
  },
  module: {
    rules: [
      {
        test: /\.(svg)$/i,
        type: "asset/resource",
        generator: {
          filename: "images/[name][ext]"
        },
      },
      {
        exclude: /node_modules/,
        test: /\.js$/,
        loader: 'babel-loader'
      },
      {
        test: /\.(sa|sc|c)ss$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
          },
          'css-loader',
          'postcss-loader',
          {
            loader: 'sass-loader'
          },
        ],
      },
      {
        test: /.(ttf|otf|eot|woff(2)?)(\?[a-z0-9]+)?$/,
        use: [{
          loader: 'file-loader',
          options: {
            name: '[name].[ext]',
            outputPath: '/fonts/'
          }
        }]
      }]
  },

  resolve: {
    extensions: ['.js', '.jsx', '.scss', '.css'],
  },
  plugins: [
    new webpack.DefinePlugin({
      "API_BASE_URL": JSON.stringify(process.env.API_BASE_URL),
      "TOKEN_KEY": JSON.stringify(process.env.AUTH_COOKIE_NAME),
      "COOKIE_LOGIN": JSON.stringify(process.env.COOKIE_LOGIN),
      "PUBLIC_URL": JSON.stringify(""),
      "BUILD_ENV": JSON.stringify(process.env.BUILD_ENV),
      "VERSION": JSON.stringify(new Date().getTime())
    }),
    new HtmlWebpackPlugin({
      template: './public/index.html',
      filename: './index.html',
    }),
    new CleanWebpackPlugin(),
    new MiniCssExtractPlugin({
      filename: '[name].css',
      chunkFilename: '[id].css',
    }),
    new CopyWebpackPlugin({
      patterns: [
        { from: 'public/images', to: 'images' },
        { from: 'public/libs', to: 'libs' }
      ]
    })
  ],
};