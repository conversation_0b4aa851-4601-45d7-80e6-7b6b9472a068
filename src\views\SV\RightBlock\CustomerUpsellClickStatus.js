import { <PERSON><PERSON>, Grid } from "@mui/material";
import { LinearProgress } from "@mui/material";
import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import rootScopeService from "../../../services/rootScopeService";
import { GetUpsellClickStatus } from "../../../services/Common";
import { ExpandMore } from "@mui/icons-material";
import { gaEventTracker } from "../../../helpers";
import User from "../../../services/user.service";

const CustomerUpsellClickStatus = () => {
    const [upsellresult, setupsellresult] = useState();
    const [CustomerUpsellLoading, setCustomerUpsellLoading] = useState(false);
    const [apifailed, setapifailed] = useState(false);
    let leadIds = useSelector(state => state.salesview.leadIds).split(';').join(',').slice(0, -1);
    const [show, setShow] = useState(false);
    let [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);
    let noOfLeadCardsToShow = useSelector(({ salesview }) => salesview.noOfLeadCardsToShow);
    const [IsRenewal] = useSelector(state => {
        let { IsRenewal } = state.salesview;
        return [IsRenewal]
    });
    useEffect(() => {
        if (RefreshLead) {
            setShow(false);
        }
    }, [RefreshLead]);

    const handleToggle = () => {
        setShow(!show);
        setupsellresult(null)
        if (noOfLeadCardsToShow <= 50) {
            if (show == false) {
                gaEventTracker("Health_Renewal","Customer_Activity_on_CJ",User.UserId)
                getUpsellstatus()
            }
        }
    }

    const getUpsellstatus = () => {
        setCustomerUpsellLoading(true);
        setapifailed(false);
        GetUpsellClickStatus(leadIds).then((result) => {
            if (result) {
                setupsellresult(result);
            }
        }).catch((e) => {
            setapifailed(true)
            console.log(e);
        })
            .finally(() => {
                setCustomerUpsellLoading(false);
            });
    }

    if (rootScopeService.getProductId() != 2 || !IsRenewal) {
        return null
    }
    return <>
        <Grid item sm={12} md={12} xs={12}>
            <div className="upsellstatus">
                <h3>Customer Activity on CJ</h3>
                <div className="expandmoreIcon">
                    <ExpandMore onClick={() => handleToggle()} style={{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)' }} /></div>
                {CustomerUpsellLoading && <LinearProgress />}
                {show && !CustomerUpsellLoading && <>
                    {upsellresult && Array.isArray(upsellresult.LeadWiseChanges) && upsellresult.LeadWiseChanges.filter(item => item.upsellFields.length > 0).length > 0  ?
                        <>
                            {upsellresult.LeadWiseChanges.map((item) => item.upsellFields.length > 0 ? <ul>{`LeadID:${item.LeadId}`} 
                            {item.upsellFields.slice(0,5).map((items) => <li> {items.key} </li>)} </ul> : null
                            )}
                        </> : <>{apifailed ? <Button variant="text" onClick={()=>getUpsellstatus()}>Click here to retry</Button> :
                         <p>No Details</p>}
                        </>
                    }
                </>
                }
            </div>
        </Grid>


    </>
}
export default CustomerUpsellClickStatus;