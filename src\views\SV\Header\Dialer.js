import React, { useEffect, useState } from "react";
import User from "../../../services/user.service";
import Common from "../Main/helpers/Common";
import { connect, useDispatch, useSelector } from 'react-redux'
import { setRefreshLead, updateStateInRedux } from "../../../store/actions/SalesView/SalesView";
import CallTransfer from "../RightBlock/CallTransfer";
import { ErrorBoundary } from "../../../hoc";
import CustFeedback from "../Main/CustFeedback";
import { CONFIG } from "../../../appconfig";
import { CALL_API } from "../../../services";
import { Tooltip } from "@mui/material";
import { Close } from "@mui/icons-material";
import { GetLeadCustomerDetails, IsApptfeedbackLead, IsAswatUser, IsHangupEnable, IsRemoveConnectCallSF } from "../../../services/Common";
import Dialpad from "./Dialpad";
import { gaEventTracker } from "../../../helpers";



const Dialer = (props) => {
    const { ConnectCallSF } = props;
    // const [hold, setHold] = useState(false);
    const [mute, setMute] = useState(false);
    const [showLeadMismatchTooltip, setShowLeadMismatchTooltip] = useState(false);
    const isCalledLeadMismatched = useSelector(state => state.salesview.isCalledLeadMismatched);
    const parentLeadId = useSelector(state => state.salesview.parentLeadId);
    const dispatch = useDispatch();

    var showHangup = false;
    let durationTime = 0;
    let displayCallStatus = "";
    const formatSeconds = function (seconds) {
        var date = new Date(1970, 0, 1);
        date.setSeconds(seconds);
        return date
            .toTimeString()
            .replace(/.*(\d{2}:\d{2}:\d{2}).*/, "$1");
    }
    // let CallInfo = window.localStorage.getItem("ConnectCallSF");
    if (ConnectCallSF) {
        var CallInitTime = ConnectCallSF.CallInitTime;
        if (CallInitTime) {
            durationTime = (new Date() - new Date(CallInitTime)) / 1000;
        }
        else {
            console.log('call ended');
        }
        //displayCallStatus = ConnectCallSF.CallStatus;
    }
    else {

        //alert('disconnect');
        //return null;
    }
    displayCallStatus = window.localStorage.getItem('callDisposition')
    var setCallStatus = window.localStorage.getItem('setCallStatus');
    // let feedbackTimer = window.localStorage.getItem('feedbackTimer');
    if (setCallStatus === "Hang Up") {
        showHangup = true;
    } else {

    }

    // const onHold = () => {
    //     window.CallHold()
    //     setHold(!hold);
    // }
    const onMute = () => {
        window.CallMute()
        setMute(!mute);
    }
    const CallDisconnect = () => {
        durationTime = 0;
        // window.localStorage.setItem("onCall", false);
        const _currentConnectCallSF = Common.getConnectCallSFFromLS();
        const CallInitTime = _currentConnectCallSF && _currentConnectCallSF.CallInitTime;
        if (CallInitTime != null) {
            const CurrentTime = JSON.stringify(new Date()).replace(/"/g, '');
            const CallDuration = Math.abs((new Date(CurrentTime) - new Date(CallInitTime)) / 1000);
            dispatch(updateStateInRedux({ key: "LastCallDuration", value: CallDuration }));
        } else {
            dispatch(updateStateInRedux({ key: "LastCallDuration", value: 0 }));
        }

        window.localStorage.setItem("isShowDoneBtn", true);

        if (IsRemoveConnectCallSF()) {
            window.localStorage.removeItem('ConnectCallSF');
        }
        window.localStorage.removeItem("calltype");
        console.log("connectCallSF -> removed from localstorage inside CallDisconnect at 76", new Date());
    }

    const hangUpWfhCall = () => {
        return CALL_API({
            url: `api/SalesView/hangupCall/${User.EmployeeId}`,
            method: 'GET', service: 'MatrixCoreAPI'
        })
    }

    let _displayCallStatus = window.localStorage.getItem('callDisposition')
    useEffect(() => {
        switch (_displayCallStatus) {
            case "Call terminating...":
                CallDisconnect();
                break;
            case "Remote ringing...":
                //CallDisconnect();
                break;

            default: break;

        }

    }, [_displayCallStatus]);

    useEffect(() => {
        if (ConnectCallSF && ConnectCallSF.lead
            && ConnectCallSF.lead.LeadId !== parentLeadId) {
            dispatch(updateStateInRedux({ key: "isCalledLeadMismatched", value: true }))
        }
        else {
            dispatch(updateStateInRedux({ key: "isCalledLeadMismatched", value: false }))
        }
    }, [ConnectCallSF, dispatch, parentLeadId])

    useEffect(() => {
        if (isCalledLeadMismatched !== showLeadMismatchTooltip) {
            setShowLeadMismatchTooltip(isCalledLeadMismatched)
        }
    }, [isCalledLeadMismatched])
    const handleTooltipClose = () => {
        setShowLeadMismatchTooltip(false);
    }

    const OpenSvView = () => {

        if (ConnectCallSF === null) return;
        if (ConnectCallSF && ConnectCallSF.lead && ConnectCallSF.lead.CustomerId && ConnectCallSF.lead.ProductId && ConnectCallSF.lead.LeadId) {
            Common.OpenSalesView(ConnectCallSF.lead.CustomerId, ConnectCallSF.lead.ProductId, ConnectCallSF.lead.LeadId, 's', ConnectCallSF.lead.Reason, ConnectCallSF.lead.ReasonId)
            props.setRefreshLeadToRedux(true);
        }
        else {
            let leadid = 0;
            if (ConnectCallSF && ConnectCallSF.lead && ConnectCallSF.lead.LeadId) {
                leadid = ConnectCallSF.lead.LeadId;
            }
            else if (ConnectCallSF && ConnectCallSF.LeadID) {
                leadid = ConnectCallSF.LeadID;
            }

            if (leadid > 0) {
                GetLeadCustomerDetails(leadid).then(function (JsonData) {
                    Common.OpenSalesView(JsonData.LeadData.CustID, JsonData.LeadData.ProductID, JsonData.LeadData.LeadID);
                    gaEventTracker('LeadOpen1', JSON.stringify(ConnectCallSF), JsonData.LeadData.LeadID);
                    props.setRefreshLeadToRedux(true);
                });
            }
            else {
                gaEventTracker('LeadOpen1err', JSON.stringify(ConnectCallSF), leadid);
            }

        }
    }

    const isWfhNew = Common.isUserWfhNew(User);
    return (
        <>

            <div className="DailingIcon">
                <p>{formatSeconds(durationTime)}{(User.IsWFH || IsAswatUser()) || <span>{displayCallStatus}</span>}</p>
                {(ConnectCallSF && ConnectCallSF.lead) &&
                    <Tooltip
                        title={<>
                            <p>Currently connected to {ConnectCallSF.lead.LeadId}, Click here to switch </p>
                            <span onClick={handleTooltipClose}><Close /></span>
                        </>}
                        open={showLeadMismatchTooltip}
                        placement="bottom" arrow classes={{ tooltip: "LeadMismatchTooltip" }}
                        interactive
                    >
                        <p onClick={OpenSvView} className={isCalledLeadMismatched ? "ConnectedLeadId blinkAnimation" : "ConnectedLeadId"}>
                            <span>
                                {ConnectCallSF.lead.LeadId || ConnectCallSF.LeadID}
                            </span>
                        </p>
                    </Tooltip>
                }
                {User.IsProgressive && User.IsWFH === false && (!mute
                    ? <span className="mute" onClick={onMute}> <img src={CONFIG.PUBLIC_URL + "/images/salesview/microphone.svg"} alt="mute" /></span>
                    : <span className="mute" onClick={onMute}> <img src={CONFIG.PUBLIC_URL + "/images/salesview/mute.svg"} alt="un-Mute" /></span>)
                }
                <Dialpad />
                {!IsApptfeedbackLead() &&
                    <CallTransfer />
                }
                {/* <span className="cltransfer"> <img src="/images/salesview/calltransfer.svg" alt="transfer" /></span> */}
                <ErrorBoundary name="Cust Feedback">
                    <CustFeedback />
                </ErrorBoundary>

                {IsHangupEnable(durationTime) && User.IsWFH === false && (showHangup || IsAswatUser()) && (!Common.isUserWebphoneNew(User)) && <span className="clEnd" onClick={() => { CallDisconnect(); window.CallHangup() }}> <img src={CONFIG.PUBLIC_URL + "/images/salesview/call-end.svg"} alt="Disconnect Call" /></span>}
                {IsHangupEnable(durationTime) && ((User.IsWFH === true && isWfhNew) || (Common.isUserWebphoneNew(User))) && <span className="clEnd" onClick={() => { CallDisconnect(); hangUpWfhCall() }}> <img src={CONFIG.PUBLIC_URL + "/images/salesview/call-end.svg"} alt="Disconnect Call" /></span>}

                {/* {User.IsProgressive && User.IsWFH == false && hold ? <PhonePausedIcon titleAccess="Hold" onClick={onHold} /> : null}
                {User.IsProgressive && User.IsWFH == false && !hold ? <PhoneIcon titleAccess="Hold" onClick={onHold} /> : null} */}
                {/* {User.IsProgressive && User.IsWFH === false && mute ? <MicOffIcon titleAccess="Mute" onClick={onMute} /> : null}
                {User.IsProgressive && User.IsWFH === false && !mute ? <MicIcon titleAccess="Mute" onClick={onMute} /> : null}
                {User.IsProgressive === false ? <div className="callDailing" onClick={() => window.CallHangup()}> <PhoneInTalkIcon /> </div> : null}
                {User.IsWFH === false && showHangup ? <div className="callEnd" onClick={() => { CallDisconnect(); window.CallHangup() }}><CallEndIcon /> </div> : null}*/}
                {/* <SyncAltIcon /> */}
                {/* <MicOffIcon /> */}
                {/* <div className="callDailing" onClick={() => window.CallHangup()}>
                <PhoneInTalkIcon />
            </div> */}

            </div>
        </>
    )
}

const mapStateToProps = state => {
    return {

    };
};

const mapDispatchToProps = dispatch => {

    return {
        setRefreshLeadToRedux: (value) => dispatch(setRefreshLead({ RefreshLead: value }))
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(Dialer);
