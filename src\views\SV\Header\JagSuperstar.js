import { Description } from "@mui/icons-material";
import React from "react";
import User from "../../../services/user.service";

let BANDS = {
    // goldSup: {
    //     className: 'JagGoldSuperStar',
    //     title: '',
    //     // Description:'Jeeto Apna Ghar 2022-23'
    // },
    gold: {
        className: 'JagGoldSuperStar',
        title: 'Congratulations! You are a Gold Superstar of JAG 5.0.'
    },
    // silverSup: {
    //     className: 'JagSilverSuperStar',
    //     title: '',
    //     // Description:'Jeeto Apna Ghar 2022-23'
    // },
    silver: {
        className: 'JagSilverSuperStar',
        title: 'Congratulations! You are a Silver Superstar of JAG 5.0.',
        // Description:'Jeeto Apna Ghar 2022-23'
    },
    // hustlerSup: {
    //     className: 'JagHustlerSuperStar',
    //     title: '',
    //     // Description:'Emerging Star 2022-23'
    // },
    hustler: {
        className: 'JagHustlerSuperStar',
        title: 'Congratulations! You are a Hustler Superstar of ES 3.0.'
    },
    // warriorSup: {
    //     className: 'JagHustlerSuperStar warrior',
    //     title: '',
    //     // Description:'Emerging Star 2022-23'
    // },
    warrior: {
        className: 'JagHustlerSuperStar warrior',
        title: 'Congratulations! You are a Warrior Superstar of ES 3.0.',
        // Description:'Emerging Star 2022-23'
    }
}

const JagSuperstar = () => {
 let userBand = User.UserBand;
    // let userBand = 'silver';
 
   if (!userBand || !BANDS[userBand]) return null;
    return (
        <div className={BANDS[userBand].className}>
             <marquee direction="left">   <h3>{BANDS[userBand].title}</h3 >  </marquee>
               
            {/* <h3>{BANDS[userBand].Description}</h3> */}
        </div>
    )
}

export default JagSuperstar;