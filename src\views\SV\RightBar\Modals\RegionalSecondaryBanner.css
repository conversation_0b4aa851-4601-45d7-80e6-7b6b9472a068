.regional-banner-container {
  font-family: <PERSON><PERSON>;
  border-radius: 10px;
  position: relative;
  background-image: url("/public/images/background_regional.png");
  background-size: cover;
  max-width: 1200px;
  display: flex;
  align-items: center;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.3);
}

.regional-banner-image img {
  max-width: 100%;
  height: auto;
}

.regional-banner-content {
  text-align: center;
  flex-grow: 1;
  padding-top: 10px;
}

.regional-banner-congrats {
  width: 100%;
  max-width: 370px;
  height: auto;
  margin-bottom: 20px;
}

.regional-banner-user-name {
  font-size: 48px;
  font-weight: 500;
  color: white;
  margin-bottom: 26px;
}

.regional-banner-amount {
  font-size: 53px;
  font-weight: 800;
  color: white;
  margin: 20px 0;
}

.regional-banner-lead-id {
  font-size: 24px;
  font-weight: 400;
  color: white;
  margin-bottom: 20px;
}

.regional-banner-secondary-text {
  color: white;
  font-size: 28px;
  font-weight: 500;
  margin-bottom: 30px;
}

.regional-banner-transfer-text {
  font-size: 22px;
  font-weight: 400;
  color: white;
  margin-bottom: 30px;
}

.regional-banner-language-bar {
  width: 100%;
  max-width: 800px;
  margin-top: 10px;
}

.regional-banner-modal .MuiDialog-paperWidthSm{
  padding:0 !important;
  border-radius:37px !important;
}

.regional-banner-modal .MuiDialogContent-root { 
  padding:0 !important;
}