// SME Edit info popup
import React, { useEffect, useState } from "react";
import { SelectDropdown, TextInput } from "../../../../components";
import ModalPopup from '../../../../components/Dialogs/ModalPopup';
import { Autocomplete } from '@mui/material';
import { Button, Grid, TextField } from "@mui/material";
import masterService from "../../../../services/masterService";
import { default as Data } from "../../../../assets/json/StaticData";
import rootScopeService from "../../../../services/rootScopeService";
import { CALL_API } from "../../../../services";
import User from "../../../../services/user.service";
import { useSnackbar } from "notistack";
import { SetLeadAudit } from "../../../../services/Common";

const IsSmeHouseHold = (item) => {
    let result = false;
    try {
        //For PI and Marine (where occupancy is Household item)
        if (item.SubProduct.ID === 14 || (item.SubProduct.ID === 13 && item.Occupation.ID == 28)) {
            result = true;
        }
    }
    catch
    {

    }
    return result;
}

const getLeadDetailsByProductService = (requestData) => {
    const input = {
        url: "api/Bms/GetLeadDetailsByLeadId?LeadId=" + requestData.LeadId,
        method: 'GET',
        service: 'MatrixCoreAPI'
    }
    return CALL_API(input).then(response => {
        return response;
    });
}

const UpdateLeadDetailsService = (requestData) => {
    const input = {
        url: "api/Bms/UpdateLeadDetails",
        method: 'POST',
        service: 'MatrixCoreAPI',
        requestData: requestData.obj
    }
    return CALL_API(input).then(response => {
        return response;
    });
}

export const EditInfoPopUp = (props) => {
    const { LeadID, UpdateStatus } = props;
    const [cities, setCities] = useState([]);
    const [Occupations, setOccupations] = useState([]);
    const [PolicyTypes, setPolicyTypes] = useState([]);
    const [CoverTypes, setCoverTypes] = useState([]);
    const [SubProducts, setSubProducts] = useState([]);
    const [LeadInfo, setLeadInfo] = useState({});
    const [Originaldata, setOriginaldata] = useState({});
    const [ShowNoOfLives, setShowNoOfLives] = useState(false);
    const [ShowNoOfEmployees, setShowNoOfEmployees] = useState(false);
    const [ShowCoverType, setShowCoverType] = useState(false);
    const [Isdisabled, setIsdisabled] = useState(true);
    const ProductId = rootScopeService.getProductId();

    const { enqueueSnackbar } = useSnackbar();

    const ShowSection = (subProductID) => {
        let _showNoOfLives = false;
        let _showNoOfEmployees = false;
        let _showCoverType = false;

        if ([1, 2, 3, 4].indexOf(subProductID) !== -1) {
            _showNoOfLives = true;
            _showNoOfEmployees = true;
        }
        if ([1, 2, 3, 4].indexOf(subProductID) !== -1) {
            _showCoverType = true;
        }
        if ([19].indexOf(subProductID) !== -1) {
            _showNoOfLives = true;
        }
        setShowCoverType(_showCoverType);
        setShowNoOfEmployees(_showNoOfEmployees);
        setShowNoOfLives(_showNoOfLives);
    };
    const GetLeadDetailsByProduct = () => {
        // if ([1527, 1532].indexOf($scope.CurrentSubStatusId) != -1) {
        //    ShowEdit = true;
        // }
        // else {
        //    ShowEdit = false;
        // }

        const reqData = {
            LeadId: LeadID
        }
        getLeadDetailsByProductService(reqData)
            .then(function (response) {
                if (response) {
                    setLeadInfo(response);
                    Setdata(response);
                }
            })
            .catch(() => {

            })
    }
    const getSubProductByProductID = () => {
        setPolicyTypes(Data.PolicyType.SME);
        setCoverTypes(Data.CoverType);
        masterService.getSubProductByProductID(ProductId).then((response) => {
            setSubProducts(response);
        })
    }
    const Setdata = (response) => {
        let _leadInfo = {};

        _leadInfo.SubProduct = SubProducts.find(item => item.ID === response.InvestmentTypeID);
        _leadInfo.PolicyType = PolicyTypes.find(item => item.PolicyTypeId === response.PolicyTypeId);
        // eslint-disable-next-line eqeqeq
        _leadInfo.Occupation = Occupations.find(item => item.ID == response.OccupancyId && item.SubProductTypeId == response.InvestmentTypeID);
        // eslint-disable-next-line eqeqeq
        _leadInfo.CoverType = CoverTypes.find(item => item.Id == response.CoverTypeId);

        // for leadAudit
        _leadInfo.OccupancyName = response.OccupancyName || (_leadInfo.Occupation ? _leadInfo.Occupation.Name : '');
        _leadInfo.SubProductName = response.SubProductName || (_leadInfo.SubProduct ? _leadInfo.SubProduct.Name : '');
        _leadInfo.PolicyTypeName = response.PolicyTypeName || (_leadInfo.PolicyType ? _leadInfo.PolicyType.PolicyTypeName : '');

        if (response.CityId) {
            _leadInfo.city = cities.find(c => c.CityID === response.CityId);
        }
        if (response.State) {
            _leadInfo.City = response.City + "(" + response.State + ")";
        }
        _leadInfo.InsuredName = response.InsuredName;

        setLeadInfo({ ...response, ..._leadInfo });
        setOriginaldata({ ...response, ..._leadInfo });
        ShowSection(response.InvestmentTypeID);
    }
    const EnableDisableEdit = function (e, resetLeadInfo = true) {
        if (Isdisabled) {
            setIsdisabled(false);
        }
        else {
            if (resetLeadInfo) {
                setLeadInfo({ ...Originaldata });
            }

            setIsdisabled(true);
            ShowSection(LeadInfo.SubProduct.ID);

        }
    }

    const ValidateLeadInfo = (type) => {
        let message = "";
        let item = LeadInfo;
        if (!(item.Name && item.Name.trim())) {
            message += "Enter Name. \n";
        }

        if (IsSmeHouseHold(item)) {
            if (item.InsuredName && item.InsuredName.trim() !== "") {
                // Do nothing
            }
            else {
                message += "Enter Insured Name. \n";
            }
        }

        if (!IsSmeHouseHold(item)) {
            if (item.CompanyName && item.CompanyName.trim() !== "") {
                // Do nothing
            }
            else {
                message += "Enter Company Name. \n";
            }
        }

        if (!(item.SubProduct)) {
            message += "Enter Valid SubProduct. \n";
        }
        else if (!(item.SubProduct.ID)) {
            message += "Enter Valid SubProduct. \n";
        }

        if (!(LeadInfo.city)) {
            message += "Enter Valid City. \n";
        }

        if (ShowNoOfLives) {
            if (!item.NoOfLives
                || isNaN(Number(item.NoOfLives)) || !Number(item.NoOfLives)) {
                message += "Enter valid No of Lives. \n";
            };
        }

        if (ShowNoOfEmployees) {
            if (!item.NoOfEmployees
                || isNaN(Number(item.NoOfEmployees)) || !Number(item.NoOfEmployees)) {
                message += "Enter valid No of Employees.\n";
            };
        }
        if (ShowNoOfEmployees && ShowNoOfLives && !message) {
            if (Number(item.NoOfEmployees) > Number(item.NoOfLives)) {
                message += "No. Of Lives cannot be less than No. Of Employees.\n"
            }
        }

        if (ShowCoverType) {
            if (!item.CoverType
                || isNaN(Number(item.CoverType.Id))) {
                message += "Select CoverType.\n";
            };
        }

        if (OccupationsList && Array.isArray(OccupationsList) && OccupationsList.length > 0) {
            if (!item.Occupation) {
                message += "Select Occupation Type.\n";
            }
            else if (item.Occupation.ID === 0) {
                message += "Select Occupation Type.\n";
            }
        }

        if (!item.PolicyType) {
            message += "Select Policy Type.\n";
        }

        if (message !== "") {
            alert(message);
            return false;
        }
        if (type === "s") {
            UpdateLeadDetails(item);
        }
        else {
            UpdateStatus();
        }
    }
    const UpdateLeadDetails = function (item) {

        let OccupancyId = 0;
        let OccupancyName = "";
        if (item.Occupation) {
            OccupancyId = item.Occupation.ID;
            OccupancyName = item.Occupation.Name;
        }

        const reqData = {
            "obj": {
                Name: item.Name.trim(),
                LeadID: LeadID,
                InvestmentTypeID: item.SubProduct ? item.SubProduct.ID : 0,
                NoOfLives: item.NoOfLives,
                NoOfEmployees: item.NoOfEmployees,
                PolicyTypeId: item.PolicyType ? item.PolicyType.PolicyTypeId : 0,
                CoverTypeId: item.CoverType ? item.CoverType.Id : 0,
                OccupancyId: OccupancyId,
                SubProductName: item.SubProduct ? item.SubProduct.Name : '',
                PolicyTypeName: item.PolicyType ? item.PolicyType.PolicyTypeName : '',
                OccupancyName: OccupancyName,
                City: LeadInfo.city.CityStateName,
                CityId: LeadInfo.city.CityID,
                StateId: LeadInfo.city.StateID,
                InsuredName: !IsSmeHouseHold(item) ? item.CompanyName : LeadInfo.InsuredName,
                CompanyName: IsSmeHouseHold(item) ? item.InsuredName : LeadInfo.CompanyName
            }
        };
        UpdateLeadDetailsService(reqData).then((response) => {
            if (response) {
                if (response === 1) {
                    alert("Successfully updated.");

                    /*Log Changes*/
                    var reqDataAudit = [];
                    let vdata = Originaldata;
                    Object.keys(reqData.obj).forEach((key1) => {
                        const vdata1 = reqData.obj[key1];
                        const FieldsData = require("../../../../assets/json/FieldsData").default;
                        var fieldVal = FieldsData.EdiftInfo[key1];
                        if ((typeof vdata1 != "object") && (key1 !== "MatrixLeadId") && (vdata[key1]) && (vdata[key1] !== vdata1) && fieldVal > '') {
                            reqDataAudit.push({
                                LeadId: LeadID,
                                AgentId: User.UserId,
                                SectionName: "EditInfo",
                                Field: fieldVal,
                                OldValue: vdata[key1],
                                NewValue: vdata1,
                                ProductId: rootScopeService.getProductId()
                            });
                        }
                    });


                    if (reqDataAudit.length > 0) {
                        SetLeadAudit(reqDataAudit).then(() => {
                            enqueueSnackbar('Audit Details Saved', {
                                variant: "success",
                                autoHideDuration: 3000
                            });
                        }, function () {
                            console.log("Lead Audit not saved");
                        })
                    }
                    /*Log Changes*/

                    // rootScopeService.setLeadRefresh();
                    //dispatch(setRefreshLead({ RefreshLead: true }));
                    GetLeadDetailsByProduct();
                    EnableDisableEdit(null, false);
                }
                else {
                    alert("Unable to Update.");
                }
            }
        })
    }
    const handleChange = (e) => {
        let e_name = e.target.name;
        let e_value = e.target.value;

        //setLeadInfo({ ...LeadInfo, [e_name]: e_value });

        switch (e_name) {
            case 'SubProduct':
                setLeadInfo({ ...LeadInfo, [e_name]: e_value, Occupation: null });
                if (e_value)
                    ShowSection(e_value.ID);
                break;

            default:
                setLeadInfo({ ...LeadInfo, [e_name]: e_value });
                break;
        };

    }

    let OccupationsList = [];
    try {
        OccupationsList = (Occupations && Array.isArray(Occupations)) ? Occupations.filter((o) => o.SubProductTypeId === (LeadInfo.SubProduct ? LeadInfo.SubProduct.ID.toString() : 0)) : [];
    }
    catch {
        OccupationsList = []
    }
    useEffect(() => {
        masterService.getCities().then((cities) => {
            setCities(cities);
        });

        masterService.getSMEOccupationList().then((response) => {
            setOccupations(response);
        });

        getSubProductByProductID();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        if (Array.isArray(SubProducts) && SubProducts.length > 0
            && Array.isArray(Occupations) && Occupations.length > 0
            && Array.isArray(cities) && cities.length > 0
        ) {
            GetLeadDetailsByProduct();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [SubProducts, Occupations, cities])

    return (
        <ModalPopup open={true} handleClose={props.handleClose} className="editInfoPopup">

            <Grid container spacing={3}>
                <TextInput
                    name="Name"
                    label="Contact Person's Name"
                    handleChange={handleChange}
                    maxLength="50"
                    value={LeadInfo.Name}
                    disabled={Isdisabled}
                    sm={12} md={12} xs={12}
                />
                <TextInput
                    name="CompanyName"
                    label="Company Name"
                    handleChange={handleChange}
                    maxLength="100"
                    value={LeadInfo.CompanyName}
                    disabled={Isdisabled}
                    show={!IsSmeHouseHold(LeadInfo)}
                    sm={12} md={12} xs={12}
                />
                <TextInput
                    name="InsuredName"
                    label="Insured Name"
                    handleChange={handleChange}
                    maxLength="100"
                    value={LeadInfo.InsuredName}
                    disabled={Isdisabled}
                    show={IsSmeHouseHold(LeadInfo)}
                    sm={12} md={12} xs={12}
                />
                <SelectDropdown
                    name="SubProduct"
                    label="Sub Product"
                    value={LeadInfo.SubProduct}
                    options={SubProducts}
                    labelKeyInOptions='Name'
                    valueKeyInOptions='_all'
                    handleChange={handleChange}
                    disabled={Isdisabled}
                    sm={12} md={12} xs={12}
                />
                <TextInput
                    name="NoOfLives"
                    label="No Of Lives"
                    handleChange={handleChange}
                    maxLength="8"
                    value={LeadInfo.NoOfLives}
                    disabled={Isdisabled}
                    show={ShowNoOfLives}
                    sm={6} md={6} xs={12}
                />
                <TextInput
                    name="NoOfEmployees"
                    label="No Of Employees"
                    handleChange={handleChange}
                    maxLength="8"
                    value={LeadInfo.NoOfEmployees}
                    disabled={Isdisabled}
                    show={ShowNoOfEmployees}
                    sm={6} md={6} xs={12}
                />

                <SelectDropdown
                    name="Occupation"
                    label="Occupancy"
                    value={LeadInfo.Occupation}
                    options={OccupationsList}
                    labelKeyInOptions='Name'
                    valueKeyInOptions='_all'
                    handleChange={handleChange}
                    disabled={Isdisabled}
                    sm={12} md={12} xs={12}
                />
                <SelectDropdown
                    name="PolicyType"
                    label="Policy Type"
                    value={LeadInfo.PolicyType}
                    options={PolicyTypes}
                    labelKeyInOptions='PolicyTypeName'
                    valueKeyInOptions='_all'
                    handleChange={handleChange}
                    disabled={Isdisabled}
                    sm={12} md={12} xs={12}
                />
                <SelectDropdown
                    name="CoverType"
                    label="Cover Type"
                    value={LeadInfo.CoverType}
                    options={CoverTypes}
                    labelKeyInOptions='Name'
                    valueKeyInOptions='_all'
                    handleChange={handleChange}
                    disabled={Isdisabled}
                    show={ShowCoverType}
                    sm={6} md={4} xs={12}
                />
                <Grid item sm={12} md={12} xs={12}>
                    <Autocomplete
                        onChange={(event, value) => handleChange({ target: { name: 'city', value } })}
                        options={cities}
                        name="City"
                        value={LeadInfo.city || null}
                        getOptionLabel={(option) => (option.CityStateName || '')}
                        style={{ width: "100%" }}
                        renderInput={(params) =>
                            <TextField {...params}
                                label="Enter City"
                                variant='outlined' />}
                        disabled={Isdisabled}
                    />
                </Grid>
            </Grid>
            <Grid container spacing={3}>
                {Isdisabled
                    ? <>
                        <Grid item xs={12} sm={6}>
                            <Button
                                variant="contained"
                                color="secondary"
                                className="editBtn"
                                fullWidth
                                onClick={EnableDisableEdit}
                            >
                                Edit
                            </Button>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <Button
                                variant="contained"
                                color="secondary"
                                className="submitBtn"
                                fullWidth
                                onClick={() => ValidateLeadInfo('u')}
                            >
                                Update Status
                            </Button>
                        </Grid>
                    </>
                    : <>
                        <Grid item xs={12} sm={6}>

                            <Button variant="text"
                                onClick={EnableDisableEdit}
                                fullWidth
                            >
                                Cancel
                            </Button>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <Button
                                variant="contained"
                                color="secondary"
                                className="submitBtn"
                                fullWidth
                                onClick={() => ValidateLeadInfo('s')}
                            >
                                Save
                            </Button>
                        </Grid>
                    </>}

            </Grid>
        </ModalPopup>
    )
}
