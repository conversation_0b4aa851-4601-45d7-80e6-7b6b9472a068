import { Grid } from "@mui/material";
import { ExpandMore } from "@mui/icons-material";
import React, { useState } from "react";
import { useSelector } from "react-redux";


const CustomerImportantInfo = () => {
    const [show, setShow] = useState(true);
    const CustomerProfileData = useSelector((state) => state.salesview.CustomerProfileData)

    const handleToggle = () => {
        setShow(!show);
    }

    // useEffect(() => {
    //     if (RefreshLead) {
    //         setShow(false);
    //     }
    // }, [RefreshLead]);
    if (!(CustomerProfileData &&
        (CustomerProfileData.payTermSelected || CustomerProfileData.planSelected)
    )) return null;
    return <>
        <Grid item sm={12} md={12} xs={12}>
            <div className="customerImportantInfo">
                <h3>Customer Selection</h3>

                <div className="expandmoreIcon">
                    <ExpandMore onClick={handleToggle} style={{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)' }} /></div>
                {show &&
                    <>
                        <ul>
                            {CustomerProfileData.payTermSelected && <li>{CustomerProfileData.payTermSelected}</li>}
                            {CustomerProfileData.planSelected && CustomerProfileData.planSelected.toLowerCase() === "return of premium" && <li>{CustomerProfileData.planSelected}</li>}
                        </ul>
                    </>
                }

            </div>
        </Grid>


    </>
}
export default CustomerImportantInfo;
