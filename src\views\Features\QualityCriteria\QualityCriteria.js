import { Accordion, AccordionDetails, AccordionSummary, Typography } from "@mui/material";
import { ExpandMore } from "@mui/icons-material";
import React, { useEffect, useState } from "react";
import { CONFIG } from "../../../appconfig";
import { QualityCriteriasData } from "./QualityCriteriasData";
import './QualityCriteria.scss';
export default function QualityCriteria(props) {
    // const [productId, setProductId] = useState(0);
    const [qualityData, setQualityData] = useState([]);

    useEffect(() => {
        let reqIndex = CONFIG.PUBLIC_URL ? 4 : 2;
        let routeParams = window.location.pathname.split('/');
        if (!routeParams) {
            return 0;
        }
        let ProductId = parseInt((routeParams[reqIndex]).split('/')[0])
        // setProductId(ProductId);
        if (ProductId === 1000) {
            ProductId = 7;
        }
        setQualityData(QualityCriteriasData[ProductId]);
    }, []);

    return (
        <div className="QualityCriteria">
            {Array.isArray(qualityData) &&
                qualityData.map((data, i) => {
                    return (
                        <>
                            {data.heading && <h3 className="DisclosuresOption">{data.heading}</h3>}
                            <Accordion key={i}>
                                <AccordionSummary
                                    expandIcon={<ExpandMore />}
                                >
                                    <img src={CONFIG.PUBLIC_URL + data.img} alt="img" />  <Typography className="title"> {data.title}</Typography>
                                </AccordionSummary>
                                <AccordionDetails className="qualityDescription">
                                    <div className="details">
                                        {data.data.map((subData, j) => (
                                            <div key={j} className="bgBlue">
                                                <p className="subParameter">Sub-Parameter</p>
                                                <div className="Q-Description">Quality Description</div>
                                                <p>{subData.subParameter}</p>
                                                <ul>
                                                    {subData.qualityDescription.map((descriptionText, k) => (
                                                        <li key={k}>{descriptionText}</li>
                                                    ))}
                                                </ul>
                                            </div>
                                        ))}
                                    </div>
                                </AccordionDetails>
                            </Accordion>
                        </>
                    )
                })
            }

        </div>
    );
}
