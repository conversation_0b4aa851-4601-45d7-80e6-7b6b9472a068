import { But<PERSON>, Grid } from "@mui/material";
import { ExpandMore } from "@mui/icons-material";
import { LinearProgress } from "@mui/material";
import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import dayjs from "dayjs";

import rootScopeService from "../../../services/rootScopeService";
import { gaEventTracker } from "../../../helpers";
import User from "../../../services/user.service";
import {
  getCustomersRelationshipManager,
  AssignRelationshipManagerBeforeIssuance,
  GetAlreadyAssignedRMDetails,
  GetRMCommentsService,
  getCustomersRelationshipManagerTerm
} from "../../../services/Common";
import { ViewAllCommentsPopup } from "../Main/Modals/ViewAllCommentsPopup";
import { ChkIsRenewal } from '../../../../src/helpers/index';

const RMDetails = () => {
  const [show, setShow] = useState(false);
  const [rmresult, setrmresult] = useState();
  let [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);
  const [isTicketDataLoading, setIsTicketDataLoading] = useState(false);
  const [apifailed, setapifailed] = useState(false);
  let [ShowAssignRM, setShowAssignRM] = useState(false);
  let [isRMAssigned, setisRMAssigned] = useState(false);
  let [ShowRMComments, setShowRMComments] = useState(false);
  let [RMEmployeeId, setRMEmployeeId] = useState('');
  let [ViewAllComments, setViewAllComments] = useState(false);
  let [RMComments, setRMComments] = useState([]);

  let [IsRenewal, allLeads] = useSelector((state) => {
    let { IsRenewal, allLeads } = state.salesview;
    return [IsRenewal, allLeads];
  });
  
  let [parentLeadId] = useSelector(({ salesview }) => [salesview.parentLeadId]);
  let leadIds = useSelector(state =>  state.salesview.leadIds).split(';').join(',').slice(0, -1);

  const handleToggle = (customerId) => {
    setShow(!show);
    setrmresult(null);
    if (show == false) {
      if( rootScopeService.getProductId() === 2 && IsRenewal ) { 
        gaEventTracker("Health_Renewal","RM_Details",User.UserId)
      }
      getCustomersRelationship(customerId);
    }
  };

  const getCustomersRelationship = (customerId) => {
    setIsTicketDataLoading(true);
    setapifailed(false);
    let statusId  = 0;
    if(Array.isArray(allLeads)){
      let leadsTemp = allLeads.find(lead => [13,37,39,55,76].indexOf(lead.StatusId) > -1)
      if(leadsTemp){
        statusId = leadsTemp.StatusId;
      }
    }
    if([7,115].indexOf(rootScopeService.getProductId()) > -1 && [13,37,39,55,76].indexOf(statusId) > -1)
    {
      getCustomersRelationshipManagerTerm(rootScopeService.getCustomerId(),rootScopeService.getLeadId())
      .then((result) => {
        if(result && result.RMEmployeeID){
          setrmresult(result);
        }
      }).finally(() =>{
        setIsTicketDataLoading(false);
      })
    }
    else{
      getCustomersRelationshipManager(customerId)
      .then((result) => {
        if (result && result.RMEmployeeID) {
          setrmresult(result);
          if(rootScopeService.getProductId() == 2 && ChkIsRenewal(allLeads))
           //if(rootScopeService.getProductId() == 2)
          {
            setRMEmployeeId(result.RMEmployeeID);
            setShowRMComments(true);
          }
        } else {
          if ([2].includes(rootScopeService.getProductId())) {
            if (Array.isArray(allLeads) && allLeads.length > 0) {
              const isNotHealthFresh = allLeads.find(
                (o) => o.LeadSource.toLowerCase() === "renewal");
              if (!isNotHealthFresh) {
                GetAlreadyAssignedRMDetails(
                  customerId,
                  parentLeadId,
                  rootScopeService.getProductId()
                ).then((rma) => {
                  if (rma.RMEmployeeID) {
                    setrmresult(rma);
                  } else {
                    const isBooked = allLeads.find((o) => o.StatusId >= 13);
                    const isInterested = allLeads.find(
                      (o) => [4, 11].indexOf(o.StatusId) !== -1
                    );
                    if (!isBooked && isInterested) {
                      setShowAssignRM(true);
                    }
                  }
                });
              }
            }
          }
        }
      })
      .catch((e) => {
        setapifailed(true);
        console.log(e);
      })
      .finally(() => {
        setIsTicketDataLoading(false);
      });
    }
  };

  const AssignRelationshipManager = (customerId) => {
    AssignRelationshipManagerBeforeIssuance(
      customerId,
      parentLeadId,
      rootScopeService.getProductId()
    ).then((res) => {
      if (res) {
        setisRMAssigned(true);
        setrmresult(res);
      }
    });
  };

  const GetRMComments = () => {
    setRMComments([]);
    GetRMCommentsService(leadIds,RMEmployeeId).then((result) => {
        if (Array.isArray(result)) {
          setRMComments(result);
        }
      setViewAllComments(true);
    })
};
  let viewAllComments = ((Array.isArray(RMComments) && RMComments.length >0) ?
    (RMComments.map((commentData, index) => {
      return <div className="comment-text">
          <p>{commentData.EmployeeId}{commentData.LeadId ? ' - ' + commentData.LeadId : ''}</p>
          <small>{dayjs(commentData.CreatedOn).format('DD/MM/YYYY h:mm:ss a')}</small>
          <p className="commentmsg">{commentData.Comments}</p>
      </div>}))
      :
       <div className="comment-text">No Data Found</div>
  );


  useEffect(() => {
    if (RefreshLead) {
      setShow(false);
    }
  }, [RefreshLead]);

  if (
    rootScopeService.getProductId() != 2 &&
    rootScopeService.getProductId() != 7 &&
    rootScopeService.getProductId() != 115
  ) {
    return null;
  }
  return (
    <>
      <Grid item sm={12} md={12} xs={12}>
        <div className="Rmdetails">
          <h3>RM Details</h3>

          <div className="expandmoreIcon">
            <ExpandMore
              onClick={() => handleToggle(rootScopeService.getCustomerId())}
              style={{ transform: show ? "rotate(180deg)" : "rotate(0deg)" }}
            />
          </div>
          {isTicketDataLoading && <LinearProgress />}
          {show && !isTicketDataLoading && (
            <>
              {rmresult && rmresult.RMEmployeeID ? (
                <>
                  <p>RMEmployeeID - {rmresult.RMEmployeeID}</p>
                  <p>RMName - {rmresult.RMName} </p>
                  <p>
                    RMMobileNo -{" "}
                    {!!rmresult.RMMobileNo ? rmresult.RMMobileNo : ""}
                  </p>
                </>
              ) : (
                <>
                  {apifailed ? (
                    <Button
                      variant="text"
                      onClick={() =>
                        getCustomersRelationship(
                          rootScopeService.getCustomerId()
                        )
                      }
                    >
                      Click here to retry
                    </Button>
                  ) : (
                    <>{!ShowAssignRM && <p>No Details</p>} </>
                  )}
                </>
              )}
              {ShowRMComments === true && (
                <>
                      <div className="text-center">
                        <Button
                          className="AssignRMBtn"
                          onClick={() =>
                            GetRMComments()
                          }
                          variant="contained"
                        >
                          Get RM Comments
                        </Button>
                      </div>
                    </>
              )}
              {ShowAssignRM === true && (
                <>
                  {isRMAssigned === false ? (
                    <>
                      <div className="text-center">
                        <Button
                          className="AssignRMBtn"
                          onClick={() =>
                            AssignRelationshipManager(
                              rootScopeService.getCustomerId()
                            )
                          }
                          variant="contained"
                        >
                          Assign RM
                        </Button>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="text-center">
                        <Button
                          variant="contained"
                          className="AssignRMBtnDisble"
                          disabled
                        >
                          RM Assigned
                        </Button>
                      </div>
                    </>
                  )}
                </>
              )}
            </>
          )}
        </div>
        <ViewAllCommentsPopup open={ViewAllComments} handleClose={() => { setViewAllComments(false) }} viewAllComments={viewAllComments} />
      </Grid>
    </>
  );
};
export default RMDetails;
