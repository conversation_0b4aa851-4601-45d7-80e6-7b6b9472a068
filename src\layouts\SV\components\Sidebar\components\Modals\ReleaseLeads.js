
import { Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@mui/material";
import ModalPopup from "../../../../../../components/Dialogs/ModalPopup";
import { SV_CONFIG } from "../../../../../../appconfig";
import { CallReleaseService } from "../../helper/sidebarHelper";
import User from "../../../../../../services/user.service";
import { useSnackbar } from "notistack";
import { Card, CardContent, Typography, List, ListItem, ListItemIcon, ListItemText } from "@mui/material";
import { CheckCircle, Info } from "@mui/icons-material";

export const ReleaseLeads = (props) => {
    let { TodayReleaseLeadCount, TodayReleaseLeadCountTill } = props;
    const { enqueueSnackbar } = useSnackbar();

    let rows = props.data !== undefined ? props.data : [];
    let [TodayReleaseLeadMax, setTodayReleaseLeadMax] = useState(0);
    // Todo userprod
    //  var UserProdSession = JSON.parse(window.sessionStorage.getItem('UserMainProdId'));
    //  if (UserProdSession != undefined && UserProdSession != null)
    //  $scope.UserProd = UserProdSession.ProdId;
    let [UserProd, setUserProd] = useState(0);

    useEffect(() => {
        if (props.open) {
            let usergrp = User.ProductList;
            let prod = 0;
            if (Array.isArray(usergrp)) {
                for (const item of usergrp) {
                    if ([115, 1001, 7, 1000, 139, 2, 117, 114, 3, 195, 101, 106, 118, 130, 131, 147].indexOf(item.ProductId) !== -1) {
                        prod = item.ProductId;
                        break;
                    }
                }
            }
            setUserProd(prod);

            if ([115, 1001, 7, 1000, 139, 2, 117, 114, 3, 195, 101, 106, 118, 130, 131, 147].indexOf(prod) !== -1) {
                setTodayReleaseLeadMax(SV_CONFIG.TodayReleaseLeadMax[SV_CONFIG.environment])
            }
        }
    }, [props.open])

    const ReleaseLead = (leadid) => {
        let Releasecount = TodayReleaseLeadCountTill + 1;

        var totalRequests = (Releasecount);
        if (totalRequests > TodayReleaseLeadMax) {
            alert("You can only request for (" + (TodayReleaseLeadMax).toString() + ") Leads for today.");
            return false;
        }
        CallReleaseService(leadid).then((result) => {

            if (result != null && !result.status) {
                enqueueSnackbar(result.message, {
                    variant: 'error',
                    autoHideDuration: 3000,
                });
            }
            else {
                enqueueSnackbar("Call Restriction Released Successfully", {
                    variant: 'success',
                    autoHideDuration: 3000,
                });
                props.GetReleaseLeads()
                props.setTodayReleaseLeadCount(Releasecount)

            }

        });

    }

    const ReleaseValidation = (updatedrows) => {
        let Releasecount = TodayReleaseLeadCountTill;
        let CheckedLeads = [];
        //TodayReleaseLeadCount = TodayReleaseLeadCountTill;

        if (updatedrows.length == 0) {
            alert("Please select a lead.");
            return false;
        }

        for (let i = 0; i < updatedrows.length; i++) {
            if (updatedrows[i].selected === true && Releasecount < TodayReleaseLeadMax) {
                CheckedLeads.push(updatedrows[i].LeadID);
                //TodayReleaseLeadCount = TodayReleaseLeadCount + 1;
                Releasecount = Releasecount + 1;
            }
        }

        props.setTodayReleaseLeadCount(Releasecount)
        var totalRequests = (Releasecount);
        if (totalRequests > TodayReleaseLeadMax) {
            alert("You can only request for (" + (TodayReleaseLeadMax).toString() + ") Leads for today.");
            return false;
        }
    };

    const check = (event, row) => {

        if (row.Status === 0) {

            const updatedrows = rows.map((item) => item.LeadID === row.LeadID ?
                ({ ...item, selected: event.target.checked }) : item)

            if (Array.isArray(updatedrows)) {
                props.setReleaseLeadsData(updatedrows);
            }
            ReleaseValidation(updatedrows)
        }

    }

    return (
        <ModalPopup open={props.open} title={[115, 1001, 7, 1000, 139, 2, 117, 114, 3, 195, 101, 106, 118, 130, 131, 147].indexOf(UserProd) > -1 ? `Today you released leads ${TodayReleaseLeadCount === undefined ? 0 : TodayReleaseLeadCount} out of ${TodayReleaseLeadMax}.`
            : `Weekly / Today attempt exhausted.`} handleClose={props.handleClose} className="addmoreLeadPopup ReleaseLeadPopup">
                <div className="CallReleaseCriteria">
                    <Typography variant="h5" gutterBottom>
                        Call Release Eligibility Criteria
                    </Typography>
                    <ul>
                        <li>After 4 Unanswered attempts on a lead</li>
                        <li>Weekly attempt on a lead should be exhausted.</li>
                    </ul>
                  
                    <Typography variant="body2">
                     <strong> Note: </strong> Customer level attempts will be checked before releasing the lead.
                    </Typography>
                    </div>
            
            {rows.length === 0 ? <h3 className="nodataFound">No Data Found</h3> :
                <TableContainer component={Paper} className="reassigned-table">
                    <Table aria-label="simple table" >
                        <TableHead>
                            <TableRow>
                                <TableCell align="left">Sno</TableCell>
                                <TableCell align="left">LeadId</TableCell>
                                <TableCell align="left">Name</TableCell>
                                <TableCell align="left">Lead Status</TableCell>
                                <TableCell align="left">Request Status</TableCell>
                                {/* {<TableCell align="left">Expiry Date</TableCell>} */}
                                <TableCell align="left">Release</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {rows.map((row, i) => (
                                <TableRow key={i} className="moreDeatilsData">
                                    <TableCell align="left">{i + 1}</TableCell>
                                    <TableCell align="left">{row.LeadID ? row.LeadID : ""}</TableCell>
                                    <TableCell align="left">{row.CustName ? row.CustName : ""}</TableCell>
                                    <TableCell align="left">{row.LeadStatus ? row.LeadStatus : ""}</TableCell>
                                    <TableCell align="left">{row.Status ? row.Status : ""}</TableCell>
                                    {/* {<TableCell align="left">
                                        {row.ExpiryDate !== undefined ?
                                            dayjs(SV_CONFIG && SV_CONFIG.UseReleaseLeadCommApi ? JsonToNormalDate(row.ExpiryDate) : row.ExpiryDate).format('DD/MM/YYYY h:mm a') : ""}
                                    </TableCell>} */}
                                    {/* <input type="checkbox" checked={checked} onClick={() => setChecked(!checked)} /> */}
                                    {/* <input type="checkbox" checked={checked} onClick={() => toggleCheckbox(e, row.LeadID)} /> */}
                                    {/* {row.Status==0 && <input type="checkbox"  onChange={()=>{ReleaseValidation(row.selected, row.LeadID)}}/>} */}
                                    <TableCell align="left">
                                        {
                                            ([115, 1001, 7, 1000, 139, 2, 117, 114, 3, 195, 101, 106, 118, 130, 131, 147].indexOf(UserProd) > -1) ?
                                                <Button color="info" size="small" onClick={() => ReleaseLead(row.LeadID)}> Release </Button> : null
                                        }
                                    </TableCell>

                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            }

        </ModalPopup>
    )
};




