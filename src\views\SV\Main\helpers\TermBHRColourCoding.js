//#E6695F - Red
//#ffbf00 - Amber
//#00B222 - Green
export const TermBHRBoxColour = {
  "APE_Salaried": {
    "ranges": {
      "#E6695F": [0, 0.4200],
      "#ffbf00": [0.4201, 0.5900],
      "#00B222": [0.5901, 1]
    }
  },
  "APE SelfEmployeed": {
    "ranges": {
      "#E6695F": [0, 0.2950],
      "#ffbf00": [0.2951, 0.4400],
      "#00B222": [0.4401, 1]
    }
  },
  "Bookings": {
    "ranges": {
      "#E6695F": [0, 0.3800],
      "#ffbf00": [0.3801, 0.5800],
      "#00B222": [0.5801, 1]
    }
  },
  "Dedicated FOS": {
    "ranges": {
      "#E6695F": [0, 0.3750],
      "#ffbf00": [0.3751, 0.5200],
      "#00B222": [0.5201, 1]
    }
  },
  "HDFC IPRU HNI": {
    "ranges": {
      "#E6695F": [0, 0.5000],
      "#ffbf00": [0.5001, 0.6300],
      "#00B222": [0.6301, 1]
    }
  },
  "Inbound Birthday": {
    "ranges": {
      "#E6695F": [0, 0.5050],
      "#ffbf00": [0.5051, 0.6350],
      "#00B222": [0.6351, 1]
    }
  },
  "NRI": {
    "ranges": {
      "#E6695F": [0, 0.4770],
      "#ffbf00": [0.4771, 0.5960],
      "#00B222": [0.5961, 1]
    }
  },
  "Retainer": {
    "ranges": {
      "#E6695F": [0, 0.3750],
      "#ffbf00": [0.3751, 0.5400],
      "#00B222": [0.5401, 1]
    }
  }
};  
export const TermBHRBGColour = {
  "APE_Salaried": {
    "ranges": {
      "#FFEFEF": [0, 0.4200],
      "#FFFBEA": [0.4201, 0.5900],
      "#EDFFEA": [0.5901, 1]
    }
  },
  "APE SelfEmployeed": {
    "ranges": {
      "#FFEFEF": [0, 0.2950],
      "#FFFBEA": [0.2951, 0.4400],
      "#EDFFEA": [0.4401, 1]
    }
  },
  "Bookings": {
    "ranges": {
      "#FFEFEF": [0, 0.3800],
      "#FFFBEA": [0.3801, 0.5800],
      "#EDFFEA": [0.5801, 1]
    }
  },
  "Dedicated FOS": {
    "ranges": {
      "#FFEFEF": [0, 0.3750],
      "#FFFBEA": [0.3751, 0.5200],
      "#EDFFEA": [0.5201, 1]
    }
  },
  "HDFC IPRU HNI": {
    "ranges": {
      "#FFEFEF": [0, 0.5000],
      "#FFFBEA": [0.5001, 0.6300],
      "#EDFFEA": [0.6301, 1]
    }
  },
  "Inbound Birthday": {
    "ranges": {
      "#FFEFEF": [0, 0.5050],
      "#FFFBEA": [0.5051, 0.6350],
      "#EDFFEA": [0.6351, 1]
    }
  },
  "NRI": {
    "ranges": {
      "#FFEFEF": [0, 0.4770],
      "#FFFBEA": [0.4771, 0.5960],
      "#EDFFEA": [0.5961, 1]
    }
  },
  "Retainer": {
    "ranges": {
      "#FFEFEF": [0, 0.3750],
      "#FFFBEA": [0.3751, 0.5400],
      "#EDFFEA": [0.5401, 1]
    }
  }
};  
