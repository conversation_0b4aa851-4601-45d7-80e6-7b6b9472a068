/* eslint-disable react-hooks/exhaustive-deps */
import {
    <PERSON><PERSON>,
    <PERSON>lapse,
    Drawer,
    List,
    ListItem,
    ListItemIcon,
    ListItemSecondaryAction,
    ListItemText,
} from '@mui/material';
import makeStyles from '@mui/styles/makeStyles';
import { ExpandLess, ExpandMore } from '@mui/icons-material';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { CONFIG, SV_CONFIG } from '../../../../appconfig';
import { setIsIncentive, updateStateInRedux } from '../../../../store/actions/SalesView/SalesView';
import AgentInfo from '../../../../views/SV/Main/AgentInfo';
import MenuOpenIcon from '@mui/icons-material/MenuOpen';
import { useSnackbar } from 'notistack';
import User from '../../../../services/user.service';
import { GetAssignedLeadsService, GetExpiryLeadsService, GetImportantLeadsService, GetReassignedLeadsService, GetRejectedLeadsService, GetReleaseLeadsCountService, GetReleaseLeadsService, isAgentDashboardVisible, IsHealthRenewalAgent, OpenAgentDashboard, OpenInternalEmailINNewTab, ShowCreateLeads, ShowDuplicateLeads, ShowLeadSummary, ShowMyBookings, ShowNewMyBookings, ShowPGView, ShowQcReport, ShowSOSBookings, ShowSOSBookingsDesk, IsPaymentOverdueVisible, IsPaymentAttemptDashboardVisible, ShowPaymentAttemptsDashboard, OpenPBQuiz, GetFosAgentAssignedLeads,OpenPBSchool, GetRejectedLeadsBookedService, OpenLeaveManagement, ShowDuplicateLeadsNew } from './helper/sidebarHelper';
import { CALL_API } from '../../../../services';
import rootScopeService from '../../../../services/rootScopeService';
import { ErrorBoundary } from '../../../../hoc';
import TodayLeads from './components/Modals/TodayLeads';
import { ImportantLeads } from './components/Modals/ImportantLeads';
import { ReassignedLeads } from './components/Modals/ReassignedLeads';
import { RejectedLeads } from './components/Modals/RejectedLeads';
import { ReleaseLeads } from './components/Modals/ReleaseLeads';
import NCLeadPopup from './components/Modals/NCLeadPopup';
import { ExpiryLeads } from './components/Modals/ExpiryLeads';
import AnalyticsDashboard from './components/Modals/AnalyticsDashboard';
import NotificationsPopup from './components/Modals/NotificationsPopup';
import { useInterval } from '../../../../views/SV/Main/helpers/useInterval';
import { DocListingPopUp } from '../../../../views/SV/Main/Modals/DocListingPopUp';
import { QualityCriteriaPopUp } from './components/Modals/QualityCriteriaPopUp';
import { CallbackTracker } from './components/Modals/CallbackTracker';
import { FOSAgentDashboard } from './components/Modals/FOSAgentDashboard';
import { CustomerIssuesDashboard } from './components/Modals/CustomerIssuesDashboard';
import { CsatRatingPopUp } from './components/Modals/CsatRatingPopUp';
import { CallGalleryPopUp } from './components/Modals/CallGalleryPopUp';
import BMSInternalEmailPopup from '../../../../views/SV/RightBar/Modals/BMSInternalEmailPopup';
import { CommonModalPopUp } from '../../../../views/SV/Main/Modals/CommonModalPopUp';
import { getMoodleUrlService, IsApptfeedbackLead, IsCustomerAccess } from '../../../../services/Common';
import { HealthRenewalFaq } from './components/Modals/HealthRenewalFaq';
import { GetIframeURL } from "../../../../../src/services/api.service";
import { CustomerListing } from './components/Modals/CustomerListing';
import RejectedLeadsBooked from './components/Modals/RejectedLeadsBooked';
import {localStorageCache} from '../../../../../src/utils/utility'; 
import BookingCreditLog from './components/Modals/BookingCreditLog';
import { gaEventNames, gaEventTracker } from '../../../../helpers';
import { getMongoNotificationData, removeExtraCookies } from '../../../../helpers/commonHelper';
import { ReleaseLeadsV1 } from './components/Modals/ReleaseLeadsV1';
import SearchLead from './components/Modals/SearchLead';
import RolloverPanel from './components/Modals/RolloverPanel';
// import { RejectedLeadsBooked } from './components/Modals/RejectedLeadsBooked';


const SpeedResult = (props) => {
    let { speed } = props
    return (
        <div className="speedTestResult">{speed}</div>
    )
}


const useStyles = makeStyles((theme) => ({
    root: {
        width: '100%',
        maxWidth: 360,
        backgroundColor: theme.palette.background.paper,
    },
    nested: {
        padding: '0px 0px 0px 18px !important',
    },
}));
const getInitials = (string) => {
    let split = string.split(' ');
    split = split.map((s) => s[0].toUpperCase());
    return split.join('');
}

export default function SidebarDrawer(props) {


    const classes = useStyles();
    let isSidebarDrawerOpen = useSelector(state => state.salesview.isSidebarDrawerOpen);
    let openMenus = useSelector(state => state.salesview.sidebarDrawerOpenMenus);

    const dispatch = useDispatch();

    const toggleDrawer = (open = false) => (event) => {
        if (event.type === 'keydown' && (event.key === 'Tab' || event.key === 'Shift')) {
            return;
        }
        dispatch(updateStateInRedux({ key: "isSidebarDrawerOpen", value: open }))
    };

    const toggleMenu = (MenuTitle) => {
        let indexOfMenuTitle = openMenus.indexOf(MenuTitle);
        let updatedOpenMenus = indexOfMenuTitle === -1
            ? [...openMenus, MenuTitle]
            : openMenus.filter((m) => m !== MenuTitle);

        dispatch(updateStateInRedux({
            key: "sidebarDrawerOpenMenus",
            value: updatedOpenMenus
        }));
    }
    const [rejectedLeadloader, setRejectedLeadloader] = useState(false);

    const [txtSpeedtest, settxtSpeedtest] = useState('');

    const [currentPopup, setCurrentPopup] = useState(null);
    const [autoPopup, setAutoPopup] = useState(null);
    const [AssignedLeads, setAssignedLeads] = useState([]);
    const [FosAssignedLeads, setFosAssignedLeads] = useState([]);
    const [ImportantLeadsData, setImportantLeadsData] = useState([]);
    const [ExpiryLeadsdata, setExpiryLeadsdata] = useState([]);
    const [ReassignedLeadsData, setReassignedLeadsData] = useState([]);
    const [RejectedLeadsData, setRejectedLeadsData] = useState([]);
    const [ReleaseLeadsData, setReleaseLeadsData] = useState([]);
    const [IsVisibleReleaseChat, setIsVisibleReleaseChat] = useState(false);
    const [IsVisibleDuplicateLead, setIsVisibleDuplicateLead] = useState(false);
    const [IsVisibleBookingDesk, setIsVisibleBookingDesk] = useState(false);
    const [IsVisibleQualityCriterias, setIsVisibleQualityCriterias] = useState(false);
    const [IsCallGalleryVisible, setIsCallGalleryVisible] = useState(false);
    const [RejectedLeadsBookedData, setRejectedLeadsBookedData] = useState([]);

    const [nonProgressiveOrNonOneLead, setNonProgressiveOrNonOneLead] = useState(false);

    const [visibleMyBooking, setVisibleMyBooking] = useState(false);
    const [visibleoldBooking, setVisibleoldBooking] = useState(true);
    const [TodayReleaseLeadCount, setTodayReleaseLeadCount] = useState(0);
    const [TodayReleaseLeadCountTill, setTodayReleaseLeadCountTill] = useState(0);
    const { enqueueSnackbar, closeSnackbar } = useSnackbar();

    const [internetNotWorkingSnackbarKey, _setInternetNotWorkingSnackbarKey] = useState(null);
    const [userProduct, setUserProduct] = useState(null);
    const internetNotWorkingSnackbarKeyRef = React.useRef(internetNotWorkingSnackbarKey);
    const setInternetNotWorkingSnackbarKey = data => {
        internetNotWorkingSnackbarKeyRef.current = data;
        _setInternetNotWorkingSnackbarKey(data);
    };
    const IspriorityUser = rootScopeService.getPriority();
    const [NoOfDays, setNoOfDays] = useState(30);
    let notificationDatafromRedux = useSelector(state => state.salesview.notificationData);
    let [IsIncentive, ShowAnalyticsDashboardPopup] = useSelector(({ salesview }) => [salesview.IsIncentive, salesview.ShowAnalyticsDashboardPopup]);
    let MongoNotification = getMongoNotificationData();
    let unreadNotifCount = Array.isArray(MongoNotification) ? 
        MongoNotification.filter(item => item.IsRead !== true).length 
        : 0;
    let version = 1;

    try {
        const _date = new Date(parseInt(CONFIG.VERSION));
        version = `v:${_date.getDate()}-${_date.getHours()}.${_date.getMinutes()}`;
    }
    catch { }
    //     const [container, setContainer] = useState(null);
    //     const newWindow = useRef(null);

    //     useEffect(() => {
    //       // Create container element on client-side
    //       setContainer(document.createElement("div"));
    //     }, []);

    //     useEffect(() => {
    //       // When container is ready
    //       if (container) {
    //         // Create window
    //         newWindow.current = window.open(
    //           "",
    //           "",
    //           "width=600,height=400,left=200,top=200"
    //         );
    //         // Append container
    //         newWindow.current.document.body.appendChild(container);

    //         // Save reference to window for cleanup
    //         const curWindow = newWindow.current;

    //         // Return cleanup function
    //         return () => curWindow.close();
    //       }
    //     }, [container]);

    //     return container && createPortal(props.children, container);
    //   };

    const OpenURLinNewWindow = (key) => {
        const url = GetIframeURL(key);
        window.open(url, "_blank");
    }
    const handlePopupOpen = (name) => {
        if(name && name == 'QualityCriteriaPopUp'){
            gaEventTracker(gaEventNames.QualityCriteria, User.EmployeeId, rootScopeService.getLeadId());
        }
        if(name && name == 'DocListingPopUp'){
            gaEventTracker(gaEventNames.DocListing, User.EmployeeId, rootScopeService.getLeadId());
        }
        setCurrentPopup(name);
    };

    const openScriptsDirectory = () => {
        var url = SV_CONFIG["ScriptDirectory"];
        window.open(url, "_blank");
    }
    let NewTab = OpenInternalEmailINNewTab();

    const IsIncentiveAllowed = () => {
        var url = SV_CONFIG["IncentiveAPIPublic"][SV_CONFIG["environment"]] + "Jag/GetVisibleMenus/" + btoa(User.UserId);
        const input = {
            url: url,
            method: 'GET', service: 'custom',
            Header: {
                'Access-Control-Allow-Credentials': true
            },
            withCredentials: true
        };
        CALL_API(input).then((resultdata) => {
            if (resultdata != null && resultdata.Response != null) {
                let obj = JSON.parse(resultdata.Response)[0];
                if (obj.Incentive || obj.JAG) {
                    dispatch(setIsIncentive({ IsIncentive: 2 }));
                }
                else {
                    dispatch(setIsIncentive({ IsIncentive: 0 }));
                }
            }
        });
    }
    const checkIsVisibleDuplicateLead = () => {
        let UserGroup = User.UserGroupList;
        if (UserGroup && UserGroup.length > 0) {
            let UserGroupId = UserGroup[0].GroupId;
            if (SV_CONFIG.showDuplicateLeadsGrps[SV_CONFIG.environment].indexOf(UserGroupId) > -1) {
                setIsVisibleDuplicateLead(true);
                return;
            }
        }
        setIsVisibleDuplicateLead(false);
    }
    const checkIsVisibleReleaseChat = () => {
        let UserGroup = User.UserGroupList;
        if (UserGroup && UserGroup.length > 0) {
            let UserGroupId = UserGroup[0].GroupId;
            if (SV_CONFIG.showReleaseChatGrps[SV_CONFIG.environment].indexOf(UserGroupId) > -1) {
                setIsVisibleReleaseChat(true);
                return;
            }
        }
        setIsVisibleReleaseChat(false);
    }
    const isQualityCriteriasVisible = () => {

        // let PrdGroupList = User.PrdGroupList;
        // if (Array.isArray(PrdGroupList) && PrdGroupList.length > 0) {
        //     if ([2, 7, 1000, 115, 117].includes(PrdGroupList[0].ProductId)) {
        //         setIsVisibleQualityCriterias(true);
        //         setUserProduct(PrdGroupList[0].ProductId);
        //     }
        // }
        if (User.RoleId === 13) {
            var usergrp = User.ProductList;

            usergrp.forEach(function (item, key) {
                if ([7, 1000, 115, 2, 117].indexOf(item.ProductId) > -1) {
                    setIsVisibleQualityCriterias(true);
                    setUserProduct(item.ProductId);
                }
            });
        }
    }

    const IsBookingCreditVisible = () => {

        if (User.RoleId === 13) {
            var usergrp = User.ProductList;
            usergrp.forEach(function (item, key) {
                if ([2,7,1000,115,1001,117,131].indexOf(item.ProductId) > -1) {
                    return true
                }
            });
        }else{return false}
    }
    const checkCallGalleryVisiblity = () => {

        // let PrdGroupList = User.PrdGroupList;
        // if (Array.isArray(PrdGroupList) && PrdGroupList.length > 0) {
        //     if ([2, 7, 1000, 115, 117].includes(PrdGroupList[0].ProductId)) {
        //         setIsVisibleQualityCriterias(true);
        //         setUserProduct(PrdGroupList[0].ProductId);
        //     }
        // }
        // if (User.RoleId === 13) {
        let usergrp = User.ProductList;

        Array.isArray(usergrp) && usergrp.forEach(function (item, key) {
            if ([7, 1000, 2].indexOf(item.ProductId) > -1) {
                setIsCallGalleryVisible(true);
                setUserProduct(item.ProductId);
            }
        });
        // }
    }

    const isSMEQuoteLeadVisible = () => {
        var result = false;
        if (User.RoleId === 13) {
            let usergrp = User.ProductList;

            Array.isArray(usergrp) && usergrp.forEach(function (item, key) {
                if ([131].indexOf(item.ProductId) > -1) {
                    result = true;
                }
            });
        }
        return result;
    }
    const IsInvIncentiveVisible = () => {
        var result = false;
        if (User.RoleId === 13) {
            let usergrp = User.ProductList;

            Array.isArray(usergrp) && usergrp.forEach(function (item, key) {
                if ([115].indexOf(item.ProductId) > -1) {
                    result = true;
                }
            });
        }
        return result;
    }


    const enableBookingDesk = () => {
        //var UserGroup = rootScopeService.getUserGroup()
        //if (UserGroup && UserGroup.length > 0) {
        //    var UserGroupId = UserGroup[0].GroupId;
        //    if (AppConfig.BMSGroupId.indexOf(UserGroupId) > -1) {
        //        return true;
        //    }
        //}
        //return false;
        // if (User && User.IsSOSGroup === true) {
        //     setIsVisibleBookingDesk(true);
        // } else {
            setIsVisibleBookingDesk(false);
        //}
    }
    const GetAssignedLeads = () => {
        GetAssignedLeadsService().then((result) => {
            if (result) {
                setAssignedLeads(result);
            }
        });
    }
    const GetFosAssignedLeads = (NoOfDays) => {
        GetFosAgentAssignedLeads(NoOfDays).then((result) => {
            if (result) {
                setFosAssignedLeads(result);
            }
        });
    }
    const GetReassignedLeads = () => {
        GetReassignedLeadsService().then((result) => {
            if (result) {
                setReassignedLeadsData(result);
            }
        });
    }
    const GetRejectedLeads = () => {
        setRejectedLeadloader(true);
        GetRejectedLeadsService().then((result) => {
            if (Array.isArray(result)) {
                setRejectedLeadsData(result);
            } else { setRejectedLeadsData([]); }
        }).finally(() => {
            setRejectedLeadloader(false);
        });
    }
    const GetRejectedLeadsBooked = () => {
        GetRejectedLeadsBookedService().then((result) => {
            if (Array.isArray(result)) {
                setRejectedLeadsBookedData(result);
            } else { setRejectedLeadsBookedData([]); }
        });
    }
    const GetReleaseLeads = () => {
        GetReleaseLeadsService().then((result) => {
            if (Array.isArray(result)) {
                setReleaseLeadsData(result);
            } else {
                setReleaseLeadsData([]);
            }
        });
        GetReleaseLeadsCountService().then((result) => {
            if (result) {
                setTodayReleaseLeadCount(parseInt(result));
                setTodayReleaseLeadCountTill(parseInt(result));
            }
            else {
                setTodayReleaseLeadCount(0);
                setTodayReleaseLeadCountTill(0);
            }
        });

    }
    const GetImportantLeads = () => {
        GetImportantLeadsService().then((result) => {
            if (result) {
                setImportantLeadsData(result);
            }
        });
    }
    
    const GetExpiryLeads = () => {
        GetExpiryLeadsService().then((result) => {
            if (result) {
                setExpiryLeadsdata(result);
            }
        });
    }

    const OpenfeedBackurl = () => {
        var cid = btoa(User.UserId);
        var secretToken = window.localStorage.getItem('AsteriskToken');
        window.open(SV_CONFIG["feedback"][SV_CONFIG["environment"]] + "/Landing.html#/matrix/LandingPage/" + secretToken + "/matrix/" + cid, "_blank")
    }

    const ShowMyQuiz = () => {
        var input = {
            url: 'https://mobilematrix.policybazaar.com/PGV/Quiz.aspx/CheckPendingQuiz',
            method: 'POST',
            service: 'custom',
            requestData: {}
        };
        CALL_API(input).then((resp) => {
            if (resp.d) {
                window.open('/PGV/Quiz.aspx?Quiz=yes', '_self');
            } else {
                enqueueSnackbar("You don't have any pending quiz.", {
                    variant: 'error',
                    autoHideDuration: 3000,
                });
            }
        });
    }
    const OpenPBSupport = () => {
        window.open('https://pbsupport.policybazaar.com/', '_blank');
    }

    const TollFreeNumber = () => {
        try {
            gaEventTracker(gaEventNames.PBHelplineNumber, User.EmployeeId, rootScopeService.getLeadId());
            window.open('https://docs.google.com/spreadsheets/d/1prv6OSvzUk2GoOFXIrJWMcEuom5d0wrHf_QKmhauNC8/edit?usp=sharing', '_blank');
        }
        catch (e) {
            enqueueSnackbar("Unable to open", {
                variant: 'error',
                autoHideDuration: 3000,
            });
        }
    }

    const getMoodleUrl = () => {
        // getMoodleUrlService().then((result) => {
        //     if (result && result.hasOwnProperty('getMoodleUrlResult')) {
        //         let url = result.getMoodleUrlResult.Message;
        //         if (url && url.includes && url.includes('https://')) {
        //             window.open(url, '_blank');
        //         }
        //         else {
        //             enqueueSnackbar("Something went wrong!", {
        //                 variant: 'error',
        //                 autoHideDuration: 3000,
        //             });
        //         }
        //     }
        //     else {
        //         enqueueSnackbar("No Url Found", {
        //             variant: 'error',
        //             autoHideDuration: 3000,
        //         });
        //     }
        // }).catch(() => {
        //     enqueueSnackbar("Unable to open LMS, Please try again", {
        //         variant: 'error',
        //         autoHideDuration: 3000,
        //     });
        // });
    }
    function checkinternetSpeed() {
        //JUST AN EXAMPLE, PLEASE USE YOUR OWN PICTURE!
        let file = CONFIG.PUBLIC_URL + "/images/salesview/31120037-5mb.jpg";
        var downloadSize = 4995374; //bytes
        var startTime, endTime;
        settxtSpeedtest('Checking...');
        var download = new Image();
        download.onload = function () {
            endTime = (new Date()).getTime();
            showResults();
        }

        download.onerror = function (err, msg) {
            // $(".offline").show();
        }

        startTime = (new Date()).getTime();
        var cacheBuster = "?nnn=" + startTime;
        download.src = file + cacheBuster;

        function showResults() {
            var duration = (endTime - startTime) / 1000;
            var bitsLoaded = downloadSize * 8;
            var speedBps = (bitsLoaded / duration).toFixed(2);
            var speedKbps = (speedBps / 1024).toFixed(2);
            var speedMbps = (speedKbps / 1024).toFixed(2);
            var actualHTML = speedMbps + " Mbps";
            settxtSpeedtest(actualHTML);

        }
    }
    function doesConnectionExist() {
        var xhr = new XMLHttpRequest();
        var file = CONFIG.PUBLIC_URL + "/images/salesview/favicon.ico";
        var randomNum = Math.round(Math.random() * 10000);

        xhr.open('HEAD', file + "?rand=" + randomNum, true);

        function processRequest(e) {

            if (xhr.readyState === 4) {
                if (xhr.status >= 200 && xhr.status < 304) {
                    if (internetNotWorkingSnackbarKey) {
                        closeSnackbar(internetNotWorkingSnackbarKey);
                        setInternetNotWorkingSnackbarKey(null);
                    }
                } else {
                    if (!internetNotWorkingSnackbarKey) {
                        const key = enqueueSnackbar(
                            xhr.status >= 400 ? 
                            `Something went wrong ${xhr.status ? `(${xhr.status})` : ''}` 
                            : `Internet is not working${xhr.status ? `(${xhr.status})...` : '...'}`, {
                            variant: "warning",
                            persist: true,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'center',
                            },
                        })
                        setInternetNotWorkingSnackbarKey(key);
                    }
                }
            }
        }
        xhr.onreadystatechange = processRequest;
        // xhr.addEventListener("readystatechange", processRequest, false);
        xhr.send();
    }

    useInterval(function () {
        doesConnectionExist();
    }, 5000);

    useEffect(function () {
        if(ShowAnalyticsDashboardPopup){        
        setAutoPopup('AnalyticsDashboard');
        }
    }, [ShowAnalyticsDashboardPopup]);

    const ShowMyIncentive = () => {
        if(IsIncentive === 2 && IsInvIncentiveVisible()){
            var url = SV_CONFIG["InvIncentiveDashboadURL"][SV_CONFIG["environment"]];
            window.open(url, '_blank');
        }
        else if (IsIncentive === 2) {
            var url = SV_CONFIG["Incentiveurl"][SV_CONFIG["environment"]] + btoa(User.UserId);
            window.open(url);
        }
        else {
            window.open('../Incentive/TermSI.aspx?Source=SV&ProductId=' + rootScopeService.getProductId(), '_blank');
        }
    }

    useEffect(() => {
        checkIsVisibleDuplicateLead();
        checkIsVisibleReleaseChat();
        enableBookingDesk();
        IsIncentiveAllowed();
        isQualityCriteriasVisible();
        checkCallGalleryVisiblity();
        visibleMyNewBooking();
        visibleMyOldBooking();
        let isOneLead = window.localStorage.getItem('isOneLead')
        if (isOneLead && User) {
            setNonProgressiveOrNonOneLead(!(isOneLead === "true" && User.IsProgressive));
        }
    }, [])

    const visibleMyNewBooking = () => {
        if (User.RoleId === 13) {
            let product = User.ProductList;
            let bookingProduct = (window && window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.MyBookingsProducts) || SV_CONFIG.MyBookingsProducts
            if (Array.isArray(bookingProduct)) {
                Array.isArray(product) && product.forEach(function (item, key) {
                    if (bookingProduct.indexOf(item.ProductId) > -1) {
                        setVisibleMyBooking(true);
                    }
                });
            }
        }
    }

    const visibleMyOldBooking = () => {
        if (User.RoleId === 13) {
            // let Groups = User.GroupList;
            // let bookingGroups = (window && window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.OldBookingsGroups) || SV_CONFIG.OldBookingsGroups
            // if(Array.isArray(bookingGroups)){
            //     Array.isArray(Groups) && Groups.forEach(function (item, key) {
            //         if (bookingGroups.indexOf(item.GroupId) > -1) {
            //             setVisibleoldBooking(false);
            //         }
            //     });
            // }
            let Products = User.ProductList;
            let OldMyBookingsProducts = (window && window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.OldMyBookingsProducts) || SV_CONFIG.OldMyBookingsProducts
            if (Array.isArray(OldMyBookingsProducts)) {
                Array.isArray(Products) && Products.forEach(function (item, key) {
                    if (OldMyBookingsProducts.indexOf(item.ProductId) > -1) {
                        setVisibleoldBooking(false);
                    }
                });
            }
        }
    }

    const showHWOpportunity = () => {
        const show = User.RoleId === 13 && SV_CONFIG && Array.isArray(User.ProductList) && User.ProductList.length > 0 && Array.isArray(SV_CONFIG['ShowHWOpportunityPrdct'])
            && User.ProductList.some(product => SV_CONFIG['ShowHWOpportunityPrdct'].includes(product.ProductId))
    
        return show;
    }

    const showSearchLead = () => {
        const show = SV_CONFIG && !SV_CONFIG["HideSearchLeadPanel"] && User.RoleId === 13 && Array.isArray(User.ProductList) && User.ProductList.length > 0 && User.ProductList.some(product => product.ProductId == 2) && User.IsRenewal

        return show;
    }

    const showRolloverLead = () => {
        const show = SV_CONFIG && !SV_CONFIG["HideRolloverPanel"] && User.RoleId === 13 && Array.isArray(User.ProductList) && User.ProductList.length > 0 && User.ProductList.some(product => product.ProductId == 2) && User.IsRenewal
        return show;
    }

    const openInNewTab = url => {
        window.open(url, "_blank");
    };

    const ShowViewData = () => {
        const show = SV_CONFIG && SV_CONFIG["ShowViewDataPanel"] && Array.isArray(SV_CONFIG["ShowViewDataPanel"]) && SV_CONFIG["ShowViewDataPanel"].length > 0 && SV_CONFIG["ShowViewDataPanel"].includes(User.UserId);
        return show;
    }

    let CustomerAccessMenuList = [
        {
            title: IsApptfeedbackLead() ? "Leads": "Cases",
            icon: <img alt="Leads" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/lead.svg"} />,
            // show: IspriorityUser,
            show: true,
            subMenu: [
                { title: IsApptfeedbackLead() ? "Assigned Leads":"Cases Raised", onClick: () => { handlePopupOpen("CustomerListing"); GetFosAssignedLeads(NoOfDays) } },
            ]
        }
    ]

    let menuList = [
        {
            title: "My Bookings",
            // show : window && window.SV_CONFIG_UNCACHED && Array.isArray(window.SV_CONFIG_UNCACHED.MyBookingsGroup) && (window.SV_CONFIG_UNCACHED.MyBookingsGroup.indexOf(User.GroupId) > -1),
            // show : visibleMyBooking,
            icon: <img alt="myBooking" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/mybookingIcon.svg"} />,
            onClick: ShowNewMyBookings
        },
        {
            title: "Dashboard",
            icon: <img alt="Dashboard" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/dashboard.svg"} />,
            onClick: () => { OpenAgentDashboard() },
            show: (isAgentDashboardVisible() > 0),
            // subMenu: [
            //     //{ title: "3 Days NC Lead", icon: "3NC", onClick: () => { handlePopupOpen("NCLeadPopup"); GetNotContactedLeads(3) } },
            //     //{ title: "7 Days NC Lead", icon: "7NC", onClick: () => { handlePopupOpen("NCLeadPopup"); GetNotContactedLeads(7) } },
            //     { title: "EMI Pending", onClick: () => { handlePopupOpen("PaymentFailedLeads"); GetPaymentFailedData() } },
            // ]
        },
        {
            title: "Vista (My Leaves)",
            show: SV_CONFIG["ShowRms"],
            icon: <Badge badgeContent="New" color="secondary" className="NewBadge"></Badge>,
            onClick: OpenLeaveManagement
        },
        {
            title: "Leads",
            icon: <img alt="Leads" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/lead.svg"} />,
            // show: IspriorityUser,
            show: true,
            subMenu: [
                { 
                    title: "Search Lead", 
                    show: showSearchLead(),
                    onClick: () => { handlePopupOpen("SearchLead");}
                },
                { 
                    title: "Rollover Leads Status", 
                    show: showRolloverLead(),
                    onClick: () => { handlePopupOpen("RolloverPanel");}
                },
                { title: "Today's Leads", onClick: () => { handlePopupOpen("TodayLeads"); GetAssignedLeads() } },
                { title: "Important Leads", onClick: () => { handlePopupOpen("ImportantLeads"); GetImportantLeads(); } },
                { title: "Re-Assigned Leads", onClick: () => { handlePopupOpen("ReassignedLeads"); GetReassignedLeads(); } },
                { title: "Rejected Leads", onClick: () => { handlePopupOpen("RejectedLeads"); GetRejectedLeads(); } },
                { title: "Rejected Leads (Booked)",
                  show: rootScopeService.getProductId() === 2,
                  icon: "RL",
                  onClick: () => { handlePopupOpen("RejectedLeadsBooked"); GetRejectedLeadsBooked(); } 
                },
                {
                    //title: rootScopeService.getProductId() === 115 && User.RoleId === 13 ? 'Release Leads' : 'Exhausted Attempts',
                    title: "Call Release",
                    onClick: () => { 
                        if(SV_CONFIG.useOldReleaseLeads){
                            handlePopupOpen("ReleaseLeadsV1"); 
                        }
                        else{
                            handlePopupOpen("ReleaseLeads"); 
                        }
                        GetReleaseLeads(); 
                    }
                },
                { 
                    title: "Spouse Opportunity",
                    show: showHWOpportunity(),
                    icon: <Badge badgeContent="New" color="secondary" className="NewBadge"></Badge>,
                    onClick: () => { handlePopupOpen("HWOpportunity"); } 
                },
                { title: "My Tickets", onClick: () => { handlePopupOpen("CustomerIssuesDashboard"); } },
                {
                    title: "Expiry Leads",
                    show: rootScopeService.getProductId() === 117,
                    onClick: () => { handlePopupOpen("ExpiryLeads"); GetExpiryLeads() }
                },
                {
                    title: "Duplicate Leads",
                    show: IsVisibleDuplicateLead,
                    onClick: ShowDuplicateLeadsNew
                },
                {
                    title: "Release Chat",
                    show: nonProgressiveOrNonOneLead && IsVisibleReleaseChat,
                    onClick: ShowDuplicateLeads
                },
                { title: "Lead Summary", onClick: ShowLeadSummary },
                {
                    title: "Create Lead",
                    show: User.UserId === 2899,
                    onClick: ShowCreateLeads
                },
                { title: "Callback Tracker", onClick: () => { handlePopupOpen("CallbackTracker"); } },
                // { title: "FOS Leads", show: User.RoleId === 13, onClick: () => { handlePopupOpen("FOSAgentDashboard"); } },
                { title: "Untouched Leads", onClick: () => { handlePopupOpen("NCLeadPopup"); } },
                { title: "View Data",show: ShowViewData(), onClick: () => { openInNewTab('../ViewTabularData'); } },
                {
                    title: "PG Details",
                    //show: [7, 1000].includes(rootScopeService.getProductId()),
                    onClick: ShowPGView
                },
                // { title: "EMI Pending", onClick: () => { handlePopupOpen("PaymentFailedLeads"); GetPaymentFailedData() } },
                // {
                //     title: "My Pod Leads",
                //     show: [1, 2, 11, 12, 21, 22, 31, 32, 41, 42, 51, 52, 61, 62, 91, 92, 3, 4, 13, 14, 23, 24, 33, 34, 43, 44, 53, 54, 63, 64, 93, 94].indexOf(User.Grade) > -1,
                //     onClick: () => { handlePopupOpen("MyPodLeads"); }
                // },
                {
                    title: "Payment Attempts Dashboard",
                    show: IsPaymentAttemptDashboardVisible(),
                    icon: <Badge badgeContent="New" color="secondary" className="NewBadge"></Badge>,
                    onClick: ShowPaymentAttemptsDashboard
                },
                // {
                //     title: "Payment Overdue",
                //     show: Array.isArray(SV_CONFIG.PaymentFailedCases) && SV_CONFIG.PaymentFailedCases.indexOf(User.UserId) > -1,
                //     onClick: () => { OpenURLinNewWindow("PaymentFailedCases");}
                // },
                // {
                //     title: "My Appointments",
                //     show: User.RoleId === 13,
                //     onClick: () => { handlePopupOpen("MyAppointments"); }
                // },
                {
                    title: "SME Quote Leads",
                    show: (isSMEQuoteLeadVisible()),//rootScopeService.getProductId() === 131 && User.RoleId === 13,
                    onClick: () => { handlePopupOpen("SMEQuoteLeads"); }
                },
                {
                    title: "Cross-Sell / Referral Leads",
                    show: User.RoleId === 13,
                    onClick: () => { handlePopupOpen("AgentCrossSellLead"); }
                },
                {
                    title: "Moved Bookings",
                    show: IsBookingCreditVisible() ,
                    onClick: ()=>{handlePopupOpen("BookingCreditLog");}
                },


            ]
        },
        {
            title: "FOS",
            icon: <img alt="FOS" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/fosIcon.svg"} className="fosIcon" />,
            show: User.RoleId === 13,

            subMenu: [
                { title: "FOS Leads", show: User.RoleId === 13, onClick: () => { handlePopupOpen("FOSAgentDashboard"); } },
                // {
                //     title: "My Appointments",
                //     show: User.RoleId === 13,
                //     onClick: () => { handlePopupOpen("MyAppointments"); }
                // },             
            ]
        },
        {
            title: "Payment Overdue",
            icon: <img alt="PaymentOverdue" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/PaymentOverdueV2.svg"} className="fosIconPayOverdue" />,
            show: IsPaymentOverdueVisible(),
            onClick: () => { OpenURLinNewWindow("PaymentFailedCases"); }
        },
        // {
        //     title: "Needs Attention",
        //     icon: <img alt="Needs Attention" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/NeedsAttention.svg"} />,
        //     subMenu: [
        //         //{ title: "3 Days NC Lead", icon: "3NC", onClick: () => { handlePopupOpen("NCLeadPopup"); GetNotContactedLeads(3) } },
        //         //{ title: "7 Days NC Lead", icon: "7NC", onClick: () => { handlePopupOpen("NCLeadPopup"); GetNotContactedLeads(7) } },
        //         { title: "EMI Pending", onClick: () => { handlePopupOpen("PaymentFailedLeads"); GetPaymentFailedData() } },
        //     ]
        // },
        {
            title: "Performance",
            icon: <img alt="Performance" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/Performance.svg"} />,
            // show: IspriorityUser,
            show: true,
            subMenu: [
                { title: "Analytics Dashboard", onClick: () => { handlePopupOpen("AnalyticsDashboard"); } },

                { title: "QC Report", onClick: ShowQcReport },
                {
                    title: "My Rewards",
                    show: (IsIncentive > 0),
                    onClick: ShowMyIncentive
                },
                // {

                //     title: "My Bookings",
                //     show: visibleoldBooking,
                //     //show : window && window.SV_CONFIG_UNCACHED && Array.isArray(window.SV_CONFIG_UNCACHED.MyBookingsGroup) && (window.SV_CONFIG_UNCACHED.MyBookingsGroup.indexOf(User.GroupId) == -1),
                //     //icon: <Badge badgeContent="Old" color="secondary" className="NewBadge"></Badge>,
                //     onClick: ShowMyBookings
                // },
                {
                    title: "SOS Bookings",
                    show: User.IsSOSAgent,
                    onClick: ShowSOSBookings
                },
                {
                    title: "CSAT Rating",
                    onClick: () => { handlePopupOpen("CsatRatingPopUp"); },
                },
                {
                    title: "Booking Desk",
                    show: IsVisibleBookingDesk,
                    onClick: ShowSOSBookingsDesk
                },
                {
                    title: "PB Quiz",
                    onClick: OpenPBQuiz
                },
                {
                    title: "PB School",
                    icon: <Badge badgeContent="New" color="secondary" className="NewBadge"></Badge>,
                    onClick: OpenPBSchool
                },

            ]
        },
        {
            title: "More",
            icon: <img alt="More" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/more.svg"} />,
            // show: IspriorityUser,
            show: true,
            subMenu: [
                //speed test
                // { title: "PB LMS", onClick: getMoodleUrl },
                { title: "PB Toll Free Number", onClick: TollFreeNumber },
                {
                    title: "My Quiz",
                    show: nonProgressiveOrNonOneLead,
                    onClick: ShowMyQuiz
                },
                { title: "Speed Test", extraInfo: <SpeedResult speed={txtSpeedtest} />, onClick: checkinternetSpeed },
                { title: "PB Support", onClick: OpenPBSupport },
                { title: "Feedback", onClick: OpenfeedBackurl },
                {
                    title: "Doc Listing",
                    show: User.IsSOSGroup,
                    onClick: () => { handlePopupOpen("DocListingPopUp"); },
                },
                {
                    title: "Quality Criteria",
                    show: IsVisibleQualityCriterias,
                    onClick: () => { handlePopupOpen("QualityCriteriaPopUp"); },
                },
                {
                    title: "FAQ",
                    show: IsHealthRenewalAgent(),
                    onClick: () => { handlePopupOpen("HealthRenewalFaq"); },
                },
                { 
                    title: SV_CONFIG.ScriptMenuName || "Scripts",
                    show: SV_CONFIG.ShowScriptsMenu,
                    onClick: openScriptsDirectory 
                },
                // {
                //     title: "Call Gallery",
                //     show: IsCallGalleryVisible,
                //     onClick: () => { handlePopupOpen("CallGalleryPopUp"); },
                // },
            ]
        },
        // { title: "Analytics Dashboard", show: !IspriorityUser, onClick: () => { handlePopupOpen("AnalyticsDashboard"); } },
        // { title: "Speed Test", show: !IspriorityUser, extraInfo: <SpeedResult speed={txtSpeedtest} />, onClick: checkinternetSpeed },
        // { title: "PB Support", show: !IspriorityUser, onClick: OpenPBSupport },
        // { title: "Feedback", show: !IspriorityUser, onClick: OpenfeedBackurl },
        // { title: "PB LMS", show: !IspriorityUser, onClick: getMoodleUrl },

        {
            title: "Notifications",
            icon: <img alt="Notifications" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/notification.svg"} />,
            onClick: () => { handlePopupOpen("NotificationsPopup"); },
            extraInfo: <div className="notification-count">{unreadNotifCount}</div>
        },
        {
            title: "Internal Email",
            href: "#",
            icon: <img alt="Internal Email" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/InternalEmail.svg"} />,
            show: true,
            onClick: () => { handlePopupOpen("BMSInternalEmailPopup"); }
        },
        {
            title: "Logout",
            icon: <img alt="Logout" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/logout.svg"} />,
            onClick: props.fnLogout,
            show: IspriorityUser
        }

    ]

    const list = () => {
        menuList = (!IsCustomerAccess()) ? menuList : CustomerAccessMenuList;
        return <List className="leftmenuList">
            {menuList.map((menuItem) => {
                if (menuItem.show === false) {
                    return null;
                }
                let isMenuOpen = openMenus.includes(menuItem.title);
                let isSubMenuAvailable = Array.isArray(menuItem.subMenu);
                if (!menuItem.show && menuItem.show !== undefined) return null
                return <React.Fragment key={menuItem.title}>
                    <ListItem button onClick={menuItem.onClick ? menuItem.onClick : () => { toggleMenu(menuItem.title) }} className={isMenuOpen ? "active" : ""}>
                        <ListItemIcon>
                            {menuItem.icon || getInitials(menuItem.title)}
                        </ListItemIcon>
                        <ListItemText primary={menuItem.title} className="menuTitle" />
                        {menuItem.extraInfo &&
                            < ListItemSecondaryAction >
                                {menuItem.extraInfo}
                            </ListItemSecondaryAction>
                        }
                        {isSubMenuAvailable && (isMenuOpen ? <ExpandLess /> : <ExpandMore />)}
                    </ListItem>
                    {
                        isSubMenuAvailable &&
                        menuItem.subMenu.map((subMenuItem) => {

                            if (!subMenuItem.show && subMenuItem.show !== undefined) return null
                            return <Collapse in={isMenuOpen} timeout="auto" unmountOnExit key={subMenuItem.title}>
                                <List component="div" disablePadding>
                                    <ListItem button onClick={subMenuItem.onClick} className={classes.nested}>
                                        <ListItemIcon>
                                            {subMenuItem.icon || getInitials(subMenuItem.title)}
                                        </ListItemIcon>
                                        <ListItemText primary={subMenuItem.title} />
                                        {subMenuItem.extraInfo &&
                                            < ListItemSecondaryAction >
                                                {subMenuItem.extraInfo}
                                            </ListItemSecondaryAction>
                                        }
                                    </ListItem>
                                </List>
                            </Collapse>
                        })
                    }
                </React.Fragment>
            })}
        </List >

    };
    let anchor = "left";
    return (
        <>
        <ErrorBoundary>
            <div>

                <React.Fragment key={anchor}>
                    <Drawer anchor={anchor} open={isSidebarDrawerOpen} onClose={toggleDrawer(false)} className="leftbarMenu">
                        {/* hamburger icon */}
                        <MenuOpenIcon className="menuOpenIcon" onClick={toggleDrawer(false)} />
                        <AgentInfo isSideBarMenuOpen={true} />
                        {list()}
                        <em onClick={removeExtraCookies} style={{ fontSize: 9, fontWeight: 600, margin: "auto" }}>{version}</em>
                    </Drawer>
                </React.Fragment>
            </div>
            <ErrorBoundary name="SearchLead">
                <SearchLead open={currentPopup === "SearchLead"} handleClose={() => { setCurrentPopup(null) }}/>
            </ErrorBoundary>
            <ErrorBoundary name="RolloverPanel">
                <RolloverPanel open={currentPopup === "RolloverPanel"} handleClose={() => { setCurrentPopup(null) }}/>
            </ErrorBoundary>
            <ErrorBoundary name="TodayLeads">
                <TodayLeads open={currentPopup === "TodayLeads"} handleClose={() => { setCurrentPopup(null) }}
                    data={(AssignedLeads && AssignedLeads.length > 0) ? AssignedLeads : []}
                />
            </ErrorBoundary>
            <ErrorBoundary name="ImportantLeads">
                <ImportantLeads open={currentPopup === "ImportantLeads"} handleClose={() => { setCurrentPopup(null) }}
                    data={(ImportantLeadsData && ImportantLeadsData.length > 0) ? ImportantLeadsData : []}
                />
            </ErrorBoundary>
            <ErrorBoundary name="ReassignedLeads">
                <ReassignedLeads open={currentPopup === "ReassignedLeads"} handleClose={() => { setCurrentPopup(null) }}
                    data={(ReassignedLeadsData && ReassignedLeadsData.length > 0) ? ReassignedLeadsData : []}
                />
            </ErrorBoundary>
            <ErrorBoundary name="RejectedLeads">
                <RejectedLeads open={currentPopup === "RejectedLeads"} handleClose={() => { setCurrentPopup(null) }}
                    rejectedLeadloader={rejectedLeadloader} data={(RejectedLeadsData && RejectedLeadsData.length > 0) ? RejectedLeadsData : []}
                />
            </ErrorBoundary>
            <ErrorBoundary name="RejectedLeadsBooked">
                <RejectedLeadsBooked open={currentPopup === "RejectedLeadsBooked"} handleClose={() => { setCurrentPopup(null) }}
                    data={(RejectedLeadsBookedData && RejectedLeadsBookedData.length > 0) ? RejectedLeadsBookedData : []}
                />
            </ErrorBoundary>
            <ErrorBoundary name="ReleaseLeads">
                <ReleaseLeads open={currentPopup === "ReleaseLeads"} handleClose={() => { setCurrentPopup(null) }}
                    data={(ReleaseLeadsData && ReleaseLeadsData.length > 0) ? ReleaseLeadsData : []} setReleaseLeadsData={setReleaseLeadsData}
                    TodayReleaseLeadCount={TodayReleaseLeadCount} setTodayReleaseLeadCount={setTodayReleaseLeadCount}
                    TodayReleaseLeadCountTill={TodayReleaseLeadCountTill} setTodayReleaseLeadCountTill={setTodayReleaseLeadCountTill}
                    GetReleaseLeads={GetReleaseLeads}
                />
                <ReleaseLeadsV1 open={currentPopup === "ReleaseLeadsV1"} handleClose={() => { setCurrentPopup(null) }}
                    data={(ReleaseLeadsData && ReleaseLeadsData.length > 0) ? ReleaseLeadsData : []} setReleaseLeadsData={setReleaseLeadsData}
                    TodayReleaseLeadCount={TodayReleaseLeadCount} setTodayReleaseLeadCount={setTodayReleaseLeadCount}
                    TodayReleaseLeadCountTill={TodayReleaseLeadCountTill} setTodayReleaseLeadCountTill={setTodayReleaseLeadCountTill}
                    GetReleaseLeads={GetReleaseLeads}/>
            </ErrorBoundary>
            <ErrorBoundary name="HWOpportunity">
                <CommonModalPopUp style={{ width: '100%', height: '100%' }} className="HWOpportunity" open={currentPopup === "HWOpportunity"} URL={SV_CONFIG["matrixdashboard"] ? `${SV_CONFIG["matrixdashboard"][SV_CONFIG["environment"]]}/admin/SpouseOpportunity` : null} handleClose={() => { setCurrentPopup(null) }} />
            </ErrorBoundary>
            <ErrorBoundary name="CustomerIssuesDashboard">
                <CustomerIssuesDashboard open={currentPopup === "CustomerIssuesDashboard"} handleClose={() => { setCurrentPopup(null) }} />
            </ErrorBoundary>
            <ErrorBoundary name="NCLeadPopup">
                <NCLeadPopup open={currentPopup === "NCLeadPopup"} handleClose={() => { setCurrentPopup(null) }}
                />
            </ErrorBoundary>
            <ErrorBoundary name="ExpiryLeads">
                <ExpiryLeads open={currentPopup === "ExpiryLeads"} handleClose={() => { setCurrentPopup(null) }}
                    data={(ExpiryLeadsdata && ExpiryLeadsdata.length > 0) ? ExpiryLeadsdata : []}
                />
            </ErrorBoundary>
            {/* <ErrorBoundary name="PaymentFailedCases">
                <PaymentFailedCases open={currentPopup === "PaymentFailedCases"} handleClose={() => { setCurrentPopup(null) }}
                    // data={(PaymentFailedCasesData && PaymentFailedCasesData.length > 0) ? PaymentFailedCasesData : []}
                />
            </ErrorBoundary> */}
            <ErrorBoundary name="CallbackTracker">
                <CallbackTracker open={currentPopup === "CallbackTracker"} handleClose={() => { setCurrentPopup(null) }} />
            </ErrorBoundary>
            <ErrorBoundary name="FOSAgentDashboard">
                <FOSAgentDashboard open={currentPopup === "FOSAgentDashboard"} handleClose={() => { setCurrentPopup(null) }} />
            </ErrorBoundary>
            <ErrorBoundary name="AnalyticsDashboard">
                <AnalyticsDashboard open={currentPopup === "AnalyticsDashboard" || autoPopup === "AnalyticsDashboard"} handleClose={() => { 
                setCurrentPopup(null); 
                setAutoPopup(null) ; 
                dispatch(updateStateInRedux({ key: 'ShowAnalyticsDashboardPopup', value: false }));
            }} />
            </ErrorBoundary>
            <ErrorBoundary name="NotificationsPopup">
                <NotificationsPopup open={currentPopup === "NotificationsPopup"} handleClose={() => { setCurrentPopup(null) }} />
            </ErrorBoundary>
            <ErrorBoundary name="BMSInternalEmailPopup">
                <BMSInternalEmailPopup open={currentPopup === "BMSInternalEmailPopup"} IsNewTab={NewTab} handleClose={() => { setCurrentPopup(null) }} />
            </ErrorBoundary>
            <ErrorBoundary name="DocListingPopUp">
                <DocListingPopUp open={currentPopup === "DocListingPopUp"} handleClose={() => { setCurrentPopup(null) }} />
            </ErrorBoundary>
            <ErrorBoundary name="QualityCriteriaPopUp">
                <QualityCriteriaPopUp open={currentPopup === "QualityCriteriaPopUp"} handleClose={() => { setCurrentPopup(null) }}
                    userProduct={userProduct}
                />
            </ErrorBoundary>
            <ErrorBoundary name="HealthRenewalFaq">
                <HealthRenewalFaq open={currentPopup === "HealthRenewalFaq"} handleClose={() => { setCurrentPopup(null) }}
                    userProduct={userProduct}
                />
            </ErrorBoundary>

            <ErrorBoundary name="CallGalleryPopUp">
                <CallGalleryPopUp open={currentPopup === "CallGalleryPopUp"} handleClose={() => { setCurrentPopup(null) }} />
            </ErrorBoundary>

            <ErrorBoundary name="CsatRatingPopUp">
                <CsatRatingPopUp open={currentPopup === "CsatRatingPopUp"} handleClose={() => { setCurrentPopup(null) }} />
            </ErrorBoundary>
            <ErrorBoundary name="MyPodLeads">
                <CommonModalPopUp className="MyPodLeads" open={currentPopup === "MyPodLeads"} URL={SV_CONFIG["matrixdashboard"] ? `${SV_CONFIG["matrixdashboard"][SV_CONFIG["environment"]]}/admin/MyPodLeads` : null} handleClose={() => { setCurrentPopup(null) }} />
            </ErrorBoundary>
            <ErrorBoundary name="MyAppointments">
                <CommonModalPopUp className="MyAppointments" open={currentPopup === "MyAppointments"} URL={SV_CONFIG["matrixdashboard"] ? `${SV_CONFIG["matrixdashboard"][SV_CONFIG["environment"]]}/admin/FosAssignedDetails` : null} handleClose={() => { setCurrentPopup(null) }} />
            </ErrorBoundary>
            <ErrorBoundary name="SMEQuoteLeads">
                <CommonModalPopUp className="SMEQuoteLeads" open={currentPopup === "SMEQuoteLeads"} URL={SV_CONFIG["matrixdashboard"] ? `${SV_CONFIG["matrixdashboard"][SV_CONFIG["environment"]]}/admin/SmeDashboardTrackLead` : null} handleClose={() => { setCurrentPopup(null) }} />
            </ErrorBoundary>
            <ErrorBoundary name="AgentCrossSellLead">
                <CommonModalPopUp style={{ width: '100%', height: '100%' }}
                    className="AgentCrossSellLead" open={currentPopup === "AgentCrossSellLead"} URL={SV_CONFIG["matrixdashboard"] ? `${SV_CONFIG["matrixdashboard"][SV_CONFIG["environment"]]}/admin/AgentCrossSellLead` : null} handleClose={() => { setCurrentPopup(null) }} />
            </ErrorBoundary>
            <ErrorBoundary name="CustomerListing">
                <CustomerListing open={currentPopup === "CustomerListing"} handleClose={() => { setCurrentPopup(null) }}
                    data={(FosAssignedLeads && FosAssignedLeads.length > 0) ? FosAssignedLeads : []}
                />
            </ErrorBoundary>
            <ErrorBoundary name="BookingCreditLog">
                <BookingCreditLog open={currentPopup === "BookingCreditLog"} handleClose={()=>{setCurrentPopup(null)}}/>
            </ErrorBoundary>
        </ErrorBoundary>
        </>
    );
}
