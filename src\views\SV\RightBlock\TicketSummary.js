/* eslint-disable jsx-a11y/anchor-is-valid */
import { Button, Grid, LinearProgress,useTheme,useMediaQuery } from "@mui/material";
import React, { useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { CONFIG } from "../../../appconfig";
import { CALL_API } from "../../../services";
import rootScopeService from "../../../services/rootScopeService";
import { updateStateInRedux, setOpenRightBarMenu } from "../../../store/actions/SalesView/SalesView";
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import User from "../../../services/user.service";
import { getRoleNameFromRoleId } from "../../../helpers";
import { ExpandMore } from "@mui/icons-material";
//import { checkLeadContent } from "../../../services/Common.Js"
import { IsLeadContent } from "../../../services/Common";

export const getTicketsService = () => {
    const customerId = rootScopeService.getCustomerId();
    let UserId = User.UserId,
        EmpId = User.EmployeeId,
        Name = User.UserName,
        RoleName = getRoleNameFromRoleId(User.RoleId) || 'Agent';

    const input = {
        service: 'MatrixCoreAPI',
        url: `api/Ticket/GetTicketListMyAccV2/${customerId}/${UserId}/${EmpId}/${Name}/${RoleName}/`,
        method: 'GET',
        timeout: 2000
    }
    return CALL_API(input);
}

export const getOpenTicketsSummaryService = () => {
    const customerId = rootScopeService.getCustomerId();
    const input = {
        service: 'MatrixCoreAPI',
        url: `api/Ticket/GetTicketCount/${customerId}`,
        method: 'GET',
        timeout: 2000
    }
    return CALL_API(input);
}


const TicketSummary = () => {
    const [loading, setLoading] = useState(false);
    const [show, setShow] = useState(false);
    const [isErrorInDataApi, setIsErrorInDataApi] = useState(false);
    const [openTicketsSummary, setOpenTicketsSummary] = useState({});
    const refreshTicketsData = useSelector((state) => state.salesview.refreshTicketsData);
    const RefreshLead = useSelector((state) => state.salesview.RefreshLead);
    const dispatch = useDispatch();

    const openTicketPanel = () => {
        dispatch(setOpenRightBarMenu({ OpenRightBarMenu: 'Tickets' }))
    }
    const getOpenTicketsSummary = useCallback(() => {
        setOpenTicketsSummary({});
        setLoading(true);
        setIsErrorInDataApi(false);
        getOpenTicketsSummaryService()
            .then((res) => {
                if (res === 'No Data found' || !res) {
                    setIsErrorInDataApi(true);
                    return;
                }
                const summary = res.Data || {};
                setOpenTicketsSummary(summary)
                setIsErrorInDataApi(false);
            })
            .catch((err) => {
                setOpenTicketsSummary({});
                setIsErrorInDataApi(true);
            })
            .finally(() => {
                setLoading(false);
            });
    }, [])
    const handleToggle = (e) => {
        if (!show) {
            getOpenTicketsSummary();
        }
        setShow(!show);
    }

    useEffect(() => {
        // setLoading(true);
        // setTimeout(() => {
        //     getOpenTicketsSummary();
        // }, 2000);
        setShow(false);
    }, [RefreshLead])
    useEffect(() => {
        if (refreshTicketsData) {
            getOpenTicketsSummary();
            dispatch(updateStateInRedux({ key: 'refreshTicketsData', value: false }));
        }
    }, [dispatch, getOpenTicketsSummary, refreshTicketsData])
    const theme = useTheme();
    const { distinctIssues, openTicketCount } = openTicketsSummary || {};
    const isDesktop = useMediaQuery(theme.breakpoints.up('md'), {
        defaultMatches: true
      });
    // if(checkLeadContent())
    // return null;
    if((IsLeadContent() && !isDesktop))
    return null;
    return <>
        <Grid item sm={12} md={12} xs={12}>
            <div className="ticketSummary">
                <h3>Customer Tickets</h3>
                <h4 className="ticketSummaryh4">Create or Track Customer Tickets</h4>
                <div className="expandmoreIcon">
                    <ExpandMore onClick={handleToggle} style={{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)' }} /></div>
                {show &&
                    <>
                        {loading
                            ? <LinearProgress variant="indeterminate" color="secondary" />
                            :
                            isErrorInDataApi
                                ?
                                <>
                                    <p>Tickets could not be loaded!</p>
                                    <Button variant="text" onClick={getOpenTicketsSummary}>Click here to retry</Button>
                                </>
                                :
                                <>
                                    {(openTicketCount > 0) ?
                                        <>
                                            <p>
                                                This customer has  <img src={CONFIG.PUBLIC_URL + "/images/salesview/redticket.svg"} alt=' ' />
                                                <span>{openTicketCount} open ticket(s) </span>
                                                with <span title={Array.isArray(distinctIssues) && distinctIssues.slice(1).join(', ')}>
                                                    {distinctIssues[0]}{(distinctIssues.length - 1) > 0 ? ` and ${distinctIssues.length - 1} more` : ''}
                                                </span> Issue(s)
                                            </p>
                                            <span onClick={openTicketPanel}><a href="#"> Click here to view <ChevronRightIcon /> </a></span>
                                        </>
                                        :
                                        <>
                                            <p>No Open Tickets</p>
                                            <span onClick={openTicketPanel}><a href="#"> Click here to open ticket panel </a></span>
                                        </>}

                                </>
                        }
                    </>
                }

            </div>
        </Grid>


    </>
}
export default TicketSummary;
