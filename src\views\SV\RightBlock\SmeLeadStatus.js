import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Grid } from "@mui/material";
import { useDispatch, useSelector } from 'react-redux'
import { useSnackbar } from 'notistack';
import User from "../../../services/user.service";
import { CALL_API } from "../../../services";
import { SelectDropdown, TextInput } from "../../../components";
// import ExpandMore from '@mui/icons-material/ExpandMore';
import { uniqBy } from "lodash";
import { SV_CONFIG } from "../../../appconfig";
import { updateStateInRedux } from "../../../store/actions";
//import { saveCommentsService } from "../Main/Comments";
import { ViewAllSMELeadStatusModal } from "./Modals/ViewAllSMELeadStatusModal";
import { EditInfoPopUp } from "./Modals/EditInfoPopup";
import Common, { IsCustomerAccess, IsValidUserGroup, SetCustomerComment } from "../../../services/Common";
import { FOSpopUp } from "./Modals/FOSpopUp";
import AppointmentStatusCondition from "../../../assets/json/AppointmentStatusCondition";
import { CommonModalPopUp } from "../Main/Modals/CommonModalPopUp";
import { GetAppointmentDataService, UpdateAppointmentStatusService } from "../../Features/FosHelpers/fosServices";
import { GetSubStatusV2 } from "../../../services/Common";
import ModalPopup from '../../../../src/components/Dialogs/ModalPopup';
import { AHCPopup } from "../Main/Modals/AHCPopup";
import ErrorBoundary from "../../../hoc/ErrorBoundary/ErrorBoundary";
import StaticData from "../../../assets/json/StaticData";

const SaveAppCancelReasonService = (requestData) => {
  const input = {
    url: `api/SalesView/SaveAppCancelReason`,
    method: 'POST', service: 'MatrixCoreAPI',
    requestData
  };
  return CALL_API(input);
}

const getFOSCancellationReasonsService = (productId, subStatusID) => {
  const input = {
    url: `api/SalesView/GetReasonMaster/${productId}/${subStatusID}`,
    method: 'GET', service: 'MatrixCoreAPI',
  };
  return CALL_API(input);
}
const smeFosSubstatusReason = (productId, subStatusID,Source) => {
  const input = {
    url: `api/SalesView/GetReasonMaster/${productId}/${subStatusID}/${Source}`,
    method: 'GET', service: 'MatrixCoreAPI',
  };
  return CALL_API(input);
}
const GetSubStatusReason = (LeadId, SubStatusId) => {
  const input = {
    url: `api/SalesView/GetSubStatusReason/${LeadId}/9/${SubStatusId}`,
    method: 'GET', service: 'MatrixCoreAPI',
  };
  return CALL_API(input);
}

const getSMELeadsStatusService = (requestData) => {
  const input = {
    url: 'coremrs/api/LeadDetails/GetSMELeadsStatus',
    method: 'POST', service: 'MatrixCoreAPI',
    requestData
  };
  return CALL_API(input);
}
const setSMELeadsStatusService = (requestData) => {
  const input = {
    url: "coremrs/api/LeadDetails/SetSMELeadsStatus",
    method: "POST",
    service: "MatrixCoreAPI",
    requestData
  };
  return CALL_API(input);
}

// const UpdateLeadsStatusService = (requestData) => {
//   const input = {
//     url: `LeadDetails/UpdateLeadsStatus`,
//     method: "POST",
//     service: "core",
//     requestData
//   };

//   return CALL_API(input).then((response) => {
//     if (response && response.UpdateLeadsStatusResult && response.UpdateLeadsStatusResult.Data) {
//       return response.UpdateLeadsStatusResult.Data;
//     }
//     return false;
//   }).catch(() => false);
// };


const getAnyError = function (newstatus, productId, SubStatusMandatory) {
  var errormessage = {
    Lead: "Select a valid LeadID \n",
    Status: "Select a Status \n",
    SubStatus: "Select a SubStatus \n"
  }
  var ProductId = productId;
  var message = "";
  var invalids = [undefined, null, 0, "", "0"];
  if (invalids.indexOf(newstatus.LeadID) !== -1)
    message += errormessage.Lead;
  if (invalids.indexOf(newstatus.StatusID) !== -1)
    message += errormessage.Status;
  if (ProductId === 131) {
    if (newstatus.StatusID !== 11 && invalids.indexOf(newstatus.SubStatusID) !== -1)
      message += errormessage.SubStatus;
  }
  else if ([158, 159, 160, 164, 171].indexOf(ProductId) !== -1 && newstatus.StatusID === 11) {
  }
  else if (invalids.indexOf(newstatus.SubStatusID) !== -1 && SubStatusMandatory)
    message += errormessage.SubStatus;
  return message;
}

const _newSMELeadStatus = { "LeadID": 0, "StatusID": 0, "SubStatusID": 0, "SubStatusName": ""};
function SmeLeadStatus(props) {

  const [Product, setProduct] = useState("");
  const [IsStatusDisabled, setIsStatusDisabled] = useState(false);
  const [IsRenewalSubstaus, setIsRenewalSubstaus] = useState(0);
  const [leadOptions, setLeadOptions] = useState([]);
  const [leadSourceId, setLeadSourceId] = useState(5);
  const [planList, setPlanList] = useState([]);
  const [LeadStatus, setLeadStatus] = useState([]);
  const [SMEStatus, setSMEStatus] = useState([]);
  const [SMESubStatus, setSMESubStatus] = useState([]);
  const [StatusLog, setStatusLog] = useState([]);
  const [filterSMEStatus, setFilterSMEStatus] = useState([]);
  const [filterSMESubStatus, setFilterSMESubStatus] = useState([]);
  const [CurrentStatusId, setCurrentStatusId] = useState(0);
  const [CurrentSubStatusId, setCurrentSubStatusId] = useState(0);
  const [NewSMELeadStatus, setNewSMELeadStatus] = useState(_newSMELeadStatus);
  const [ShowSupplierPlan, setShowSupplierPlan] = useState(false);
  const [openViewAllPopup, setOpenViewAllPopup] = useState(false);
  const [openEditInfoPopup, setOpenEditInfoPopup] = useState(false);
  const [IsEnableAppointmentBtn, setIsEnableAppointmentBtn] = useState(false);
  const [AppointmentDate, setAppointmentDate] = useState('');


  const [OpenFOSLink, setOpenFOSLink] = useState(false);
  const [SupplierList, setSupplierList] = useState([{ Id: 0 }]);
  const [Isdisabled, setIsdisabled] = useState(false);
  const [AppBtnName, setAppBtnName] = useState("Set");
  const [substatusWithProperties, setSubstatusWithProperties] = useState([]);
  const [properties, setProperties] = useState([]);
  const [propertiesInput, setPropertiesInput] = useState({});
  const [FOSCancellationReasons, setFOSCancellationReasons] = useState([]);
  const [FosAppointment, setFosAppointment] = useState(false);
  const [OpenHistory, setOpenHistory] = useState(false);
  const [viewButtonToAdmin, setviewButtonToAdmin] = useState(false);
  const [SubStatusMandatory, setSubStatusMandatory] = useState(true);
  const [SubStatusReason, setSubStatusReason] = useState([]);
  
  //const [GrpSubStatusMappingArr,setGrpSubStatusMappingArr]=useState([]);
  var intloanPrds = [158, 159, 160, 164, 171];
  var GrpSubStatusMappingArr = [];

  const dispatch = useDispatch();
  const AppointmentStatus = SV_CONFIG["AppointmentStatus"][SV_CONFIG["environment"]];
  const SMELeadEditSubStatusId = SV_CONFIG["SMELeadEditSubStatusId"][SV_CONFIG["environment"]];
  const FOSSubStatus = SV_CONFIG["FOSSubStatus"][SV_CONFIG["environment"]];
  const { enqueueSnackbar } = useSnackbar();
  const { AllLeads, ParentLeadId, IsRenewal, primaryLeadId,
    leadIds, ProductId, CustomerId, leadId,
    refreshLead, AppointmentDateTime
  } = props;
  // const [AllLeads, ParentLeadId, IsRenewal, primaryLeadId, leadIds] = useSelector(state => {
  //   let { allLeads, parentLeadId, IsRenewal, primaryLeadId, leadIds } = state.salesview;
  //   return [allLeads, parentLeadId, IsRenewal, primaryLeadId, leadIds]
  // });
  const [Leadsexpiry] = useSelector(state => {
    let { Leadsexpiry } = state.salesview;
    return [Leadsexpiry]
  });
  const RoleId = User.RoleId;
  // to check
  let count = 0;
  const [IsConfirmationPopupShow, setIsConfirmationPopupShow] = useState(false);
  const [IsSubStatusMessage, setIsSubStatusMessage] = useState(false);
  const [Message, setMessage] = useState(null);
  const [AHCFlag, setAHCFlag] = useState(false);
  const [SubStatusReasonId, setSubStatusReasonId] = useState(0);
  const [ChangedLeadId, setChangedLeadId] = useState(0);
  const ShowSmeCustomerType = useSelector(state => state.salesview.ShowSmeCustomerType);
  const SetProductName = () => {

    if ([148, 155, 163].indexOf(ProductId) !== -1) {
      setIsStatusDisabled(true);
    }

    if ([2, 106, 118, 130].indexOf(ProductId) !== -1 && IsRenewal) {
      setProduct("Renewal");
      setLeadSourceId(6);
      setIsStatusDisabled(false);

    }
    else if ([106, 118, 130].indexOf(ProductId) !== -1) {
      setProduct("Fresh");
      setIsStatusDisabled(true);
    }
    else if ([117].indexOf(ProductId) !== -1) {
      if (IsRenewal) {
        setProduct("Renewal");
        setLeadSourceId(6);
      } else {
        setProduct("Motor");

      }
    }
    else if (ProductId === 158) {
      setProduct("Int Credit Cards");
    }
    else if (ProductId === 2)
      setProduct("Health");
    else if (ProductId === 159)
      setProduct("Int Personal Loans");
    else if (ProductId === 160)
      setProduct("International Bank Accounts");
    else if (ProductId === 165)
      setProduct("Partnerships");
    else if (ProductId === 101)
      setProduct("Home Insurance");
    else if (ProductId === 139)
      setProduct("Commercial Vehical");
    else if (ProductId === 148)
      setProduct("International Car");
    else if (ProductId === 155)
      setProduct("International Term");
    else if (ProductId === 155)
      setProduct("International TwoWheeler");
    else if (ProductId === 151)
      setProduct("International health");
    else if (ProductId === 7)
      setProduct("Term");
    else if (ProductId === 1000)
      setProduct("Term");
    else if (ProductId === 115)
      setProduct("Investment");
    else if (ProductId)
      setProduct("");
  }

  const GetsupplierPlanList = (leadId, statusId) => {
    setSupplierList([{ Id: 0 }]);
    setShowSupplierPlan(false);
    if (intloanPrds.indexOf(ProductId) != -1 && statusId == 11) {
      setShowSupplierPlan(true);
      setIsdisabled(false);
      if (planList != null) {
        if (planList.length > 0) {

          let supplierIndex = 0;
          planList.forEach(function (item) {
            if (item.OldSupplierId != null && supplierIndex <= 15 && item.ID === leadId) {
              if (supplierIndex === 0) {
                setSupplierList([]);
                setIsdisabled(true);
              }
              SupplierList.push({ Id: supplierIndex, Supplier: { OldSupplierId: item.OldSupplierId }, Plan: { OldPlanId: item.OldPlanId } });
              supplierIndex++;
            }
          });
        }
      }
    }
  };

  const isSMEFosAgent = () => {
    var isFOS = false;
    const fosGroups = SV_CONFIG["SMEFOSGroups"];
    if (Array.isArray(AllLeads) && AllLeads.length > 0) {
      AllLeads.forEach((vdata, index) => {
        if ((fosGroups.indexOf(vdata.AssignedToGroupId) !=-1) || (vdata.LeadCreationSource=='SMEFosCreateLeadPanel')) {
          isFOS = true;
        }
      })
    }

    return isFOS;
   
  }
  const getSMELeadsStatus = (sourceID, changedLeadId = 0) => {
    var leadSourceReq = sourceID;
    if (ProductId == 131) {
      if (isSMEFosAgent()) {
        leadSourceReq = 35
      }
      else {
        leadSourceReq = 0;
      }

    }
    const reqData = {
      "CustomerID": CustomerId,
      "LeadID": ParentLeadId,
      roleId: RoleId || 13,
      leadsourceId: leadSourceReq,
      "ParentID": ParentLeadId
    };
    let _LeadStatus = [];

    setSMEStatus([]);
    setSMESubStatus([]);

    getSMELeadsStatusService(reqData).then((resultData) => {
      resultData = resultData ? resultData.Data : null;
      if (resultData != null && Array.isArray(resultData) && resultData.length > 0) {
        if (resultData[0].planList) {
          setPlanList(resultData[0].planList);
        }

        const temp = resultData[0].Leads;
        Array.isArray(temp) && temp.forEach(function (vdata) {
          if (leadIds.indexOf(vdata.LeadID) !== -1) {
            _LeadStatus.push(vdata);
          }
        })
        setLeadStatus(_LeadStatus);
        
        setSMEStatus(resultData[0].Status);
        
     
        setSMESubStatus(resultData[0].SubStatus);

        const logdata = resultData[0].LeadStatusLog;
        let _StatusLog = [];
        Array.isArray(logdata) && logdata.forEach((vdata, key) => {
          if (leadIds.indexOf(vdata.LeadID) !== -1)
            _StatusLog.push(vdata);
        });
        setStatusLog(_StatusLog);
        setChangedLeadId(changedLeadId);
        if (([2, 106, 118, 130].indexOf(ProductId) !== -1) && IsRenewal == 1) {
          _LeadStatus.forEach(function (vdata, key) {
            if (vdata.LeadID === NewSMELeadStatus.LeadID) {
              setNewSMELeadStatus({ ...NewSMELeadStatus, StatusID : vdata.StatusId , SubStatusID: vdata.SubStatusID, SubStatusName: vdata.SubStatusName });
            }
          })
        }

      }
    });
  }

  const getSubStatus = () => {
    let _filterSMESubStatus = [];
    let usergrp = User && User.UserGroupList;
    if (RoleId === 13 && usergrp !== undefined) {

      var AppConditionOnProductIdArr = AppointmentStatusCondition.ProductGrp_SubStatusMapping[ProductId] || [];

      if (AppConditionOnProductIdArr.length > 0) {
        usergrp.forEach((item) => {
          if (AppConditionOnProductIdArr[item.GroupId])
            GrpSubStatusMappingArr = AppConditionOnProductIdArr[item.GroupId].subStatusArr || [];
        });
      }
    }



    SMESubStatus.forEach((vdata) => {
      if (vdata.StatusId === NewSMELeadStatus.StatusID && (!AppointmentStatusCondition.notShowSubStatusArr.includes(vdata.SubStatusID))) {
        if (!AppointmentStatus.includes(vdata.SubStatusID))
          _filterSMESubStatus.push(vdata);

        else {// Appointment Substatus area
          if (([2, 11].indexOf(RoleId) > -1 || (ProductId === 2 && [12].indexOf(RoleId) > -1)) && vdata.StatusId >= 4 && AppointmentStatusCondition.TLShowSubstatusArr.includes(vdata.SubStatusID))
            _filterSMESubStatus.push(vdata);

          else if (RoleId === 13 && vdata.StatusId >= 4 && ([2002].indexOf(vdata.SubStatusID) || GrpSubStatusMappingArr.indexOf(vdata.SubStatusID)))
            _filterSMESubStatus.push(vdata);


        }
      }
    });
    setFilterSMESubStatus(_filterSMESubStatus);
    if (count === 1) {
      //if (localStorage.getItem("flag") == 1) {
      dispatch(updateStateInRedux({ key: "RenewalStampingFlag", value: 1 }));
      // }
      // rootScopeService.setFlag(1);
    }

    // todo for intloanPrds.indexOf($scope.ProductId) != -1 && statusId == 11
    // GetsupplierPlanList(NewSMELeadStatus.LeadID, NewSMELeadStatus.StatusID);
  };

  const LeadChange = (lead) => {
    let matched = 0;
    let _filterSMEStatus = [];
    let newSMELeadStatusTemp = _newSMELeadStatus;
    LeadStatus.forEach(function (vdata, key) {
      if (vdata.LeadID === lead) {
        setCurrentStatusId(vdata.StatusId);
        setCurrentSubStatusId(vdata.SubStatusID);
        newSMELeadStatusTemp = { "LeadID": vdata.LeadID, "StatusID": vdata.StatusId, "SubStatusID": vdata.SubStatusID, "SubStatusName": vdata.SubStatusName };
        SMEStatus.forEach((vdata1, key1) => {
          if (vdata1.StatusID >= vdata.StatusId)
            _filterSMEStatus.push(vdata1);
        })
        // getSubStatus();
        matched = 1;
      }
    })
    if (matched === 0) {
      newSMELeadStatusTemp = { "LeadID": lead, "StatusID": 0, "SubStatusID": 0 };
      _filterSMEStatus = SMEStatus;
      // getSubStatus();
    }
    if (_filterSMEStatus != null && Array.isArray(_filterSMEStatus) && _filterSMEStatus.length > 0) {
      _filterSMEStatus = uniqBy(_filterSMEStatus, "StatusID");
    }
    setFilterSMEStatus(_filterSMEStatus);
    setNewSMELeadStatus(newSMELeadStatusTemp);
    if (Leadsexpiry.includes(lead) && newSMELeadStatusTemp.SubStatusID != null && newSMELeadStatusTemp.SubStatusID != 0 && newSMELeadStatusTemp.SubStatusID != undefined) {
      dispatch(updateStateInRedux({ key: 'SubStatusAdded', value: true }));
    }
    IsEnableAppointment(newSMELeadStatusTemp.SubStatusID, false);

  };
  const validateProperty = (item, isSubmit = false) => {
    let valid = true, errorMsg = '';
    try {
      if (isSubmit) item.value = item.value.trim();
    }
    catch { }

    // if (item.ValidationType === "numeric" && item.value != (+item.value)) {
    //   errorMsg += `${item.PropertyName} must be numeric \n`;
    //   valid = false;
    // }

    // if (isSubmit && item.PropertyType === 'checkbox') {
    //   valid = false;
    //   Object.keys(selectedCheckboxProperty).forEach((key) => {
    //     if (selectedCheckboxProperty[key] !== undefined) {
    //       valid = true
    //     }
    //   })
    //   if (!valid) errorMsg += `${item.PropertyName} is Mandatory \n`;

    // }
    // if (isSubmit && ['checkbox'].indexOf(item.PropertyType) === -1 && !item.value) {
    //   // if (isSubmit && !item.value && item.PropertyType === 'textbox') {
    //   errorMsg += `${item.PropertyName} is Mandatory \n`;
    //   valid = false;
    // }

    return { valid, errorMsg };
  }

  const handleShow = (ShowSupplierPlan) => {

    if ((ProductId == 219) && (NewSMELeadStatus.StatusID) && ([81, 82].includes(NewSMELeadStatus.StatusID))) {
      return false;
    } else if ((ProductId == 219) && (NewSMELeadStatus.StatusID) && (NewSMELeadStatus.StatusID == 83)) {
      return true
    }
    else {
      return !ShowSupplierPlan;
    }
  }

  const handleChangeForProperties = (e, PropertyId, PropertyName, ValidationType, PropertyType) => {
    let value = e.target.value;
    let updatedProperty = {
      PropertyId,
      PropertyName,
      value,
      PropertyType,
      ValidationType
      // textValue: 
    }
    let validationResult = validateProperty(updatedProperty, false);
    if (!validationResult.valid) {
      enqueueSnackbar(validationResult.errorMsg, {
        variant: 'error',
        autoHideDuration: 3000,
        style: { whiteSpace: 'pre-line' }
      });
      return;
    }
    setPropertiesInput({
      ...propertiesInput,
      [PropertyId]: updatedProperty
    });
  }

  const GetSubStatusPropertyData = (statusId, leadsourceId, productId) => {
    GetSubStatusV2(statusId, productId, leadsourceId, User.RoleId, User.UserId).then((result) => {
      if (result && Array.isArray(result)) {
        setSubstatusWithProperties(result);
      }
      else {
        setSubstatusWithProperties([]);
      }
    });
  }

  const getOptionsList = (name) => {
    let optionsList = [];
    let labelKey = 'Name', valueKey = 'ID';
    switch (name) {
      case 'FOS Cancellation Reason':
        if ([2004].includes(NewSMELeadStatus.SubStatusID) && (!Array.isArray(FOSCancellationReasons) || FOSCancellationReasons.length === 0)) {
          getFOSCancellationReasonsService(ProductId, NewSMELeadStatus.SubStatusID).then((result) => {
            setFOSCancellationReasons(result);
          });
        }
        labelKey = 'Reason';
        valueKey = 'ReasonId';
        optionsList = FOSCancellationReasons;
        break;
      default:
        break;
    }
    optionsList = Array.isArray(optionsList) ? optionsList : [];
    return { optionsList, labelKey, valueKey };
  }

  const getInputByProperty = (property) => {
    let { PropertyId, PropertyName, PropertyType, ValidationType } = property;
    let Component = null;
    let _handleChange = (e) => { handleChangeForProperties(e, PropertyId, PropertyName, ValidationType, PropertyType) }
    switch (PropertyType) {
      case 'textbox':
        Component = <TextInput
          name={PropertyName} // id
          label={PropertyName}
          value={propertiesInput[PropertyId] ? propertiesInput[PropertyId].value : ''}
          handleChange={_handleChange}
          show={true}
          sm={12} md={12} xs={12}
          maxLength={255}
          key={PropertyId}
        />
        break;
      case 'dropdown':
        const { optionsList, labelKey, valueKey } = getOptionsList(PropertyName);
        Component = <SelectDropdown
          name={PropertyName}
          label={PropertyName}
          value={propertiesInput[PropertyId] ? propertiesInput[PropertyId].value : ''}
          options={optionsList}
          labelKeyInOptions={labelKey}
          valueKeyInOptions={valueKey}
          handleChange={_handleChange}
          show={true}
          sm={12} md={12} xs={12}
        />
        break;
      // case 'checkbox':
      //   const options = getCheckboxOptions(PropertyName);
      //   Component = <>
      //     <Grid item>
      //       <h3>{PropertyName}</h3>
      //     </Grid>
      //     {options.map((option) => (
      //       <Grid key={option.id} item sm={12} md={12} xs={12}>
      //         <Fragment key={option.id}>
      //           <input
      //             type="checkbox"
      //             onChange={(e) => { handleCheckboxChange(e, option) }}
      //             checked={selectedCheckboxProperty[option.id] !== undefined}
      //             id={option.id} name={option.label}
      //             key={1}
      //           />
      //           <label key={2}>{option.label}</label>
      //         </Fragment>
      //       </Grid>
      //     ))}
      //   </>

      //   break;
      default:
        break;
    }
    return Component;
  }

  // Set list of values for the filters
  const initFormValues = () => {
    SetProductName();
    let lOptions = [];
    // let sOptions = [{ label: "Lead Status", value: "0" }];

    AllLeads.forEach((lead) => {
      if (IsRenewal && ([2, 106, 118, 130].indexOf(ProductId) !== -1)) {
        if (lead.LeadSourceId === 6 && ((lead.StatusMode === "P" && lead.StatusId < 13) || (lead.StatusMode === "P" && (lead.StatusId == 81 || lead.StatusId == 82 || lead.StatusId == 83))))
          lOptions.push(lead.LeadID);
      }
      else {
        if ((lead.StatusMode === "P" && lead.StatusId < 13) || (lead.StatusMode === "P" && (lead.StatusId == 81 || lead.StatusId == 82 || lead.StatusId == 83)))
          lOptions.push(lead.LeadID);
      }
      // sOptions.push({ label: lead.StatusName, value: lead.StatusId });
    });

    //LeadId dropdown values
    setLeadOptions(lOptions);

  };
  const IsChkAppointmentValidate = function () {
    let result = false;
    //let PreviousStatusId = NewSMELeadStatus.PreviousSubstatusId;
    let PreviousStatusId = CurrentSubStatusId;
    let updateStatusId = NewSMELeadStatus.SubStatusID;
    let currentStatusCondition = AppointmentStatusCondition[updateStatusId];
    if (currentStatusCondition && currentStatusCondition.PreStatusStatus.indexOf(PreviousStatusId) > -1 && !(currentStatusCondition.PreStatusConditions.indexOf(PreviousStatusId) > -1)) {
      result = true;
    }
    else if (currentStatusCondition.PreStatusStatus.length == 0 && currentStatusCondition.PreStatusConditions.length == 0) {
      result = true;
    }
    else if ([2002].indexOf(updateStatusId) > -1 && !(currentStatusCondition.PreStatusConditions.indexOf(PreviousStatusId) > -1)) {
      result = true;
    }

    return result;

  }
  const IsChkAppointmentPostponed = function () {
    return true;
    // let result = false;

    // if (AppointmentDate !== "") {
    //let appointmentDate = new Date($scope.AppointmentDate).getDate();
    //let tommorowDate = new Date().setDate(new Date().getDate() + 1);

    // let d = new Date();
    // d.setDate(d.getDate() + 1);
    // //if (d.getDate() <= appointmentDate) {
    // if (new Date(d.toLocaleDateString()) <= new Date(new Date(AppointmentDate).toLocaleDateString())) {
    //   return true;
    // }
    // }
    // return result;
  }

  // const UpdateLeadsStatus = () => {

  //   let reqLeadList = "";
  //   AllLeads.forEach((vdata, key) => {
  //     // Select only Positive Status
  //     if (vdata.StatusId < 3 && vdata.LeadID != NewSMELeadStatus.LeadID) {
  //       reqLeadList = reqLeadList + vdata.LeadID + ',';
  //     }
  //   });
  //   let subStatusID = 0;
  //   if (ProductId === 131)
  //     subStatusID = 1520
  //   var reqData = {
  //     Leads: reqLeadList,
  //     StatusId: 3,
  //     SubStatusId: subStatusID,
  //     UserID: User.UserId
  //   };
  //   UpdateLeadsStatusService(reqData).then((resultData) => {
  //     if (resultData) {
  //       reqData = {
  //         parentID: ParentLeadId,
  //         UserId: User.UserId,
  //         action: 7
  //       };
  //       Common.SetNotificationAction(reqData);
  //       // IsVisible = false;
  //       // rootScopeService.setLeadRefresh();
  //       // dispatch(setRefreshLead({ RefreshLead: true }));
  //       refreshLead();

  //     }
  //   }, function () {
  //     // $scope.leads = {};
  //     // rootScopeService.setLeadRefresh();
  //     // dispatch(setRefreshLead({ RefreshLead: true }));
  //     refreshLead();


  //   });
  // }
  const SaveLeadStatusData = (setComment, _planList) => {
    const requestData = {
      CustomerID: CustomerId,
      ParentID: ParentLeadId,
      UserID: User.UserId,
      roleId: RoleId,
      planList: _planList,
      LeadID: NewSMELeadStatus.LeadID,
      StatusId: NewSMELeadStatus.StatusID,
      SubStatusId: NewSMELeadStatus.SubStatusID ? NewSMELeadStatus.SubStatusID : 0,
      IsLogSubStatus: IsStatusDisabled ? true : false, // In case of Health Fresh leads
      IsHealthRenewal: IsRenewalSubstaus === 2 ? true : false,
    }
    setSMELeadsStatusService(requestData).then((resultData) => {
      if (resultData && resultData.Data) {
        setIsConfirmationPopupShow(false);
        setSubstatusMessage(NewSMELeadStatus.SubStatusID);
        setIsRenewalSubstaus(0);
        resultData = resultData.Data;
        if (resultData.Message === "HigherStatus") {
          enqueueSnackbar("Lead has been moved to higher status", {
            variant: 'error',
            autoHideDuration: 3000,
          });
          // dispatch(setRefreshLead({ RefreshLead: true }));
          refreshLead();
          return;
        }

        if (resultData.StatusCode == 1 && IsRenewal == 1) {
          enqueueSnackbar(resultData.Message, {
            variant: 'error',
            autoHideDuration: 3000,
          });
          refreshLead();
          return;
        }

        if (AppointmentStatusCondition.PropertySubStatuses.includes(NewSMELeadStatus.SubStatusID)) {
          let propertyId = 7;
          let property = propertiesInput[propertyId];
          let selectedReason = FOSCancellationReasons.find(reason => reason.ReasonId === property.value); let textValue = selectedReason ? selectedReason.Reason : '';


          if (IsSmeReason(NewSMELeadStatus.SubStatusID)) {
            propertyId = 9;
            property = { value: SubStatusReasonId }
            let reasonText = SubStatusReason.find(reason => reason.ReasonId === SubStatusReasonId);
            textValue = reasonText ? reasonText.Reason : ''
          }

          const reqData = {
            Leads: NewSMELeadStatus.LeadID,
            value: property.value,
            textValue,
            PropertyId: propertyId,
            UserID: User.UserId,
            SubStatusId: NewSMELeadStatus.SubStatusID ? NewSMELeadStatus.SubStatusID : 0,
          }
          SaveAppCancelReasonService(reqData);
        }

        dispatch(updateStateInRedux({ key: "renewalStampingFlag", value: 1 }));
        count = 1;

        // rootScopeService.setSubStatus($scope.NewSMELeadStatus.SubStatusID ? $scope.NewSMELeadStatus.SubStatusID : 0);
        console.log("Saved");
        if (ProductId === 131) {
          var data = {
            LeadId: NewSMELeadStatus.LeadID,
            productId: ProductId,
            statusId: NewSMELeadStatus.StatusID,
            subStatusId: NewSMELeadStatus.SubStatusID ? NewSMELeadStatus.SubStatusID : 0,
            userId: User.UserId
          };
        }
        if (setComment) {

          var comment = Product + " Status updated to ";
          let counter = 0;
          SMEStatus.forEach((vdata, key) => {
            if (NewSMELeadStatus.StatusID === vdata.StatusID)
              comment += vdata.StatusName;
          })
          SMESubStatus.forEach((vdata) => {
            if (NewSMELeadStatus.SubStatusID === vdata.SubStatusID && counter === 0) {
              comment += "(" + vdata.SubStatusName + ")";
              counter = 1;
            }
          })

          const reqData = {
            CustomerId: CustomerId,
            ProductId: ProductId,
            ParentLeadId: NewSMELeadStatus.LeadID,
            PrimaryLeadId: primaryLeadId,
            UserId: User.UserId,
            Comment: comment,
            EventType: 12
          };
          SetCustomerComment(reqData).then((resultData) => {
            //saveCommentsService(reqData).then(function (resultData) {
            enqueueSnackbar("Saved Successfully", {
              variant: 'success',
              autoHideDuration: 3000,
            });
            if (ShowSupplierPlan) {
              getSMELeadsStatus();
            }
            // TODO
            if (NewSMELeadStatus.StatusID >= 3 && ([2, 106, 118, 130].indexOf(ProductId) === -1) ) {
              // UpdateLeadsStatus();
              //do nothing
            }
            else {
              // rootScopeService.setLeadRefresh();
              // dispatch(setRefreshLead({ RefreshLead: true }));
              refreshLead();
            }
          }, function () {
            enqueueSnackbar("Error Saving History", {
              variant: 'error',
              autoHideDuration: 3000
            });
            // dispatch(setRefreshLead({ RefreshLead: true }));
            refreshLead();

          });
        }
        else {
          if (Leadsexpiry.includes(NewSMELeadStatus.LeadID) && NewSMELeadStatus.SubStatusID != null && NewSMELeadStatus.SubStatusID != 0 && NewSMELeadStatus.SubStatusID != undefined) {
            dispatch(updateStateInRedux({ key: 'SubStatusAdded', value: true }));
          }
          enqueueSnackbar("Saved Successfully", {
            variant: 'success',
            autoHideDuration: 3000
          });
          // TODO
          if (NewSMELeadStatus.StatusID >= 3 && ([2, 106, 118, 130].indexOf(ProductId) === -1)) {
            // UpdateLeadsStatus();
            // rootScopeService.setLeadRefresh();
            // dispatch(setRefreshLead({ RefreshLead: true }));
            refreshLead();
          }
          else {
            // rootScopeService.setLeadRefresh();
            // dispatch(setRefreshLead({ RefreshLead: true }));
            refreshLead();
          }

          if (ShowSupplierPlan) {
            getSMELeadsStatus();
          }
        }
        if (ProductId === 131 && NewSMELeadStatus.StatusID === 4) {
          if (AllLeads && AllLeads.length > 0 && AllLeads[0].SubProductTypeId && AllLeads[0].SubProductTypeId == 13 && !ShowSmeCustomerType) {
            dispatch(updateStateInRedux({ key: "PotentialRepeatBuyer", value: true }));
          }
        }
        // resetInput();
      }

    }).catch((err) => {
      console.warn(`error in saving sme status: ${err}`)
      enqueueSnackbar(`Error Saving ${Product} Status`, {
        variant: "error",
        autoHideDuration: 3000,
      });
      getSMELeadsStatus();
    });
  }

  const SetLeadSMEStatus = (ShowEditSection = true) => {
    let _planList = [];
    // if (invalids.indexOf($scope.SupplierList) == -1) {
    //   angular.forEach($scope.SupplierList, function (item) {
    //     if (item.Supplier != null && item.Plan != null) {
    //       planList.push({
    //         OldSupplierId: item.Supplier.OldSupplierId, SupplierDisplayName: item.Supplier.SupplierDisplayName,
    //         OldPlanId: item.Plan.OldPlanId, PlanDisplayName: item.Plan.PlanName
    //       });
    //     }
    //   });
    // }
    // if ($scope.ShowSupplierPlan) {
    //   if (planList.length == 0) {
    //     alertify.error("supplier/Plan Mandatory");
    //     return;
    //   }

    //   $scope.NewSMELeadStatus.SubStatusID = 0;
    // }

    const errMessage = getAnyError(NewSMELeadStatus, ProductId, SubStatusMandatory);
    if (errMessage) {
      enqueueSnackbar(errMessage, {
        variant: 'error',
        autoHideDuration: 3000,
        style: { whiteSpace: 'pre-line' }
      });
      return;
    }

    // if (
    //   ShowEditSection &&
    //   ProductId === 131 &&
    //   NewSMELeadStatus.StatusID === 4 &&
    //   SMELeadEditSubStatusId.indexOf(NewSMELeadStatus.SubStatusID) > -1
    //   //&& NewSMELeadStatus.SubStatusID >= 1522
    // ) {
    //   setOpenEditInfoPopup(true);
    //   return;
    // }


    let setComment = false;
    LeadStatus.forEach((vdata, key) => {
      if (vdata.LeadID === NewSMELeadStatus.LeadID && IsRenewalSubstaus !== 2) {
        if (NewSMELeadStatus.StatusID === vdata.StatusId) {
          setComment = true;
        }
      }
    });

    // if (AppointmentStatus.indexOf(NewSMELeadStatus.SubStatusID) > -1) {
    //   //CallApi 
    //   updateAppointmentModel(rootScopeService.getLeadId());
    // }

    //Save Data 
    SaveLeadStatusData(setComment, _planList);



  }
  useEffect(() => {

    if (ParentLeadId > 0) {
      dispatch(updateStateInRedux({ Key: 'AppointmentDateTime', value: '' }));
      //setAppointmentDate('');
      //GetSMELeadsStatusApp();
      if (NewSMELeadStatus.SubStatusID > 0) {
        IsEnableAppointment(NewSMELeadStatus.SubStatusID, false);
      }
    }
  }, [ParentLeadId])

  useEffect(() => {
    if (SMESubStatus && SMESubStatus.length > 0) {
      getSubStatus();
      // if (NewSMELeadStatus.StatusID !== 0) {
      //   GetSubStatusPropertyData(NewSMELeadStatus.StatusID, leadSourceId, ProductId)
      // }
    }
  }, [NewSMELeadStatus.StatusID, SMESubStatus, filterSMEStatus])

  useEffect(() => {
    if (NewSMELeadStatus.StatusID !== 0) {
      GetSubStatusPropertyData(NewSMELeadStatus.StatusID, leadSourceId, ProductId)
    }
  }, [NewSMELeadStatus.StatusID])

  // useEffect(() => {
  //   if (LeadStatus && LeadStatus.length > 0 && SMEStatus && SMEStatus.length > 0 && SMESubStatus && SMESubStatus.length > 0) {
  //     LeadChange(LeadStatus[0].LeadID);
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [LeadStatus, SMEStatus, SMESubStatus])

  useEffect(() => {
    if (Array.isArray(AllLeads) && AllLeads.length > 0)
      GetLeadsData(AllLeads[0].LeadID);
  }, [AllLeads]);

  useEffect(() => {
    if (Array.isArray(AllLeads) && AllLeads.length > 0) {
      initFormValues();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [AllLeads, IsRenewal]);

  useEffect(() => {
    if (NewSMELeadStatus.SubStatusID > 0)
      ProcessSubStatusReason();
  }, [NewSMELeadStatus.SubStatusID])

  useEffect(() => {
    if (ChangedLeadId > 0) {
      LeadChange(ChangedLeadId);
      // ProcessSubStatusReason(ChangedLeadId, NewSMELeadStatus.SubStatusID);
    }
  }, [ChangedLeadId])

  const GetLeadsData = (changedLeadId = 0) => {
    if (Array.isArray(AllLeads) && AllLeads.length > 0) {
      let sourceID = 5;
      AllLeads.forEach((Lead) => {
        if (Lead.LeadSourceId === 6) {
          sourceID = 6;
        }
      })
      getSMELeadsStatus(sourceID, changedLeadId);
    }
  }

  const ProcessSubStatusReason = (leadId = null, subStatusId = null) => {
    if (IsSmeReason(NewSMELeadStatus.SubStatusID)) {
      if (isSMEFosAgent()) {
        smeFosSubstatusReason(ProductId, subStatusId ? subStatusId : NewSMELeadStatus.SubStatusID, "SMEFosCreateLeadPanel").then((result) => {
          setSubStatusReason(result);
        });
      }
      else
      {
        getFOSCancellationReasonsService(ProductId, subStatusId ? subStatusId : NewSMELeadStatus.SubStatusID).then((result) => {
          setSubStatusReason(result);
        });
      }


      GetSubStatusReason(leadId ? leadId : NewSMELeadStatus.LeadID, subStatusId ? subStatusId : NewSMELeadStatus.SubStatusID).then((result) => {
        setSubStatusReasonId(result);
      });
    }
    else {
      setFOSCancellationReasons([]);
      setSubStatusReason([]);
      setSubStatusReasonId(0);
    }
  }

  const UpdateStatus = () => {
    setOpenEditInfoPopup();
    SetLeadSMEStatus(false)
  }

  const IsSmeReason = (subStatusID) => {
    return (ProductId == 131 && [2150, 2155, 2160, 2253, 2322].includes(subStatusID));
  }

  const handleChange = (e) => {
    const val = e.target.value;
    const e_name = e.target.name;
    switch (e_name) {
      case "leadId":
        setNewSMELeadStatus({ ...NewSMELeadStatus, LeadID: val });
        if (ProductId == 131) {
          GetLeadsData(val);
        }
        else {
          LeadChange(val);
        }
        break;
      case "StatusId":
        setNewSMELeadStatus({ ...NewSMELeadStatus, StatusID: val, SubStatusID: 0 });
        setSubStatusMandatory((val == 81) ? false : true);        // For CustomerAssistance Product when Lead is Close then Substatus is mandatory
        IsEnableAppointment(0);
        break;
      case "SubStatusId":
        setNewSMELeadStatus({ ...NewSMELeadStatus, PreviousSubstatusId: NewSMELeadStatus.SubStatusID, SubStatusID: val });
        IsEnableAppointment(val, true);

        let data = Array.isArray(substatusWithProperties)
          && substatusWithProperties.find(subStatusP => subStatusP.SubStatusID == val);
        data = data && data.properties;
        if (Array.isArray(data)) {
          setProperties(data);
          let _propertiesInput = {};
          if (data) {
            data.forEach((d) => {
              let { PropertyId, PropertyName, ValidationType, PropertyType } = d;
              _propertiesInput[PropertyId] = {
                PropertyId,
                PropertyName,
                PropertyType,
                value: '',
                textValue: '',
                ValidationType
              }
            });
          }
          setPropertiesInput(_propertiesInput);
        }
        break;
      case "SubStatusReason":
        setSubStatusReasonId(val);
        break;
      default:
        break;
    }
  }

  const GetSMELeadsStatusApp = function () {
    if (AppointmentDateTime) {
      setAppointmentDate(AppointmentDateTime);
    }
    else {
      setAppointmentDate('')
    }
    return false;
  }

  useEffect(() => {
    GetSMELeadsStatusApp();

  }, [AppointmentDateTime])

  const getFosDetail = () => {
    // if status is appointmentbooked then popup will open
    //if ([2, 115, 117, 7, 1000,131].indexOf(ProductId) !== -1 && FOSSubStatus.indexOf(NewSMELeadStatus.SubStatusID) !== -1) {
    setOpenFOSLink(true);
    setFosAppointment(false);
    //}
  }

  const viewFosDetail = () => {
    // if status is appointmentbooked then popup will open
    // if ([2, 115, 117, 7, 1000,131].indexOf(ProductId) !== -1 && FOSSubStatus.indexOf(NewSMELeadStatus.SubStatusID) !== -1) {
    setOpenFOSLink(true);
    setFosAppointment(true);
    //}
  }

  const viewFosHistory = () => {
    // if status is appointmentbooked then popup will open
    // if ([2, 115, 117, 7, 1000,131].indexOf(ProductId) !== -1 && FOSSubStatus.indexOf(NewSMELeadStatus.SubStatusID) !== -1) {
    if (FOSSubStatus.indexOf(NewSMELeadStatus.SubStatusID) !== -1) {
      setOpenHistory(true);
    }
  }

  const IsEnableAppointment = function (subStatusId, showPopup = true) {
    let _AppBtnName = [2005].indexOf(subStatusId) !== -1 ? "Reset" : "Set";
    setAppBtnName(_AppBtnName);
    // if (User.RoleId === 13 && [2, 115, 7, 1000, 117,131].indexOf(ProductId) !== -1 && FOSSubStatus.indexOf(subStatusId) !== -1) {
    if (FOSSubStatus.indexOf(subStatusId) !== -1) {
      setIsEnableAppointmentBtn(true);
    }
    else {
      setIsEnableAppointmentBtn(false);
    }
    // if ([2, 115, 7, 1000, 117,131].indexOf(ProductId) !== -1 && FOSSubStatus.indexOf(subStatusId) !== -1) {
    if (FOSSubStatus.indexOf(subStatusId) !== -1) {
      setviewButtonToAdmin(true);
    }
    else {
      setviewButtonToAdmin(false);
    }
    if (
      showPopup &&
      User.RoleId === 13
      && [2].indexOf(ProductId) !== -1
      && AppointmentStatusCondition.PopShowSubStatus.indexOf(subStatusId) !== -1
    ) {
      getFosDetail();
    }
  }

  let url = `${SV_CONFIG["matrixdashboard"][SV_CONFIG["environment"]]}/admin/FosAppointmentData?leadid=` + ParentLeadId;

  const SubmitPopUp = () => {
    if (NewSMELeadStatus.SubStatusID === 2008) // Annual health checkup issue substatus , need to call AHC 
    {
      setAHCFlag(true);
      setIsRenewalSubstaus(1);
    }
    else {
      setIsConfirmationPopupShow(true);
    }
  }

  const updateIsRenewalSubstaus = (flag) => {
    if (flag)
      setIsRenewalSubstaus(2);
    else
      setIsRenewalSubstaus(1);
  }

  const setSubstatusMessage = (SubStatusID) => {
    var flag = false;
    if (SubStatusID == 2265) {
      flag = true;
      setMessage(<>Send "Corporate policy - Deductible" WhatsApp to this customer<br />&<br /> Pitch Deductible on the next call </>);
    }
    if (SubStatusID == 2273 || SubStatusID == 2262 || SubStatusID == 2284) {
      flag = true;
      setMessage(<>Send "High premium - Deductible Discount" WhatsApp to this customer<br />&<br /> Pitch Deductible / port on the next call </>);
    }
    if (SubStatusID == 2266) {
      flag = true;
      setMessage(<>Send "PB Advantage & Deductible Discount" WhatsApp<br />&<br /> Pitch Deductible on the next call </>);
    }
    if (flag) {
      setIsSubStatusMessage(true);
      setTimeout(function () {
        setIsSubStatusMessage(false);
      }, 5000);
    }
  }

  useEffect(() => {
    if (IsRenewalSubstaus !== 0)
      SetLeadSMEStatus(true);
  }, [IsRenewalSubstaus])


  return (
    <>
      {([7, 115, 117, 3].indexOf(ProductId) == -1) &&
        <Grid item sm={12} md={12} xs={12}>
          <div className={`SmeLeadStatus ${props.className || ''}`}>
            <h3>{Product} Lead Status</h3>
            {/* <div className="expandmoreIcon">
            <ExpandMore
              onClick={handleToggle}
              style={{ transform: show ? "rotate(180deg)" : "rotate(0deg)" }}
            />
          </div> */}
            <>
              <Grid container spacing={2}>
                <SelectDropdown
                  name="leadId"
                  label="Lead"
                  value={NewSMELeadStatus.LeadID}
                  handleChange={handleChange}
                  fullWidth={true}
                  default
                  sm={12}
                  md={12}
                  xs={12}
                  options={leadOptions}
                  labelKeyInOptions="_all"
                  valueKeyInOptions="_all"
                />
                <SelectDropdown
                  name="StatusId"
                  label={`${Product} Status`}
                  value={NewSMELeadStatus.StatusID}
                  handleChange={handleChange}
                  fullWidth={true}
                  sm={12}
                  md={12}
                  xs={12}
                  options={filterSMEStatus}
                  labelKeyInOptions="StatusName"
                  valueKeyInOptions="StatusID"
                  disabled={IsStatusDisabled}
                />
                <SelectDropdown
                  name="SubStatusId"
                  label={`${Product} SubStatus`}
                  value={NewSMELeadStatus.SubStatusID}
                  handleChange={handleChange}
                  fullWidth={true}
                  sm={12}
                  md={12}
                  xs={12}
                  options={filterSMESubStatus}
                  labelKeyInOptions="SubStatusName"
                  valueKeyInOptions="SubStatusID"
                  show={handleShow(ShowSupplierPlan)}
                />
                <SelectDropdown
                  name="SubStatusReason"
                  label={`${Product} SubStatusReason`}
                  value={SubStatusReasonId}
                  handleChange={handleChange}
                  fullWidth={true}
                  sm={12}
                  md={12}
                  xs={12}
                  options={SubStatusReason}
                  labelKeyInOptions="Reason"
                  valueKeyInOptions="ReasonId"
                  show={IsSmeReason(NewSMELeadStatus.SubStatusID)}
                />
                {Array.isArray(properties) && ProductId !== 131 &&
                  <>
                    {properties.map((property) => {
                      return getInputByProperty(property);
                    })}
                  </>
                }
                {([2, 106, 118, 130].indexOf(ProductId) !== -1) &&
                  < Grid container item xs={12} sm={12}>
                    <b>Current SubStatus: &nbsp;  </b>
                    <span>
                      {NewSMELeadStatus.SubStatusName}
                    </span>
                  </Grid>
                }

                <Grid item xs={12} sm={6}>
                  <Button onClick={() => { (([2, 106, 118, 130].indexOf(ProductId) !== -1) && IsRenewal) ? SubmitPopUp() : SetLeadSMEStatus(true) }} variant="contained" color="secondary" fullWidth className="submitBtn">
                    Submit
                  </Button>
                </Grid>

                {/* <Grid item xs={12} sm={6}>
                <Button onClick={() => {  SetLeadSMEStatus(true) }} variant="contained" color="secondary" fullWidth className="button-bg">
                  Submit
                </Button>
              </Grid> */}

                <Grid item xs={12} sm={6}>
                  <Button onClick={() => { setOpenViewAllPopup(true) }} variant="outlined" color="primary" fullWidth className="button-txt">
                    View All
                  </Button>
                </Grid>
                {AppointmentDate && (viewButtonToAdmin || IsEnableAppointmentBtn) && <Grid item xs={12} sm={12}>
                  <Button onClick={() => { viewFosHistory() }} variant="outlined" color="primary" fullWidth className="button-txt">
                    View Appointment History
                  </Button>
                </Grid>}
              </Grid>
            </>
          </div>
        </Grid>
      }
      {
        openViewAllPopup &&
        <ViewAllSMELeadStatusModal
          Product={Product}
          data={StatusLog}
          open={openViewAllPopup}
          handleClose={() => { setOpenViewAllPopup(false) }}
        />
      }
      {/* {
        openEditInfoPopup &&
        <EditInfoPopUp
          open={openEditInfoPopup}
          handleClose={() => { setOpenEditInfoPopup(false); refreshLead(); }}
          LeadID={NewSMELeadStatus.LeadID}
          UpdateStatus={UpdateStatus}
        />
      } */}
      {
        OpenFOSLink && <FOSpopUp
          open={OpenFOSLink}
          view={FosAppointment}
          handleClose={() => {
            setOpenFOSLink(false);
          }}
          parentId={ParentLeadId}
          selectedSubStatus={NewSMELeadStatus.SubStatusID}
        />
      }
      {
        OpenHistory && <CommonModalPopUp
          open={OpenHistory}
          URL={url}
          className='AppointmentHistory'
          handleClose={() => {
            setOpenHistory(false);
          }}
          parentId={ParentLeadId}
          selectedSubStatus={NewSMELeadStatus.SubStatusID}
        />
      }
      {IsConfirmationPopupShow &&
        <ModalPopup open={IsConfirmationPopupShow} className="RenewalStatusPopup" handleClose={() => { setIsConfirmationPopupShow(false); }}>
          <div className="comboPolicyPopup">
            <h4>Do you wish to apply same substatus to other stacked renewal leads?</h4>
            <div>
              <button onClick={() => { updateIsRenewalSubstaus(true) }} >Yes, Proceed</button>
              <button onClick={() => { updateIsRenewalSubstaus(false) }}>Mark this lead only</button>
            </div>
          </div>
        </ModalPopup>
      }
      {IsSubStatusMessage &&
        <ModalPopup open={IsSubStatusMessage} className="RenewalMessageStatusPopup" handleClose={() => { setIsSubStatusMessage(false); }}>
          <div className="comboPolicyPopup">
            <h4>{Message} </h4>
          </div>
        </ModalPopup>
      }
      {AHCFlag &&
        <ErrorBoundary name="AHCPopup">
          <AHCPopup
            open={AHCFlag}
            handleClose={() => {
              setAHCFlag(false);
            }}
            LeadID={NewSMELeadStatus.LeadID}
          />
        </ErrorBoundary>
      }
    </>
  );

}

export default SmeLeadStatus;