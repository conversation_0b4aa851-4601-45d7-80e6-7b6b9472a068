import Axios from 'axios'
import { CONFIG, SV_CONFIG } from './../appconfig'
import rootScopeService from './rootScopeService';
import User from './user.service';
import { gaEventNames, gaEventTracker, getAsteriskToken } from "../helpers";
import { getSourcefromQuery } from "../helpers"

export const base_url = CONFIG.API_BASE_URL;
let axiosInstance = Axios.create({
    baseURL: base_url,
    header: {},
    // withCredentials: true 
});
if (!!CONFIG.COOKIE_LOGIN) {
    axiosInstance.defaults.withCredentials = true;
    axiosInstance.defaults.headers['Content-Type'] = 'application/json';
    axiosInstance.defaults.headers['Access-Control-Allow-Credentials'] = true
}
export const IsSourceCustomerWhatsapp = () => {
    try {
        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);
        let src = params.get("src");
        if (!src) {
            return false;
        }
        return (['customerwhatsapp', 'customersms'].indexOf(src.toLowerCase())) > -1 ? true : false
    }
    catch {
        return false;
    }


}

export const IsFOSCrossSell = () => {
    try {
        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);
        let src = params.get("src");
        let CrossSellProcess = params.get("CrossSellProcess");
        if (!src) {
            return false;
        }
        return ((['crosssell'].indexOf(src.toLowerCase())) > -1 && CrossSellProcess == 'true') ? true : false
    }
    catch {
        return false;
    }


}

export const getHeaders = (Headers = null, Source) => {

    if (IsSourceCustomerWhatsapp() === true) {

        Headers = {
            "encryptleadid": window.localStorage.getItem('EncryptedLeadId'),//,//'cSxgNtq5KIkz+OARHELtCQ==',//'zP7qJsUJarQbZHFCezGAPQ==',////localStorage.getItem('parentLeadId'),
            // "source": "customerwhatsapp",
            "source": getSourcefromQuery(),
            "token": localStorage.getItem('CustomerToken'),//'283973',//
            "content-type": "application/json",
            "ProcessId": User.GroupProcessId || 0
        }
    }
    else {
        Headers = Headers || {
            "AgentId": User.UserId || 0,
            "source": Source || "matrix",
            "Token": getAsteriskToken(),
            "Content-Type": "application/json",
            "ProcessId": IsFOSCrossSell() ? 0 : (User.GroupProcessId || 0)
        }
    }
    // console.log("Get Headers are",Headers)
    return Headers || {};
}



// SV
export const CALL_API = async (input) => {
    //input = {url, requestData, timeout, method,service}
    //let Token = userService().Token;
    let Token = 'cG9saWN5 YmF6YWFy';
    // set withcredentials true in these services // to be in lower case
    const secureServices =
        (window && window.SV_CONFIG_UNCACHED && Array.isArray(window.SV_CONFIG_UNCACHED.secureServices) && window.SV_CONFIG_UNCACHED.secureServices) ||
        ['agenttracker', 'customernotification', 'incentiveapi', 'commservice', '/fos/api/fos/', '/mrs/', '/onelead/api/', 'matrixcoreapi', 'matrixticket'];

    // Set defaults
    input.timeout = input.timeout || "m";   // timeout s/m/l/(number of millisec)
    input.method = input.method || "GET";
    input.cache = input.cache || false;
    input.service = input.service || "core";

    let URL, Headers;
    //Set service and url
    switch (input.service) {
        case "core":
            URL = SV_CONFIG["connectionString"][SV_CONFIG["environment"]] + input.url;
            Headers = {
                "Content-Type": "application/json",
                "authorization": Token  //SV_CONFIG["Authorization"][SV_CONFIG["environment"]]
            }
            break;
        case "custom":
            URL = input.url;
            Headers = input.Headers || {
                "Content-Type": "application/json"
            }
            break;
        case "myaccountapi":
            URL = SV_CONFIG["MyAccountAPI"][SV_CONFIG["environment"]] + input.url;
            Headers = {
                "Content-Type": "application/json",
                "Authorization": "cG9saWN5 YmF6YWFy"
            }
            break;
        case "BMSURL":
            //URL = "https://bmsv2testservice.policybazaar.com/api/User/GetServiceUrlForLead"
            URL = SV_CONFIG["BMSSERVICEURL"][SV_CONFIG["environment"]] + input.url;
            Headers = {
                "Content-Type": "application/json",
                "MatrixToken": SV_CONFIG["BMSToken"][SV_CONFIG["environment"]]
            }
            break;

        case "MatrixCoreAPI":

        
            URL = SV_CONFIG["MatrixCoreAPI"][SV_CONFIG["environment"]] + input.url;
            Headers = getHeaders(input.Headers, input.source);
            // console.log("Headers are",Headers);
            // let param=getQueryParam('src');
            // const url = new URL(window.location.href);
            // const params = new URLSearchParams(url.search);
            // let src=params.get("src")
            // // let src = query.get("src");
            // if(src && src.toLowerCase()=='customerwhatsapp')
            //     if(IsSourceCustomerWhatsapp()==true)

            //     {
            //         Headers =  {

            //             "encryptleadid":window.localStorage.getItem('EncryptedLeadId'),//,//'cSxgNtq5KIkz+OARHELtCQ==',//'zP7qJsUJarQbZHFCezGAPQ==',////localStorage.getItem('parentLeadId'),
            //             "source": "customerwhatsapp",
            //             "token": localStorage.getItem('CustomerToken'),//'283973',//
            //             "content-type": "application/json"
            //         }

            //     }
            //     else{
            //     Headers = input.Headers || {
            //         "AgentId": User.UserId,
            //         "Source": "MATRIX",
            //         "Token": localStorage.getItem('AsteriskToken'),
            //         "Content-Type": "application/json"
            //     }
            // }
            break;
        case "uploadFileToUrl":
            URL = SV_CONFIG["ComboxFileUpload"][SV_CONFIG["environment"]] + input.url;
            Headers = {
                ...input.headers,
                "content-Type": "multipart/form-data"
            }
            if (input.requestData) {
                let formData = new FormData();
                Object.keys(input.requestData).forEach(key => {
                    formData.append(key, input.requestData[key]);
                });
                input.formData = formData;
                input.requestData = null;
            }
            break;
        case "commonUploadFileToUrl":
            URL = SV_CONFIG["MatrixCoreAPI"][SV_CONFIG["environment"]] + input.url;
            Headers = {
                ...input.headers,
                "AgentId": User.UserId,
                "Token": localStorage.getItem('AsteriskToken'),
                "Content-Type": "multipart/form-data",
            }
            if (input.requestData) {
                let formData = new FormData();
                Object.keys(input.requestData).forEach(key => {
                    formData.append(key, input.requestData[key]);
                });
                input.formData = formData;
                input.requestData = null;
            }
            break;
        case "RenewalPaymentUploadFileToUrl":
            URL = SV_CONFIG["MatrixCoreAPI"][SV_CONFIG["environment"]] + input.url;
            Headers = {
                ...input.headers,
                "AgentId": User.UserId,
                "Token": localStorage.getItem('AsteriskToken'),
                "Content-Type": "multipart/form-data",
            }
            if (input.requestData) {
                let formData = new FormData();
                Object.keys(input.requestData).forEach(key => {
                    formData.append(key, input.requestData[key]);
                });
                input.formData = formData;
                input.requestData = null;
            }
            break;
        case "commService":
            URL = SV_CONFIG["commService"][SV_CONFIG["environment"]] + input.url;
            // Headers = {'Access-Control-Allow-Credentials': true ,...getHeaders(input.Headers)};
            Headers = {
                "Content-Type": "application/json"
            }
            break;
        case "MatrixTicket":
            URL = SV_CONFIG["feedback"][SV_CONFIG["environment"]] + input.url;
           
            Headers = input.Headers || {
                "Content-Type": "application/json"
            }
            break;
        default:
            URL = SV_CONFIG[input.service][SV_CONFIG["environment"]] + input.url;
            Headers = {
                "Content-Type": "application/json"
            }
            break;
    }


    // TIMEOUT
    let timeout = 0;
    try {
        try {
            timeout = SV_CONFIG["timeout"][input.timeout];
        } catch { }
        if (!timeout) {
            timeout = !isNaN(parseInt(input.timeout))
                ? parseInt(input.timeout)
                : 3000
        }
    }
    catch {
        timeout = 3000
    }
    if (!timeout) {
        timeout = 3000
    }

    // SECURITY: withCredentials
    try {
        let isSecure = false;
        const _inputService = input.service.toLowerCase();
        const _url = URL.toLowerCase();
        secureServices.forEach((service) => {
            if (
                _inputService === service
                || _url.includes(service)
            ) {
                isSecure = true;
            }
        });

        if (isSecure) {
            input.withCredentials = true;
            if (!input.Headers) {
                input.Headers = {};
            }
            Headers = {
                ...Headers,
                ...input.Headers,
                'Access-Control-Allow-Credentials': true
            };
        }
    }
    catch { }


    // Prepare reqData
    var reqData = {
        method: input.method,
        url: URL,
        headers: Headers,
        cache: input.cache,
        timeout,
        withCredentials: input.withCredentials
    };

    // Add payload if provided
    if (input.requestData !== undefined) { reqData.data = JSON.stringify(input.requestData); }
    if (input.formData) { reqData.data = input.formData; }
    let RequestTime = new Date();
    return new Promise((resolve, reject) => {

        axiosInstance(reqData)
            .then((res) => {
                if (input.acceptOnly200 === true && res.status !== 200) {
                    reject(res.statusText);
                    return;
                }
                //console.log(URL, res.data);
                resolve(res.data)
            })
            .catch((error) => {
                let gaErrorLabel = {};
                try {
                    gaErrorLabel = {
                        url: reqData.url,
                        method: reqData.method,
                        // errorToJSON: error.toJSON(),
                        ...reqData,
                        message: error.message,
                    }
                } catch (e) { }

                try { gaErrorLabel.RequestTime = RequestTime; } catch { }
                try { gaErrorLabel.ResponseTime = new Date(); } catch { }
                try { gaErrorLabel.status = error.response.status; } catch { }

                if (error.response) {
                    // The request was made and the server responded with a status code
                    // that falls out of the range of 2xx
                    try { gaErrorLabel.status = error.response.status; }
                    catch { }

                    reject(error.response)
                } else if (error.request) {
                    // The request was made but no response was received
                    // `error.request` is an instance of XMLHttpRequest
                    reject(error.request);
                } else {
                    // Something happened in setting up the request that triggered an Error
                    console.log('Error', error.message);
                    reject(error.message)
                }
                try {
                    gaErrorLabel = JSON.stringify(gaErrorLabel);
                    let status = error.response ? error.response.status : 0;
                    gaEventTracker(gaEventNames.apiError, gaErrorLabel, status);
                } catch { }
                console.log(error.config);
            })
    })

}

const checkUserGroup = (allowedGroups) => {
    if (!Array.isArray(allowedGroups)) return false;

    const userGroups = User?.UserGroupList || [];

    return userGroups.some(group => allowedGroups.includes(group.GroupId));
};


export const GetIframeURL = (input, ParentId, data) => {
    let url = '';
    switch (input) {
        case "Message":
            url = SV_CONFIG["communication"][SV_CONFIG["environment"]] + ParentId + "/" + User.UserId;
            break;
        case "calendar":
            var IsAgent = (User.RoleId === 13);
            var calendarInput = {
                CustomerId: (rootScopeService.getCustomerId() || 0),
                IsQueueAgent: false
            }
            window.sessionStorage.setItem("calendarInput", JSON.stringify(calendarInput));

            url = SV_CONFIG["calendar"][SV_CONFIG["environment"]]
                + "?x=" + (IsAgent ? User.UserId : 0)
                + "&y=" + IsAgent
                + "&z=" + ParentId
                + "&v=false&w=" + User.UserId
                + "&prd=" + rootScopeService.getProductId()
                + "&renewal=" + data.isRenewal
                //Todo
                + "&ServiceLead=" + data.ServiceLead;
            //+ "&ServiceLead=0";
            break;
        case "WhatsApp":
            url = SV_CONFIG["WhatsApp"][SV_CONFIG["environment"]] + "/" + data.LeadId + "/" + User.UserId;
            break;

        case "DocUpload":
            url = SV_CONFIG["Docsection"][SV_CONFIG["environment"]] + "#/DocRepositorySale/" + User.UserId + "/" + data.LeadId;
            break;
        case "CallTranfer":
            const productId = data.productId;

            const allowedProdForNewTransferPanel = Array.isArray(SV_CONFIG.allowedProdForNewTransferPanel) ? SV_CONFIG.allowedProdForNewTransferPanel : [];

            const allowedGroupForNewTransferPanel = Array.isArray(SV_CONFIG.allowedGroupForNewTransferPanel) ? SV_CONFIG.allowedGroupForNewTransferPanel : [];

            if (allowedProdForNewTransferPanel.includes(productId) || checkUserGroup(allowedGroupForNewTransferPanel)) {
                url = SV_CONFIG["dialerdashboard"][SV_CONFIG["environment"]] + "/admin/BlockAgent?" + data.url;
            } else {
                url = SV_CONFIG["matrixdashboard"][SV_CONFIG["environment"]] + "/admin/BlockAgent?" + data.url;
            }
            break;
        // case "RMCallTransfer":
        //     url = SV_CONFIG["matrixdashboard"][SV_CONFIG["environment"]] + "/admin/RMInsurerTransfer?" + data.url;
        //     break;
        case "Chat":
            url = `${SV_CONFIG["OldSVComponents"][SV_CONFIG["environment"]]}MatrixChat.htm#!/${data.EmployeeId}/${data.rid}`;
            break;
        case "ChatNew":
            url = `${SV_CONFIG["AgentChatUrl"]}/whatsapp`;
            break;
        case "ChatNewSME":
            url = `${SV_CONFIG["AgentChatUrl"]}/SME/whatsapp`;
            break;
        case "ChatUHNI":
            url = `${SV_CONFIG["AgentChatUrl"]}/sales/whatsapp`;
            break;
        case "PbTap":
            url = SV_CONFIG["PbTap"][SV_CONFIG["environment"]] + `${data.UserName}_${data.EmployeeId}/${data.TLName}'s Team?u=${User.UserId}`;
            break;
        case "ProposalDetails":
            url = SV_CONFIG["ProposalDetails"][SV_CONFIG["environment"]] + `${data.EncrptedUrl}`;
            break;
        case "ReadEmail":
            url = SV_CONFIG["ReadEmail"][SV_CONFIG["environment"]] + 'ViewNotification/' + data.id + '/1';
            break;
        case "QualityCriteria":
            url = "../qualityCriterias/" + data.userProduct;
            break;
        case "SmeQuoteHistory":
            url = `../SmeQuoteHistory/${data.leadId}/${data.productId}`;
            break;
        case "OldSVComponents":
            url = SV_CONFIG["OldSVComponents"][SV_CONFIG["environment"]];
            break;
        case "ViewQuotes":
            url = `../ViewQuotes/${data.encryptedURL}?allowAction=${data.allowAction}`;
            break;
        case "CallGallery":
            url = SV_CONFIG["matrixdashboard"][SV_CONFIG["environment"]] + `/admin/CallGallery?u=${User.UserId}`;
            break;
        case "CsatRating":
            url = "../CsatRating/";
            break;
        case "AddUpdateTicket":
            url = SV_CONFIG["ticket"][SV_CONFIG["environment"]] + data.encryptUrl
            break;
        case "PaymentFailedCases":
            url = "../PaymentFailedCases/";
            break;
        default:
            break;
    }
    return url;
}