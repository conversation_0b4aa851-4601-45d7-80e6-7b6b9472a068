/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from "react";
import ErrorBoundary from "../../../hoc/ErrorBoundary/ErrorBoundary";
import MarkImportant from "./MarkImportant";
import LastFiveLeads from "./LastFiveLeads";
import SuggestionBox from "./SuggestionBox";
import { Grid, useMediaQuery, useTheme } from "@mui/material";
import ActionButtons from "../Header/ActionButtons";
import AddToQueue from "../Main/actions/AddToQueue";
import AddCar from "./AddCar";
import rootScopeService from "../../../services/rootScopeService";
import { useDispatch, useSelector } from "react-redux";
import AddInfo from "./AddInfo";
import RenewalAddInfo from "./RenewalAddInfo";
import RenewalPehchan from "./RenewalPehchan";
import ClaimComments from "./ClaimComments";
import Comments from "../Main/Comments";
import CustomerHistory from "../Main/CustomerHistory";
import SendSoftcopy from "../Main/SendSoftcopy";
import Search from "../Header/Search";
import CustomerActivity from "./CustomerActivity";
import CustomerEmails from "./CustomerEmails";
import User from "../../../services/user.service";
import asyncComponent from "../../../hoc/asyncComponent";
import RenewalAddonLead from "./RenewalAddonLead";
import CustomerImportantInfo from "./CustomerImportantInfo";
import { SmeLeadStatusContainer } from "./SmeLeadStatusContainer";
import FosCitiesStores from "../Main/FosCitiesStores";
import TicketSummary from "./TicketSummary";
import FOSAssignmentAlert from "./FOSAssignmentAlert";
import DKDBanner from "./DKDBanner";
import RMDetails from "./RMDetails";
import { updateStateInRedux } from "../../../store/actions";
import { hideCallButton } from "../../../helpers";
import AppointmentChip from "./AppointmentChip";
import { ClaimDetailsPopup } from "../Main/Modals/ClaimDetailsPopup";
import { EMIOptionsPopup } from "../Main/Modals/EMIOptionsPopup";
import ManualPaymentLink from "../Main/Modals/ManualPaymentLink";
import {RenewalPaymentLinkTabs}  from "../Main/Modals/RenewalPaymentLinkTabs";
import { CONFIG } from "../../../appconfig";
import PreferredCommunicationMode from "./PreferredCommunicationMode";
import { ShowAddLeadbtn } from "../../../helpers/commonHelper";
import IncomeProofAvailability from "./IncomeProofAvailability";
import UtmCampaignBanner from "./UtmCampaignBanner";
import { getCurrentTalkTime, GetClaimStatus } from "../../../services/Common";
import { CALL_API } from "../../../../src/services/api.service";
import AgentAssistCTA from "../Main/AgentAssistCTA";
import { SV_CONFIG } from "../../../appconfig";
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import FOSPauseCall from "./FOSPauseCall";
import { AddToQueueErrorPopup } from "./Modals/AddToQueueErrorPopup";
import UpcomingEvents from "./UpcomingEvents";
import CustomerResearchComplete from "./CustomerResearchComplete";
import EndorsementPopup from "../Main/Modals/EndorsementPopup";

const CallButton = asyncComponent(() => {
  return import(/*webpackChunkName: "manualCalling"*/ './CallButton');
});


export const isNewTicketVisible = () => {
  try {
    if (SV_CONFIG.showNewTicketForAll) {
      return true;
    }
    return [7, 1000, 2, 106, 118, 130, 115, 117, 131, 101].includes(rootScopeService.getProductId());
  }
  catch {
    return false;
  }
}

export default function RightBlock(props) {
  const theme = useTheme();
  const dispatch = useDispatch();
  // const [IsVisibleSubstatus, setIsVisibleSubstatus] = useState(false);
  const [IsAppChipVisible, setIsAppChipVisible] = useState(false);
  const [LeadSource, setLeadSource] = useState('');
  const [OpenClaimsPopup, setOpenClaimsPopup] = useState(false);
  const [OpenEMIPopup, setOpenEMIPopup] = useState(false);
  const [OpenPaymentLink, setOpenPaymentLink] = useState(false);
  const [OpenRenewalPaymentLink, setOpenRenewalPaymentLink] = useState(false);
  let [IsAddCarVisible, setIsAddCarVisible] = useState(false);
  let [IsPehchanvisible, setIsPehchanvisible] = useState(false);
  let [IsLeadSetRenewal, setIsLeadSetRenewal] = useState(false);
  let [IsUtmCampaignBannerVisible, setIsUtmCampaignBannerVisible] = useState(false);
  let [ClaimDetails, setClaimDetails] = useState(undefined);
  let [UserConsent, setUserConsent] = useState(0);
  const [UTMMedium, setUTMMedium] = useState('');
  const [EndorsementOpen,setEndorsementOpen] = useState(false);
  

  const ShowManualLink = SV_CONFIG["ShowManualLink"];

  let [AllLeads, parentLeadId, noOfLeadCardsToShow, IsRenewal, IsClaimOpted, ReferralLeadId] = useSelector(state => {
    let { allLeads, parentLeadId, noOfLeadCardsToShow, IsRenewal, IsClaimOpted, ReferralLeadId } = state.salesview;
    return [allLeads, parentLeadId, noOfLeadCardsToShow, IsRenewal, IsClaimOpted, ReferralLeadId]
  });
  const ShowAssignCriticalComponents = useSelector(state => state.salesview.ShowAssignCriticalComponents);

  const isLargeDesktop = useMediaQuery(theme.breakpoints.up('lg'), {
    defaultMatches: true
  });
  const isMobile = useSelector(state => state.common.isMobile);
  const CustomerId = rootScopeService.getCustomerId();
  const [UTMDetails, setUTMDetails] = useState([]);
  const CallType = useSelector(state => state.salesview.CallType);
  const [showPredictiveCallIcon, setshowPredictiveCallIcon] = useState(false);
  const [LeadAssignedUser, setLeadAssignedUser] = useState(0);

  let HasFresh = 0;
  for (let key in AllLeads) {
    if (AllLeads[key].LeadSourceId !== 6) {
      HasFresh = 1
    }
  }

  const showAdditionalInfo = () => {
    if (rootScopeService.getProductId() === 117) {
      return SV_CONFIG["ShowMotorNeedAnalysis"]
    }
    else if (![2, 106, 118, 130, 117].includes(rootScopeService.getProductId()) || HasFresh === 1)
      return true
    return false
  }

  const showFOSPauseCallBtn = () => {
    return User.RoleId === 13
      && (
        (
          User.ProductList && User.ProductList.length > 0 && SV_CONFIG['ShowPauseCallingBtnPrduct'] && User.ProductList.some(product => SV_CONFIG['ShowPauseCallingBtnPrduct'].includes(product.ProductId))
        ) ||
        (
          User.GroupList && User.GroupList.length > 0 && SV_CONFIG['ShowPauseCallingBtnGrps'] && User.GroupList.some(grps => SV_CONFIG['ShowPauseCallingBtnGrps'].includes(grps.GroupId))
        )
      )
      && (User.GroupProcessId && parseInt(User.GroupProcessId) > 0 && parseInt(User.GroupProcessId) === 8)
  }

  const showCustomerResearchBtn = () => {
    return User && User.RoleId === 13
      && 
        (
          User.GroupList && User.GroupList.length > 0 && SV_CONFIG['ShowCustomerResearchGroups'] && User.GroupList.some(grps => SV_CONFIG['ShowCustomerResearchGroups'].includes(grps.GroupId))
        )
        &&
        (
          LeadAssignedUser && (LeadAssignedUser == User.UserId)
        )
        &&
        (
          UserConsent === 1
        )
        &&
        (
          UTMMedium === 'cxinterview'
        )
      
  }

  const checkVisibility = () => {
    //Add Car
    if (rootScopeService.getProductId() === 117) {
      AllLeads.forEach(lead => {
        //if (vdata.StatusId<3) {
        //    //$scope.IsVisible = true;
        //    rootScopeService.setContactedVisible(true);
        //}
        if (lead.StatusId >= 3) {
          setIsAddCarVisible(true);
          dispatch(updateStateInRedux({ key: 'IsAddCarVisible', value: true }))
        }
      });
    }

    
    if (AllLeads && Array.isArray(AllLeads) && AllLeads.length > 0) {
      GetUTMCampaignBanners(AllLeads);   // UtmCampaignBannerVisible

      const foundLead = AllLeads.find(lead => lead.LeadID === parentLeadId);
      if (foundLead) {
        const {
          LeadAssignedUser: assignedUser,
          UserConsent: newUserConsent,
          UTM_Medium: newUTMMedium
        } = foundLead;
    
        if (assignedUser !== LeadAssignedUser) {
          setLeadAssignedUser(assignedUser);
        }
    
        if ([0, 1].includes(newUserConsent) && newUserConsent !== UserConsent) {
          setUserConsent(newUserConsent);
        }
    
        if (newUTMMedium && newUTMMedium !== UTMMedium) {
          setUTMMedium(newUTMMedium);
        }
      }
    }
  }

  const IsPehchan = () => {
    Array.isArray(AllLeads) && AllLeads.forEach(lead => {
      if ([16, 2].indexOf(lead.RenewalSupplierId) !== -1) {
        setIsPehchanvisible(true);
      }
    });
  }
  const GetClaimStatusDetails = () => {
    if ([2, 106, 130, 117, 118].includes(rootScopeService.getProductId())) {
      let productid = 117;
      if ([2, 106, 130, 118].includes(rootScopeService.getProductId())) {
        productid = 2;
      }
      GetClaimStatus(CustomerId, productid).then((Result) => {
        setClaimDetails(Result);
      });
    }
  }

  const GetUTMCampaignBanners = (AllLeads) => {
    const UTMDetails = [];
    AllLeads.forEach((vdata, index) => {
      UTMDetails.push((({ Utm_campaign, Utm_source, ProductID, LeadID, UTM_Medium, LeadSource }) =>
        ({ Utm_campaign, Utm_source, ProductID, LeadID, UTM_Medium, LeadSource }))(vdata));
    })

    const input =
    {
      url: `coremrs/api/LeadDetails/GetUTMCampaignList`,
      method: "POST",
      service: "MatrixCoreAPI",
      requestData: UTMDetails
    }
    CALL_API(input).then((result) => {
      if (result) {
        setIsUtmCampaignBannerVisible(true);
        setUTMDetails(result);
      }
    });
  }

  const GetCreateBenchmarkUrl = () => {
    const input =
    {
      url: `coremrs/api/LeadDetails/GetBenchmarkUrl/` + parentLeadId,
      method: "GET",
      service: "MatrixCoreAPI"
    }
    CALL_API(input).then((result) => {
      if (result) {
        window.open(result);
      }
    });
  }

  const checkVisibilitySubstatus = () => {
    // setIsVisibleSubstatus(false);
    setIsAppChipVisible(false);
    setLeadSource(false);
    let _IsMotorRenewal = false;
    let _IsCommercialRenewal = false;
    let _IschipVisible = false;
    let _IsLeadSetRenewal = false;
    // let IsActive=IsLeadDataSetActive(AllLeads);
    Array.isArray(AllLeads) && AllLeads.forEach(lead => {
      if ((lead.StatusMode == 'P' && lead.StatusId > 2)) {
        _IschipVisible = true;

      }
      if (lead.LeadSourceId == 6) {
        _IsLeadSetRenewal = true
        setIsLeadSetRenewal(true);
      }

      if ((parentLeadId == lead.LeadID && lead.LeadSourceId == 6)) {
        setLeadSource('renewal');
      }
      if (rootScopeService.getProductId() == 117 && lead.LeadSourceId == 6) {
        _IsMotorRenewal = true;
      }
      if (rootScopeService.getProductId() == 139 && lead.LeadSourceId == 6) {
        _IsCommercialRenewal = true;
      }
    });


    if (_IschipVisible && !_IsMotorRenewal && !_IsCommercialRenewal) {
      setIsAppChipVisible(true);
    }

  }

  const ShowClaimDetails = () => {
    setOpenClaimsPopup(true);
  }
  const ShowEndorsementData = () => {
    setEndorsementOpen(true);
  }
  const ShowEMIDetails = () => {
    setOpenEMIPopup(true);
  }
  const ShowPaymentLink = () => {
    setOpenPaymentLink(true);
  }
  const ShowRenewalPaymentLink = () => {
    setOpenRenewalPaymentLink(true);
  }

  useEffect(() => {
    checkVisibility();
    checkVisibilitySubstatus();
    IsPehchan();
    GetClaimStatusDetails();
  }, [AllLeads])

  useEffect(() => {
    let callduration = getCurrentTalkTime();
    if (((CallType && CallType == "PDOB") || localStorage.getItem("calltype") == "PDOB")
      && (window.localStorage.getItem("onCall") === "true") && callduration < 10) {
      setshowPredictiveCallIcon(true);
      let timer = setTimeout(() => setshowPredictiveCallIcon(false), 10 * 1000);
      return () => {
        clearTimeout(timer);
      };
    }
    else {
      setshowPredictiveCallIcon(false);
    }
  }, [CallType])

  let Ispriority = rootScopeService.getPriority();


  return (
    <>
      <div className="rightSection">
        {showPredictiveCallIcon && <>
          <h3 className="fadeInRight">
            Predictive Call<br />
            Your Customer is on Call, Say Hello !!
          </h3>
        </>
        }
        <Grid container>

          {!isMobile && <>
            {Ispriority &&
              <ErrorBoundary name="AddToQueue">
                {(ShowAssignCriticalComponents || (SV_CONFIG && SV_CONFIG["DisableCriticalAcCondForATQ"]))
                  && props.Isshow && ShowAddLeadbtn() ? <AddToQueue hideText={IsAddCarVisible} /> : null}
              </ErrorBoundary>
            }
            {
              (!User.IsProgressive && <ErrorBoundary name="CallButton">
                {(ShowAssignCriticalComponents || (SV_CONFIG && SV_CONFIG["DisableCriticalAcCondForCallBtn"]))
                  && props.Isshow && !hideCallButton(User) ? <CallButton />
                  : null}
              </ErrorBoundary>)
            }
            <ErrorBoundary name="Addcar">
              {props.Isshow && IsAddCarVisible ? <AddCar /> : null}
            </ErrorBoundary>

            <ErrorBoundary name="MarkImportant">
              {props.Isshow && Ispriority ? <MarkImportant ParentLeadId={parentLeadId} /> : null}
            </ErrorBoundary>

            <ErrorBoundary name="searchbox">
              {!props.Isshow && Ispriority ? <Search /> : null}
            </ErrorBoundary>
            {Ispriority ?
              <ErrorBoundary name="LastFiveLeads">
                <LastFiveLeads />
              </ErrorBoundary>
              : null
            }
            {showFOSPauseCallBtn() &&
              <ErrorBoundary name="FOSPauseCall">
                <FOSPauseCall />
              </ErrorBoundary>
            }
            <ErrorBoundary name="ActionButtons">
              <ActionButtons isLeadOpen={props.Isshow} />
            </ErrorBoundary>
          </>}
          <ErrorBoundary name="Income Docs">
            <IncomeProofAvailability />
          </ErrorBoundary>

        </Grid>


        <ErrorBoundary name="UpcomingEvents">
          <UpcomingEvents />
        </ErrorBoundary>
        <ErrorBoundary name="RMDetails">
          <RMDetails />
        </ErrorBoundary>

        <ErrorBoundary name="PreferredCommunicationMode">
          {Boolean(!IsRenewal && ([2, 106, 118, 130].indexOf(rootScopeService.getProductId()) !== -1)) && <PreferredCommunicationMode />}
        </ErrorBoundary>



        {!showCustomerResearchBtn() ? ShowAssignCriticalComponents && (SV_CONFIG && Array.isArray(SV_CONFIG.FOSProductConfig) && SV_CONFIG.FOSProductConfig.includes(rootScopeService.getProductId()))
              && IsAppChipVisible &&  <ErrorBoundary name="AppointmentChip"> <AppointmentChip LeadSource={LeadSource} ProductId={rootScopeService.getProductId()} IsLeadSetRenewal={IsLeadSetRenewal} />
        </ErrorBoundary> : null}
        {showCustomerResearchBtn() ?
          <ErrorBoundary name="CustomerResearchComplete">
            <CustomerResearchComplete LeadID = {rootScopeService.getLeadId()} ProductID = {rootScopeService.getProductId()} GroupId = {User?.GroupId} />
          </ErrorBoundary> : null
        }

        <ErrorBoundary name="FosAssignment">
          <FOSAssignmentAlert />
        </ErrorBoundary>

        <ErrorBoundary name="DKDBanner">
          <DKDBanner />
        </ErrorBoundary>

        {isNewTicketVisible() && <ErrorBoundary name="TicketSummary">
          <TicketSummary />
        </ErrorBoundary>}
        <ErrorBoundary name="CustomerImportantInfo">
          <CustomerImportantInfo />
        </ErrorBoundary>


        {([2].indexOf(rootScopeService.getProductId()) !== -1 && IsRenewal === 1) &&
          <div className={`shopse`}>
            <button className='cursorPointer' onClick={ShowEMIDetails}>Shopse / Axio EMI Eligibility<img className='clickableImg' src={CONFIG.PUBLIC_URL + "/images/salesview/external-link.svg"} alt="Link" /></button>
          </div>}
        {([2, 106, 130, 117, 118].indexOf(rootScopeService.getProductId()) != -1) &&
          <div className={`Foscityofflinestore`}>
            {ClaimDetails && ClaimDetails.CustomerId != 0 && <>
              <button className={`cursorPointer ${ClaimDetails.ClaimStatus && ClaimDetails.ClaimStatus != 'Rejected' ? 'positive' : 'yes'}`} onClick={ShowClaimDetails}>
                <span>Claim - {ClaimDetails.ClaimStatus}
                  {ClaimDetails.ClaimedPercentage > 0 && ClaimDetails.ClaimedPercentage < 100 && <>
                    <br></br>Claim Received - {ClaimDetails.ClaimedPercentage}%
                  </>}
                </span>
              </button>

            </>
            } {ClaimDetails && ClaimDetails.CustomerId == 0 && <>
              <button className={`cursorPointer ${IsClaimOpted && IsClaimOpted == true ? 'yes' : ''}`} onClick={ShowClaimDetails}>
                <span>{IsClaimOpted && IsClaimOpted == true ? "Claim Opted - Yes" : "Claim Details"}</span> <OpenInNewIcon />
              </button>

            </>
            }
          </div>}
        {[117].includes(rootScopeService.getProductId()) && IsRenewal && IsRenewal == 1 && ReferralLeadId && ReferralLeadId > 0 ? 
          <div className={`Foscityofflinestore`}>
            <button className={`cursorPointer`} onClick={ShowEndorsementData}>
              <span>Endorsement Details</span>
            </button>
          </div>
          : <></>
        }
        {([2].indexOf(rootScopeService.getProductId()) !== -1 && IsRenewal === 1 && ShowManualLink === 1) &&
          <div className={`Foscityofflinestore`}>
            <button className='cursorPointer' onClick={ShowPaymentLink}>Create Payment Link<img className='clickableImg' src={CONFIG.PUBLIC_URL + "/images/salesview/external-link.svg"} alt="Link" /></button>
          </div>}
        {!!((rootScopeService.getProductId() === 131) && AllLeads && AllLeads[0] && AllLeads[0].SubProductTypeId && (AllLeads[0].SubProductTypeId === 1)) &&
          <div className={`Foscityofflinestore`}>
            <button className='cursorPointer' onClick={GetCreateBenchmarkUrl}>Create Benchmark<img className='clickableImg' src={CONFIG.PUBLIC_URL + "/images/salesview/external-link.svg"} alt="Link" /></button>
          </div>}
        {([2].indexOf(rootScopeService.getProductId()) !== -1 && IsRenewal === 1 && ShowAssignCriticalComponents && SV_CONFIG && Array.isArray(SV_CONFIG.RenewalPaymentLinkAgent) && SV_CONFIG.RenewalPaymentLinkAgent.includes(User.UserId)) &&
          <div className={`Foscityofflinestore`}>
            <button className='cursorPointer' onClick={ShowRenewalPaymentLink}>Create Renewal Payment Link<img className='clickableImg' src={CONFIG.PUBLIC_URL + "/images/salesview/external-link.svg"} alt="Link" /></button>
          </div>}
        {([131].indexOf(rootScopeService.getProductId()) !== -1 || ([2].indexOf(rootScopeService.getProductId()) !== -1 && !IsRenewal)) &&
          <ErrorBoundary name="AgentAssistCTA">
            <AgentAssistCTA
              ProductId={rootScopeService.getProductId()}
            />
          </ErrorBoundary>
        }

        <ErrorBoundary name="FosCityStores">
          <FosCitiesStores />
        </ErrorBoundary>

        <ErrorBoundary name="UtmCampaignBanner">
          {IsUtmCampaignBannerVisible && <UtmCampaignBanner data={UTMDetails} />}
        </ErrorBoundary>
        <ErrorBoundary name="SuggestionBox">
          <SuggestionBox data={AllLeads} />
        </ErrorBoundary>
        <ErrorBoundary name="RenewalAddonLead">

          {Boolean(props.Isshow && IsRenewal && ([2, 106, 118, 130].indexOf(rootScopeService.getProductId()) !== -1)) && <RenewalAddonLead />}
        </ErrorBoundary>
        {((noOfLeadCardsToShow !== 1 || !isLargeDesktop) && props.Isshow) &&
          <>
            <ErrorBoundary name="Comments">
              <Comments />
            </ErrorBoundary>
          </>
        }
        {!((noOfLeadCardsToShow < 3 && isLargeDesktop) || (noOfLeadCardsToShow < 2))
          && props.Isshow &&
          <>
            {/* <Grid item sm={4} md={4} xs={12}> */}
            < ErrorBoundary name="CustomerHistory">
              <CustomerHistory />
            </ErrorBoundary>
            {/* </Grid> */}
          </>
        }


        <ErrorBoundary name="Fresh Lead Status">
          {((User.RoleId !== 13) ||
            (User.RoleId === 13 && (([131, 101].includes(rootScopeService.getProductId())) || (IsRenewal && [2, 106, 118, 130].includes(rootScopeService.getProductId()))))
          ) ? <SmeLeadStatusContainer /> : null}
        </ErrorBoundary>

        {/* </Grid> */}
        <ErrorBoundary name="RenewalPehchan ">
          {props.Isshow && IsPehchanvisible && IsRenewal === 1 && [2, 106, 118, 130].includes(rootScopeService.getProductId()) && <RenewalPehchan />}
        </ErrorBoundary>
        <ErrorBoundary name="RenewalAddInfo ">
          {props.Isshow && IsRenewal === 1 && [2, 106, 118, 130].includes(rootScopeService.getProductId()) && <RenewalAddInfo />}
        </ErrorBoundary>
        <ErrorBoundary name="AddInfo ">
          {props.Isshow && showAdditionalInfo() && <AddInfo disabled={!ShowAssignCriticalComponents} />}
        </ErrorBoundary>

        <ErrorBoundary name="SendSoftCopy">
          {props.Isshow && <SendSoftcopy />}
        </ErrorBoundary>
        <ErrorBoundary name="ClaimComments ">
          {props.Isshow ? <ClaimComments /> : null}
        </ErrorBoundary>
        <ErrorBoundary name="CustomerActivity ">
          {(SV_CONFIG && Array.isArray(SV_CONFIG['CustomerActivityProductConfig']) && SV_CONFIG['CustomerActivityProductConfig'].includes(rootScopeService.getProductId())) &&
            props.Isshow ? <CustomerActivity /> : null}
        </ErrorBoundary>
        {/* <ErrorBoundary name="CustomerEmail ">
          {props.Isshow ? <CustomerEmails /> : null}
        </ErrorBoundary> */}


        <ErrorBoundary name="ClaimDetailsPopup">
          <ClaimDetailsPopup
            open={OpenClaimsPopup}
            handleClose={() => {
              setOpenClaimsPopup(false);
            }}
          />
        </ErrorBoundary>
        <ErrorBoundary name="EndorsementPopup">
          <EndorsementPopup
            open={EndorsementOpen}
            handleClose={() => {
              setEndorsementOpen(false);
            }}
          />
        </ErrorBoundary>
        <ErrorBoundary name="EMIOptionsPopup">
          <EMIOptionsPopup
            open={OpenEMIPopup}
            handleClose={() => {
              setOpenEMIPopup(false);
            }}
          />
        </ErrorBoundary>
        <ErrorBoundary name="ManualPaymentLink">
          <ManualPaymentLink
            open={OpenPaymentLink}
            handleClose={() => {
              setOpenPaymentLink(false);
            }}
          />
        </ErrorBoundary>
        
        <ErrorBoundary name="RenewalPaymentLink">
          <RenewalPaymentLinkTabs
            open={OpenRenewalPaymentLink}
            handleClose={() => {
              setOpenRenewalPaymentLink(false);
            }}
          />
        </ErrorBoundary>

        <ErrorBoundary name="AddToQueueErrorPopup">
          <AddToQueueErrorPopup />
        </ErrorBoundary>
      </div>
    </>
  );

}
