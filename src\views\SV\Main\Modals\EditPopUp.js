import React, { useEffect, useState } from "react";
import ModalPopup from '../../../../components/Dialogs/ModalPopup';
import { Grid, Switch, TextField } from "@mui/material";
import { SelectDropdown } from "../../../../components";
import TextInput from "../../../../components/TextInput";
import { Autocomplete } from '@mui/material';
import masterService from "../../../../services/masterService";
import rootScopeService from "../../../../services/rootScopeService";
import { CALL_API } from "../../../../services";
import { useSnackbar } from "notistack";
import { useSelector, useDispatch } from "react-redux";
import User from "../../../../services/user.service";
import { SetCustomerComment, SetLeadAudit } from "../../../../services/Common";
import { default as Data } from "../../../../../src/assets/json/StaticData";
import { SV_CONFIG } from "../../../../appconfig";
import { FamilyTypes, SumInsuredTypeOptions } from "./../../../SV/Main/helpers/Constants";
import { ErrorBoundary } from "../../../../hoc";
import SMEAssociationPopUp from "../../Main/Modals/SMEAssociationPopUp"
import { DatePicker as DatePickerMUI } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs from "dayjs";
let invalids = [undefined, null, 0, "", "0", 'Invalid Date'];
const DatePicker = (props) => {
    let { show = true, name, label, value, handleChange } = props;
    let { format = "dd/MM/yyyy", disableToolbar = false, disabled = false } = props;
    let { sm = 6, md = 4, xs = 12 } = props;

    if (!show) return null;
    return (
        <Grid item sm={sm} md={md} xs={xs}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePickerMUI
                    disableToolbar={disableToolbar}
                    inputFormat={format}
                    id={name}
                    inputVariant="outlined"
                    label={label}
                    value={value ? new Date(value) : value}
                    fullWidth
                    disabled={disabled}
                    onChange={(newValue) => handleChange({ target: { name, value: newValue } })}
                    renderInput={(params) => (
                        <TextField
                            {...params}
                            variant="outlined"
                            name={name}
                            fullWidth={true}
                            InputProps={{
                                ...params.InputProps,
                                readOnly: true,
                            }}
                        />
                    )}
                    autoOk={true}
                    leftArrowButtonProps={{ color: "inherit" }}
                    rightArrowButtonProps={{ color: "inherit" }}
                />
            </LocalizationProvider>
        </Grid>
    )
}
const MonthList = [
    { Name: 'January', Value: 1 },
    { Name: 'February', Value: 2 },
    { Name: 'March', Value: 3 },
    { Name: 'April', Value: 4 },
    { Name: 'May', Value: 5 },
    { Name: 'June', Value: 6 },
    { Name: 'July', Value: 7 },
    { Name: 'August', Value: 8 },
    { Name: 'September', Value: 9 },
    { Name: 'October', Value: 10 },
    { Name: 'November', Value: 11 },
    { Name: 'December', Value: 12 }
];
const genderOptions = ["Male", "Female"];

export const EditPopUp = (props) => {
    let { lead } = props;
    let [selectedCity, setSelectedCity] = useState({});
    let [Cities, setCities] = useState([selectedCity]); //more cities from api
    let [SubProducts, setSubProducts] = useState([]); //more cities from api
    let [Occupations, setOccupations] = useState([]);
    let ProductId = rootScopeService.getProductId();
    const [showPolicyExpDateEdit, setshowPolicyExpDateEdit] = useState(false);
    const [Association, setAssociation] = useState([]);
    const [LeadInfo, setLeadInfo] = useState([]);
    let [NewLead, setNewLead] = useState({
        ...lead,
        DOB: lead.Age.split(" ")[0],
        DOBdate: Number(lead.Age.split(" ")[0].split("-")[0]).toString(),
        DOBmonth: Number(lead.Age.split(" ")[0].split("-")[1]).toString(),
        DOByear: Number(lead.Age.split(" ")[0].split("-")[2]).toString(),
        PolicyExpiryDate: (lead.PolicyExpiryDate && lead.PolicyExpiryDate != -62135616600000) ?
        dayjs(lead.PolicyExpiryDate).format('YYYY-MM-DD') :
            null,
    });
    const { enqueueSnackbar } = useSnackbar();
    let [leads, PrimaryLeadId, ParentLeadId] = useSelector(({ salesview }) => [salesview.allLeads, salesview.primaryLeadId, salesview.parentLeadId]);
    const [OldInfo, setOldInfo]=useState([]);
    const [ShowAssociationPopUp, setShowAssociationPopUp] = useState(false);
    const [showOtherMedicalExtention, setshowOtherMedicalExtention] = useState(false);

    // Function to mask email address
    const maskEmail = (email) => {
        if (!email || typeof email !== 'string') return email;

        const emailParts = email.split('@');
        if (emailParts.length !== 2) return email; // Invalid email format

        const localPart = emailParts[0];
        const domain = emailParts[1];

        if (localPart.length <= 2) {
            // If local part is too short, just return original
            return email;
        }

        const firstChar = localPart[0];
        const lastChar = localPart[localPart.length - 1];
        const middleLength = localPart.length - 2;
        const maskedMiddle = '*'.repeat(middleLength);

        return `${firstChar}${maskedMiddle}${lastChar}@${domain}`;
    };

    let TransitTypes = Data.TransitTypes;
    let marineSubProduct = 13;
    let OccupationsList = [];
    try {
        OccupationsList = (Occupations && Array.isArray(Occupations)) ? Occupations.filter((o) => o.SubProductTypeId === (NewLead.SubProductTypeId ? NewLead.SubProductTypeId.toString() : 0)) : [];
    }
    catch {
        OccupationsList = []
    }
    let PolicyTypes = Data.PolicyType.SME;
    let MedicalExtensions = Data.MedicalExtensions;
    let ShipmentTypes = Data.ShipmentTypes;
    const ValidateforRenewal = () => {
        var isRenewalAgent = false;
        var allowedTAT = Math.floor((lead.OfferCreatedON + (5 * 86400000))); //Add 5 days to TAT
        var currentTime = Math.floor(Date.now());
        var UserBUMappingList = User.UserBUMapping;
        if (UserBUMappingList !== undefined
            && UserBUMappingList !== ""
            && parseInt(lead.StatusId, 10) >= parseInt(13, 10) //Booked Lead
            && lead.StatusMode === "P" //Positive status Leads
            && (allowedTAT) > (currentTime)
        ) {
            UserBUMappingList.forEach(function (val, key) {
                if (val.IsRenewal && ([106, 118, 130, 2].indexOf(val.ProductId) !== -1)) {
                    isRenewalAgent = true;
                }
            });
        }
        return isRenewalAgent;
    }

    const GetCityList = () => {
        masterService.getCities().then(function (result) {
            setCities(prevState => [...prevState, ...result]);

            let currentCity = result.filter((c) => c.CityStateName.split(" ").join("") == `${lead.City.split(" ").join("")}`)[0];
            setSelectedCity(currentCity);

        });
    }

    const getLeadDetailsByProductService=(requestData) => {
        const input = {
            url: `api/LeadDetails/GetLeadInfoByLeadId?LeadId=${requestData.LeadId}`,
            method: 'GET',
            service: 'MatrixCoreAPI'
        }
        return CALL_API(input).then(response => {
            return response;
        });
    }
    const GetLeadDetailsByProduct = () => {
        const reqData = {
            LeadId: NewLead.LeadID
        }
        getLeadDetailsByProductService(reqData)
            .then(function (response) {
                if (response) {
                    setLeadInfo(response);
                    setOldInfo(response);
                }
            })
            .catch(() => {

            })
    }

    useEffect(() => {
        if (props.open) {
            setNewLead({
                ...lead,
                DOB: lead.Age.split(" ")[0],
                DOBdate: Number(lead.Age.split(" ")[0].split("-")[0]).toString(),
                DOBmonth: Number(lead.Age.split(" ")[0].split("-")[1]).toString(),
                DOByear: Number(lead.Age.split(" ")[0].split("-")[2]).toString(),
                PolicyExpiryDate: (lead.PolicyExpiryDate && lead.PolicyExpiryDate != -62135616600000) ?
                    dayjs(lead.PolicyExpiryDate).format('YYYY-MM-DD') :
                    null,
            })
            if (Cities.length === 1) GetCityList();
            //GetLeadBookingDate();

            if (ProductId == 131) {
                GetLeadDetailsByProduct();
                     
                masterService.getSubProductByProductID(ProductId).then(function (response) {
                    setSubProducts(response);
                });
                if (Occupations && Occupations.length == 0) {
                    masterService.getSMEOccupationList().then((response) => {
                        setOccupations(response);
                    });
                }
                if (Association && Association.length == 0) {
                    GetDoctorAssociations();
                }
                    
                
            }
            try {
                if (ProductId == 117 &&
                    (lead?.PolicyExpiryDate == -62135616600000 || lead?.PolicyExpiryDate == '') &&
                    !lead?.IsBrandNewCar &&
                    ParentLeadId == lead?.LeadID) {
                    setshowPolicyExpDateEdit(true);
                }
                else {
                    setshowPolicyExpDateEdit(false);
                }
            } catch (error) {
                setshowPolicyExpDateEdit(false);
            }
        }
    }, [props.open])
    const handleChange = (event) => {
        let name = event.target.name;
        let value = event.target.value;

        if (name === 'EmailID') {
            value = value.trim();
        }
        if(name==='AssociationId')
        {
            setLeadInfo({...LeadInfo,[name]:value?.Id});
            setShowAssociationPopUp(AssociationIdPopUp(value?.Id));
        }
        if (name === 'MedicalExtension') {
            setLeadInfo({ ...LeadInfo, [name]: value });
            if (value == "Other") {
                setshowOtherMedicalExtention(true);
            }
            else {
                setshowOtherMedicalExtention(false);
            }

        }
        if (name === 'CompanyName') {
            setNewLead({ ...NewLead, [name]: value });
            setLeadInfo({ ...LeadInfo, [name]: value });
        }
        if (!['City', 'FitPassStatus','AssociationId','MedicalExtension'].includes(name)) {
            setNewLead({ ...NewLead, [name]: value });
            setLeadInfo({...LeadInfo, [name]: value});
        }

        if (name === 'City' && value) {
            setNewLead({
                ...NewLead,
                CityId: value.CityID,
                City: value.CityStateName,
                StateId: value.StateID,
                State: value.StateName,
            });
            setSelectedCity(value);
        }
      
        if (name === 'FitPassStatus') {
            let updatedValue = (NewLead.FitPassStatus === 2 ? 1 : 2)
            setNewLead({ ...NewLead, [name]: updatedValue });
        }
        if(name=='PolicyExpiryDate'){
            value = dayjs(value).format('YYYY-MM-DD')
            setNewLead({ ...NewLead, [name]: value });
        }
       

    }
    const GetDoctorAssociations = () => {
        try {
            masterService.GetDoctorAssociations().then(function (association) {
                if (association)
                    setAssociation(association);
            });
        }
        catch
        {
            return [];
        }
    }
    function validateEmail(email) {
        if ([131, 2, 117].indexOf(ProductId) !== -1) {
            const re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            return re.test(email);
        }
        return true;
    }

    function validatePolicyExpiryDate(date) {
        // Check if PolicyExpiryDate is required for the current ProductId
        const requiredProducts = [117];
        if (requiredProducts.indexOf(ProductId) !== -1) {
            if (!date) return false; // Required for these products
        }
        return true; // Not required for other products
    }

    const SaveLeadEditForRenewal = async function () {

        if (NewLead.UpSellLeadId == 0) {
            return false;
        }

        var numericLead = /^\d*[0-9](|.\d*[0-9]|,\d*[0-9])?$/;

        if ((String(NewLead.UpSellLeadId).length !== 0) && ((!numericLead.test(NewLead.UpSellLeadId)) || (String(NewLead.UpSellLeadId).length > 15))) {
            enqueueSnackbar("Invalid RenewalLeadId(enter numeric values upto 15 digits only).", { variant: 'error', autoHideDuration: 3000, style: { whiteSpace: 'pre-line' } });
            return false;
        }
        var isvalid = false;

        leads.forEach((vdata, key) => {
            if (vdata.LeadID == NewLead.UpSellLeadId) {
                isvalid = true;
            }
        });
        if (isvalid == false) {
            enqueueSnackbar("Please enter the valid lead ID", { variant: 'error', autoHideDuration: 3000, style: { whiteSpace: 'pre-line' } });
            return false;
        }

        // Validate the Renewal Lead
        let res = true;
        let resmsg = "";
        let input = {
            url: `coremrs/api/LeadDetails/ValidateLead/` + NewLead.LeadID + `/` + NewLead.UpSellLeadId,
            method: 'GET', service: 'MatrixCoreAPI'
        };

        let resu = await CALL_API(input)
        if (resu) {
            res = resu.StatusCode;
            resmsg = resu.StatusMessage;

        }
        if (!res) {
            enqueueSnackbar(resmsg, { variant: 'error', autoHideDuration: 3000, style: { whiteSpace: 'pre-line' } });
            return false;
        }

        // Update the Renewal Lead
        var reqData = {
            LeadID: NewLead.LeadID,
            ProductId: rootScopeService.getProductId(),
            Type: 0,
            UpSellLeadId: NewLead.UpSellLeadId
        };
        input = {
            url: `coremrs/api/LeadDetails/UpdateLeadDetails/`,
            method: 'POST', service: 'MatrixCoreAPI',
            requestData: reqData
        };
        CALL_API(input).then((resultData) => {
            if (resultData && resultData.IsSaved) {
                var Comment = "";
                var reqDataAudit = [];
                leads.forEach((vdata, key) => {
                    if (vdata.LeadID == reqData.LeadID) {
                        if (NewLead.UpSellLeadId != vdata.UpSellLeadId) {

                            Comment += "RenewalLeadId changed from " + vdata.UpSellLeadId + " to " + NewLead.UpSellLeadId + ". ";

                            reqDataAudit.push({
                                LeadId: NewLead.LeadID.toString(),
                                AgentId: User.UserId,
                                SectionName: "LeadEdit",
                                Field: "126",
                                OldValue: vdata.UpSellLeadId.toString(),
                                NewValue: NewLead.UpSellLeadId.toString(),
                                ProductId: rootScopeService.getProductId()
                            });
                        }
                    }
                });

                if (reqDataAudit.length > 0) {
                    SetLeadAudits(reqDataAudit)
                }
                let reqData1 = {
                    CustomerId: rootScopeService.getCustomerId(),
                    ProductId: rootScopeService.getProductId(),
                    ParentLeadId: reqData.LeadID,
                    PrimaryLeadId: PrimaryLeadId,
                    UserId: User.UserId,
                    Comment: Comment,
                    EventType: 12
                };
                SetCustomerComment(reqData1).then((result) => {
                    if (result) {
                        enqueueSnackbar("Lead Edited Successfully", { variant: 'success', autoHideDuration: 3000, });
                        props.getLeads();
                        props.handleClose();
                    }
                }, function () {
                    enqueueSnackbar("Error Updating History", { variant: 'error', autoHideDuration: 3000, });
                });
            }
            else {
                props.getLeads();
                enqueueSnackbar("Unable to Update", { variant: 'error', autoHideDuration: 3000, });
            }
        })
    }

    const SetLeadAudits = (requestData) => {
        SetLeadAudit(requestData).then((result) => {
            enqueueSnackbar("Lead Audit Details Saved", { variant: 'success', autoHideDuration: 3000, });
        }, function () {
            enqueueSnackbar("Lead Audit not saved", { variant: 'error', autoHideDuration: 3000, });
        })
    }

    const SaveLeadEdit = async function () {
        let alertMsg = "";
        let DOB;
        var validateDob = true;

        if (rootScopeService.getProductId() == 117 || rootScopeService.getProductId() == 131) {
            validateDob = false;
        }
        if (validateDob) {
            if (NewLead.DOBdate == "" || NewLead.DOBmonth == "" || NewLead.DOByear == "") {
                alertMsg = alertMsg + "Invalid Date" + "<br />";
            }
            if (NewLead.City == "") {
                alertMsg += "Invalid City" + "<br />"
            }
        }

        if (rootScopeService.getProductId() == 2) {
            if (NewLead.Name == "") {
                alertMsg += "Invalid Name" + "<br />"
            }
            if (NewLead.Email == "") {
                alertMsg += "Invalid Email" + "<br />"
            }
            if (NewLead.Gender == "") {
                alertMsg += "Invalid Gender" + "<br />"
            }
        }

        if (rootScopeService.getProductId() == 131 && NewLead.SA && (NewLead.SA.toString().includes('.') || isNaN(Number(NewLead.SA)) || NewLead.SA < 0)) {
            alertMsg += "Please enter valid Sum Assured amount \n";
        }

        if (ShowSubProduct(NewLead) && NewLead.SubProduct && NewLead.SubProduct.ID === marineSubProduct && !NewLead.TransitType) {
            alertMsg += "Please select Transit Type \n";
        }

        if(ProductId == 131 && ValidateforRenewal() == false && NewLead.SubProductTypeId && [1, 2, 3].indexOf(NewLead.SubProductTypeId) != -1)
        {
            if(Number(LeadInfo.NoOfLives)<Number(LeadInfo.NoOfEmployees))
            {
                alertMsg+="No. Of Lives cannot be less than No. Of Employees.\n"
            }
        }
        if (LeadInfo.MedicalExtension === "Other" && (isNaN(Number(LeadInfo.OtherMedicalExtension)) || LeadInfo.OtherMedicalExtension <= 0)) {
            alertMsg += "Please enter valid Other Medical Extension value. It should be numeric.\n";
        }

        if (LeadInfo.NoOfLives != "" && LeadInfo.NoOfLives != undefined) {
            if (isNaN(Number(LeadInfo.NoOfLives))
                || Number(LeadInfo.NoOfLives) < 0) {
                alertMsg += "No Of Lives should be numeric.\n";
            };
        }

        if (LeadInfo.NoOfEmployees != "" && LeadInfo.NoOfEmployees != undefined) {
            if (isNaN(Number(LeadInfo.NoOfEmployees))
                || Number(LeadInfo.NoOfEmployees) < 0) {

                alertMsg += "No Of Employees should be numeric.\n";
            };
        }

        if (validateDob) {
            var localdate = new Date();
            var DOBdate = "1"; //Set default 1.
            var DOBmonth = parseInt(NewLead.DOBmonth);
            localdate = new Date(localdate.getFullYear(), localdate.getMonth(), localdate.getDate());
            var flag = false;
            if (NewLead.DOBdate != "") {
                flag = false;
            } else {
                NewLead.DOBdate = DOBdate;
                flag = true;
            }


            var numericReg = /^\d*[0-9](|.\d*[0-9]|,\d*[0-9])?$/;

            if (!(DOBmonth > 0 && DOBmonth < 13))
                alertMsg += "Enter Valid Month" + "<br />";
            if (!(localdate.getFullYear() - NewLead.DOByear < 100 && localdate.getFullYear() - NewLead.DOByear > 0))
                alertMsg += "Enter Valid Year" + "<br />";
            if (!numericReg.test(NewLead.DOBdate)) {
                alertMsg += "Enter Valid Day" + "<br />";
            } else {
                DOBdate = parseInt(NewLead.DOBdate, 10);
                if ((DOBmonth == 1 || DOBmonth == 3 || DOBmonth == 5 || DOBmonth == 7 || DOBmonth == 8 || DOBmonth == 10 || DOBmonth == 12) && (DOBdate < 1 || DOBdate > 31)) {
                    alertMsg = alertMsg + "Enter valid Day  1 to 31 " + "<br />";
                } else if (DOBmonth == 2) {
                    if (NewLead.DOByear % 4 != 0 && (DOBdate < 1 || DOBdate > 28)) {
                        alertMsg = alertMsg + "Enter valid Day  1 to 28 " + "<br />";
                    } else if (DOBdate < 1 || DOBdate > 29) {
                        alertMsg = alertMsg + "Enter valid Day  1 to 29 " + "<br />";
                    }
                } else if ((DOBmonth == 4 || DOBmonth == 6 || DOBmonth == 9 || DOBmonth == 11) && (DOBdate < 1 || DOBdate > 30)) {
                    alertMsg = alertMsg + "Enter valid Day  1 to 30 " + "<br />";
                }
            }

            DOB = new Date(NewLead.DOByear, DOBmonth - 1, DOBdate);
            if (flag) { NewLead.DOBdate = "" };
        }


        // Validate PolicyExpiryDate for applicable ProductIds
        const policyExpiryProducts = [117];

        if (
            policyExpiryProducts.indexOf(ProductId) !== -1 &&
            NewLead.PolicyExpiryDate &&
            NewLead.PolicyExpiryDate !== 62135616600000
        ) {
            const today = new Date();
            const oneYearFromToday = new Date();
            oneYearFromToday.setFullYear(today.getFullYear() - 1);

            const [day, month, year] = NewLead.PolicyExpiryDate.split('/');
            const policyExpiryDate = new Date(`${year}-${month}-${day}`);
            
            if (policyExpiryDate < oneYearFromToday) {
                enqueueSnackbar("Policy Expiry Date should not be less than a year.", {
                    variant: 'error',
                    autoHideDuration: 3000,
                });
                return false;
            }
        }

        if (alertMsg.length > 1) {
            enqueueSnackbar(alertMsg, { variant: 'error', autoHideDuration: 3000, style: { whiteSpace: 'pre-line' } });
            return false;
        }
        else {
            if (NewLead.SubProduct != null && NewLead.SubProduct != undefined) {
                NewLead.SubProductTypeId = NewLead.SubProduct.ID;
            }
            var reqData = {
                LeadID: NewLead.LeadID,
                DOB: validateDob ? Date.parse(DOB) : 0,
                CityID: String(NewLead.CityId),
                StateID: NewLead.StateId,
                ProductId: rootScopeService.getProductId(),
                Name: NewLead.Name,
                Email: NewLead.EmailID,
                Gender: NewLead.Gender,
                SubProductTypeId: NewLead.SubProductTypeId,
                FitPassStatus: NewLead.FitPassStatus,
                Type: 0,
                SumAssured: NewLead.SA ? Number(NewLead.SA) : 0,
                TransitType: NewLead.TransitType ? NewLead.TransitType : '',
                PolicyExpiryDate: showPolicyExpDateEdit && !NewLead.PolicyExpiryDate
                ? 0
                : invalids.indexOf(NewLead.PolicyExpiryDate)==-1 ? new Date(NewLead.PolicyExpiryDate).getTime() : 0,
                //sme
                NoOfLives: LeadInfo.NoOfLives ? Number(LeadInfo.NoOfLives) : 0,
                NoOfEmployees: LeadInfo.NoOfEmployees ? Number(LeadInfo.NoOfEmployees) : 0,
                PolicyTypeId: LeadInfo.PolicyTypeId,
                FamilyType : LeadInfo.FamilyType,
                AssociationId: LeadInfo.AssociationId,
                ShipmentType: LeadInfo.ShipmentType,
                SumInsuredType: LeadInfo.SumInsuredType,
                MedicalExtension: LeadInfo.MedicalExtension=="Other"?Number(LeadInfo.OtherMedicalExtension):LeadInfo.MedicalExtension,
                OccupancyId: LeadInfo.OccupancyId,
                CompanyName: NewLead.CompanyName || "",
            };
            const input = {
                url: `coremrs/api/LeadDetails/UpdateLeadDetails`,
                method: 'POST', service: 'MatrixCoreAPI',
                requestData: reqData
            };
            CALL_API(input).then((resultData) => {
                if (resultData && resultData.IsSaved) {
                    var Comment = "";
                    var reqDataAudit = [];
                    if (rootScopeService.getProductId() == 7 || rootScopeService.getProductId() == 131) {
                        leads.forEach((vdata, key) => {
                            //                                angular.forEach($scope.leads, function (vdata, key) {
                            if (vdata.LeadID == reqData.LeadID) {
                                if (NewLead.City.split(" ").join('') != vdata.City.split(" ").join('')) {
                                    Comment += "City changed from " + vdata.City + " to " + NewLead.City + ". ";

                                    reqDataAudit.push({
                                        LeadId: NewLead.LeadID,
                                        AgentId: User.UserId,
                                        SectionName: "LeadEdit",
                                        Field: "5",
                                        OldValue: vdata.City,
                                        NewValue: NewLead.City,
                                        ProductId: rootScopeService.getProductId()
                                    });
                                }
                            }
                        });
                    }
                    if (rootScopeService.getProductId() == 2) {
                        leads.forEach((vdata, key) => {
                            //angular.forEach($scope.leads, function (vdata, key) {
                            if (vdata.LeadID == reqData.LeadID) {

                                if (NewLead.Name != vdata.Name) {
                                    Comment += "Name changed from " + vdata.Name + " to " + NewLead.Name + ". ";

                                    reqDataAudit.push({
                                        LeadId: NewLead.LeadID,
                                        AgentId: User.UserId,
                                        SectionName: "LeadEdit",
                                        Field: "1",
                                        OldValue: vdata.Name,
                                        NewValue: NewLead.Name,
                                        ProductId: rootScopeService.getProductId()
                                    });
                                }
                                if (NewLead.EmailID != vdata.EmailID) {
                                   Comment += "Email changed from " + vdata.EmailID + " to " + maskEmail(NewLead.EmailID) + ". ";

                                    reqDataAudit.push({
                                        LeadId: NewLead.LeadID,
                                        AgentId: User.UserId,
                                        SectionName: "LeadEdit",
                                        Field: "2",
                                        OldValue: vdata.EmailID,
                                        NewValue: maskEmail(NewLead.EmailID),
                                        ProductId: rootScopeService.getProductId()
                                    });
                                }
                                if (NewLead.Gender != vdata.Gender) {
                                    Comment += "Gender changed from " + vdata.Gender + " to " + NewLead.Gender + ". ";

                                    reqDataAudit.push({
                                        LeadId: NewLead.LeadID,
                                        AgentId: User.UserId,
                                        SectionName: "LeadEdit",
                                        Field: "9",
                                        OldValue: vdata.Gender,
                                        NewValue: NewLead.Gender,
                                        ProductId: rootScopeService.getProductId()
                                    });
                                }

                            }
                        });
                    }

                    if (rootScopeService.getProductId() == 117) {
                        leads.forEach((vdata, key) => {
                            //                                angular.forEach($scope.leads, function (vdata, key) {
                            if (vdata.LeadID == reqData.LeadID) {

                                if (NewLead.Name != vdata.Name) {
                                    Comment += "Name changed from " + vdata.Name + " to " + NewLead.Name + ". ";

                                    reqDataAudit.push({
                                        LeadId: NewLead.LeadID,
                                        AgentId: User.UserId,
                                        SectionName: "LeadEdit",
                                        Field: "1",
                                        OldValue: vdata.Name,
                                        NewValue: NewLead.Name,
                                        ProductId: rootScopeService.getProductId()
                                    });
                                }

                                if (NewLead.EmailID != vdata.EmailID) {
                                   Comment += "Email changed from " + vdata.EmailID + " to " + maskEmail(NewLead.EmailID) + ". ";

                                    reqDataAudit.push({
                                        LeadId: NewLead.LeadID,
                                        AgentId: User.UserId,
                                        SectionName: "LeadEdit",
                                        Field: "2",
                                        OldValue: vdata.EmailID,
                                        NewValue: maskEmail(NewLead.EmailID),
                                        ProductId: rootScopeService.getProductId()
                                    });
                                }

                                // Check PolicyExpiryDate changes
                                const oldPolicyDate = (vdata.PolicyExpiryDate && vdata.PolicyExpiryDate != -62135616600000) ?
                                dayjs(vdata.PolicyExpiryDate).format('YYYY-MM-DD') : null;
                                const newPolicyDate = dayjs(NewLead.PolicyExpiryDate).format('YYYY-MM-DD') || null;

                                if (oldPolicyDate !== newPolicyDate  && invalids.indexOf(NewLead.PolicyExpiryDate) == -1) {
                                    Comment += "PolicyExpiryDate changed from " + (oldPolicyDate || "N/A") + " to " + (newPolicyDate || "N/A") + ". ";

                                    reqDataAudit.push({
                                        LeadId: NewLead.LeadID,
                                        AgentId: User.UserId,
                                        SectionName: "LeadEdit",
                                        Field: "142",
                                        OldValue: oldPolicyDate || "N/A",
                                        NewValue: newPolicyDate || "N/A",
                                        ProductId: rootScopeService.getProductId()
                                    });
                                }

                            }
                        });
                    }
                    if (rootScopeService.getProductId() == 131) {
                        leads.forEach((vdata, key) => {
                            //angular.forEach($scope.leads, function (vdata, key) {
                            if (vdata.LeadID == reqData.LeadID) {
                                if (NewLead.Name != vdata.Name) {
                                    Comment += "Name changed from " + vdata.Name + " to " + NewLead.Name + ". ";
                                    reqDataAudit.push({
                                        LeadId: NewLead.LeadID,
                                        AgentId: User.UserId,
                                        SectionName: "LeadEdit",
                                        Field: "1",
                                        OldValue: vdata.Name,
                                        NewValue: NewLead.Name,
                                        ProductId: rootScopeService.getProductId()
                                    });
                                }
                                if (NewLead.EmailID != vdata.EmailID) {
                                    Comment += "Email changed from " + vdata.EmailID + " to " + maskEmail(NewLead.EmailID) + ". ";

                                    reqDataAudit.push({
                                        LeadId: NewLead.LeadID,
                                        AgentId: User.UserId,
                                        SectionName: "LeadEdit",
                                        Field: "2",
                                        OldValue: vdata.EmailID,
                                        NewValue: maskEmail(NewLead.EmailID),
                                        ProductId: rootScopeService.getProductId()
                                    });
                                }
                                if (NewLead.SubProductTypeId != vdata.SubProductTypeId) {

                                    if (vdata.SubProductTypeId > 0) {
                                        vdata.SubProduct = SubProducts.find(x => x.ID === vdata.SubProductTypeId);
                                    }
                                    else {
                                        vdata.SubProduct = { "ID": 0, "Name": "Blank" };
                                    }

                                    Comment += "Product Type changed from " + vdata.SubProduct.Name + " to " + NewLead.SubProduct.Name + ". ";

                                    reqDataAudit.push({
                                        LeadId: NewLead.LeadID,
                                        AgentId: User.UserId,
                                        SectionName: "LeadEdit",
                                        Field: "71",
                                        OldValue: vdata.SubProduct.Name,
                                        NewValue: NewLead.SubProduct.Name,
                                        ProductId: rootScopeService.getProductId()
                                    });
                                }
                                if (NewLead.SA != vdata.SA) {
                                    if (!vdata.SA) {
                                        Comment += "SumAssured set" + " to " + NewLead.SA + ". ";
                                    }
                                    else {
                                        Comment += "SumAssured changed from " + vdata.SA + " to " + NewLead.SA + ". ";
                                    }

                                    reqDataAudit.push({
                                        LeadId: NewLead.LeadID,
                                        AgentId: User.UserId,
                                        SectionName: "LeadEdit",
                                        Field: "40",
                                        OldValue: vdata.SA?.toString(),
                                        NewValue: NewLead.SA?.toString(),
                                        ProductId: rootScopeService.getProductId()
                                    });
                                }
                                if (NewLead.TransitType != vdata.TransitType) {
                                    if (!vdata.TransitType) {
                                        Comment += "TransitType set" + " to " + NewLead.TransitType + ". ";
                                    }
                                    else {
                                        Comment += "TransitType changed from " + vdata.TransitType + " to " + NewLead.TransitType + ". ";
                                    }

                                    reqDataAudit.push({
                                        LeadId: NewLead.LeadID,
                                        AgentId: User.UserId,
                                        SectionName: "LeadEdit",
                                        Field: "119",
                                        OldValue: vdata.TransitType,
                                        NewValue: NewLead.TransitType,
                                        ProductId: rootScopeService.getProductId()
                                    });
                                }
                            }
                        });
                        if (OldInfo) {
                            var vdata = OldInfo;
                            if (vdata.AssociationId != LeadInfo.AssociationId && Association && [14].indexOf(OldInfo.InvestmentTypeID) != -1) {
                                var oldAssociationName = Association.find((e) => e.Id == vdata.AssociationId)?.Name
                                var associationName = Association.find((e) => e.Id == LeadInfo.AssociationId)?.Name
                                if (!vdata.AssociationId) {
                                    Comment += "Association Name set" + " to " + associationName + ".";
                                }
                                else {
                                    Comment += "Association Name changed from " + oldAssociationName + " to " + associationName + ".";
                                }
                                reqDataAudit.push({
                                    LeadId: NewLead.LeadID,
                                    AgentId: User.UserId,
                                    SectionName: "LeadEdit",
                                    Field: "132",
                                    OldValue: oldAssociationName,
                                    NewValue: associationName,
                                    ProductId: rootScopeService.getProductId()
                                });

                            }
                            if (vdata.MedicalExtension != LeadInfo.MedicalExtension && MedicalExtensions && [19].indexOf(OldInfo.InvestmentTypeID) != -1) {
                                if (LeadInfo.MedicalExtension == "Other") {
                                    LeadInfo.MedicalExtension = LeadInfo.OtherMedicalExtension;
                                    if (vdata.MedicalExtension) {
                                        Comment += "Medical Extension changed from " + vdata.MedicalExtension + " to " + LeadInfo.MedicalExtension + ".";
                                    }
                                    else {
                                        Comment += "Medical Extension set" + " to " + LeadInfo.MedicalExtension + ".";
                                    }
                                    reqDataAudit.push({
                                        LeadId: NewLead.LeadID,
                                        AgentId: User.UserId,
                                        SectionName: "LeadEdit",
                                        Field: "136",
                                        OldValue: vdata.MedicalExtension,
                                        NewValue: vdata.MedicalExtension,
                                        ProductId: rootScopeService.getProductId()
                                    });

                                }
                                else {
                                    var oldMedicalExt = MedicalExtensions.find((e) => e.Id == vdata.MedicalExtension)?.Name;
                                    var newMedicalExt = MedicalExtensions.find((e) => e.Id == LeadInfo.MedicalExtension)?.Name;
                                    if (!vdata.MedicalExtension) {
                                        Comment += "Medical Extension set" + " to " + newMedicalExt + ".";
                                    }
                                    else {
                                        Comment += "Medical Extension changed from " + oldMedicalExt + " to " + newMedicalExt + ".";
                                    }
                                    reqDataAudit.push({
                                        LeadId: NewLead.LeadID,
                                        AgentId: User.UserId,
                                        SectionName: "LeadEdit",
                                        Field: "136",
                                        OldValue: oldMedicalExt,
                                        NewValue: newMedicalExt,
                                        ProductId: rootScopeService.getProductId()
                                    });
                                }

                            }
                            if (vdata.FamilyType != LeadInfo.FamilyType && FamilyTypes && [1, 2, 3].indexOf(OldInfo.InvestmentTypeID) != -1) {
                                var oldFamilyType = FamilyTypes.find((e) => e.Name == vdata.FamilyType)?.Name;
                                var newFamilyType = FamilyTypes.find((e) => e.Name == LeadInfo.FamilyType)?.Name;
                                if (!vdata.FamilyType) {
                                    Comment += "FamilyType set" + " to " + newFamilyType + ".";
                                }
                                else {
                                    Comment += "FamilyType changed from " + oldFamilyType + " to " + newFamilyType + ".";
                                }
                                reqDataAudit.push({
                                    LeadId: NewLead.LeadID,
                                    AgentId: User.UserId,
                                    SectionName: "LeadEdit",
                                    Field: "133",
                                    OldValue: oldFamilyType,
                                    NewValue: newFamilyType,
                                    ProductId: rootScopeService.getProductId()
                                });
                            }
                            if (vdata.ShipmentType != LeadInfo.ShipmentType && ShipmentTypes && [13].indexOf(OldInfo.InvestmentTypeID) != -1) {
                                var oldShip = ShipmentTypes.find((e) => e.Name == vdata.ShipmentType)?.Name;
                                var newShip = ShipmentTypes.find((e) => e.Name == LeadInfo.ShipmentType)?.Name;
                                if (!vdata.ShipmentType) {
                                    Comment += "ShipmentType set" + " to " + newShip + ".";
                                }
                                else {
                                    Comment += "ShipmentType changed from " + oldShip + " to " + newShip + ".";
                                }
                                reqDataAudit.push({
                                    LeadId: NewLead.LeadID,
                                    AgentId: User.UserId,
                                    SectionName: "LeadEdit",
                                    Field: "134",
                                    OldValue: oldShip,
                                    NewValue: newShip,
                                    ProductId: rootScopeService.getProductId()
                                });
                            }
                            if (vdata.SumInsuredType != LeadInfo.SumInsuredType && SumInsuredTypeOptions && [1, 2, 3].indexOf(OldInfo.InvestmentTypeID) != -1) {
                                var oldSumInsured = SumInsuredTypeOptions.find((e) => e.Name == vdata.SumInsuredType)?.Name;
                                var newSumInsured = SumInsuredTypeOptions.find((e) => e.Name == LeadInfo.SumInsuredType)?.Name;
                                if (!vdata.SumInsuredType) {
                                    Comment += "SumInsuredType set" + " to " + newSumInsured + ".";
                                }
                                else {
                                    Comment += "SumInsuredType changed from " + oldSumInsured + " to " + newSumInsured + ".";

                                }
                                reqDataAudit.push({
                                    LeadId: NewLead.LeadID,
                                    AgentId: User.UserId,
                                    SectionName: "LeadEdit",
                                    Field: "135",
                                    OldValue: oldSumInsured,
                                    NewValue: newSumInsured,
                                    ProductId: rootScopeService.getProductId()
                                });
                            }
                            if (vdata.PolicyTypeId != LeadInfo.PolicyTypeId && PolicyTypes && [1, 2, 3, 14].indexOf(OldInfo.InvestmentTypeID) != -1) {
                                var oldPolicy = PolicyTypes.find((e) => e.PolicyTypeId == vdata.PolicyTypeId)?.PolicyTypeName;
                                var newPolicy = PolicyTypes.find((e) => e.PolicyTypeId == LeadInfo.PolicyTypeId)?.PolicyTypeName;
                                if (!vdata.PolicyTypeId) {
                                    Comment += "PolicyType set" + " to " + newPolicy + ".";
                                }
                                else {
                                    Comment += "PolicyType changed from " + oldPolicy + " to " + newPolicy + ".";
                                }
                                reqDataAudit.push({
                                    LeadId: NewLead.LeadID,
                                    AgentId: User.UserId,
                                    SectionName: "LeadEdit",
                                    Field: "19",
                                    OldValue: oldPolicy,
                                    NewValue: newPolicy,
                                    ProductId: rootScopeService.getProductId()
                                });
                            }
                            if (vdata.OccupancyId != LeadInfo.OccupancyId && OccupationsList) {
                                var oldOccupation = OccupationsList.find((e) => e.ID == vdata.OccupancyId)?.Name;
                                var newOccupation = OccupationsList.find((e) => e.ID == LeadInfo.OccupancyId)?.Name;
                                if (!vdata.OccupancyId) {
                                    Comment += "Occupancy set" + " to " + newOccupation + ".";
                                }
                                else {
                                    Comment += "Occupancy changed from " + oldOccupation + " to " + newOccupation + ".";

                                }
                                reqDataAudit.push({
                                    LeadId: NewLead.LeadID,
                                    AgentId: User.UserId,
                                    SectionName: "LeadEdit",
                                    Field: "111",
                                    OldValue: oldOccupation,
                                    NewValue: newOccupation,
                                    ProductId: rootScopeService.getProductId()
                                });
                            }
                            if (vdata.NoOfEmployees != LeadInfo.NoOfEmployees && [1, 2, 3].indexOf(OldInfo.InvestmentTypeID) != -1) {
                                if (!vdata.NoOfEmployees) {
                                    Comment += "No of Employees set" + " to " + LeadInfo.NoOfEmployees + ".";
                                }
                                else {
                                    Comment += "No of Employees changed from " + vdata.NoOfEmployees + " to " + LeadInfo.NoOfEmployees + ".";
                                }
                                reqDataAudit.push({
                                    LeadId: NewLead.LeadID,
                                    AgentId: User.UserId,
                                    SectionName: "LeadEdit",
                                    Field: "114",
                                    OldValue: vdata.NoOfEmployees,
                                    NewValue: LeadInfo.NoOfEmployees,
                                    ProductId: rootScopeService.getProductId()
                                });
                            }
                            if (vdata.NoOfLives != LeadInfo.NoOfLives && [1, 2, 3].indexOf(OldInfo.InvestmentTypeID) != -1) {
                                if (!vdata.NoOfLives) {
                                    Comment += "No of Lives set" + " to " + LeadInfo.NoOfLives + ".";
                                }
                                else {
                                    Comment += "No of Lives changed from " + vdata.NoOfLives + " to " + LeadInfo.NoOfLives + ".";
                                }
                                reqDataAudit.push({
                                    LeadId: NewLead.LeadID,
                                    AgentId: User.UserId,
                                    SectionName: "LeadEdit",
                                    Field: "113",
                                    OldValue: vdata.NoOfLives,
                                    NewValue: LeadInfo.NoOfLives,
                                    ProductId: rootScopeService.getProductId()
                                });
                            }
                            if (vdata.CompanyName != LeadInfo.CompanyName) {
                                if (!vdata.CompanyName) {
                                    Comment += "Company Name set" + " to " + LeadInfo.CompanyName + ".";
                                }
                                else {
                                    Comment += "Company Name changed from " + vdata.CompanyName + " to " + LeadInfo.CompanyName + ".";
                                }
                                reqDataAudit.push({
                                    LeadId: NewLead.LeadID,
                                    AgentId: User.UserId,
                                    SectionName: "LeadEdit",
                                    Field: "109",
                                    OldValue: vdata.CompanyName,
                                    NewValue: LeadInfo.CompanyName,
                                    ProductId: rootScopeService.getProductId()
                                });
                            }

                        }
                    }
                   
                    if (validateDob) {
                        var DOB1 = NewLead.DOB.split("-");
                        DOB1 = new Date(DOB1[2], DOB1[1] - 1, DOB1[0]);
                        var month = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
                        if (Date.parse(DOB) != Date.parse(DOB1)) {
                            Comment += "DOB changed from " + month[DOB1.getMonth()] + " " + DOB1.getDate() + " " + DOB1.getFullYear() +
                                " to " + month[DOB.getMonth()] + " " + DOB.getDate() + " " + DOB.getFullYear() + " ";

                            reqDataAudit.push({
                                LeadId: NewLead.LeadID,
                                AgentId: User.UserId,
                                SectionName: "LeadEdit",
                                Field: "6",
                                OldValue: month[DOB1.getMonth()] + " " + DOB1.getDate() + " " + DOB1.getFullYear(),
                                NewValue: month[DOB.getMonth()] + " " + DOB.getDate() + " " + DOB.getFullYear(),
                                ProductId: rootScopeService.getProductId()
                            });
                        }
                    }
                    if (NewLead.SandBoxEligibility === true) {
                        leads.forEach(function (vdata, key) {
                            if (vdata.LeadID === reqData.LeadID) {
                                if (NewLead.FitPassStatus !== vdata.FitPassStatus) {
                                    Comment += "Fit Pass Membership status changed from " + vdata.FitPassStatus + " to " + NewLead.FitPassStatus + ".";
                                    reqDataAudit.push({
                                        LeadId: NewLead.LeadID,
                                        AgentId: User.UserId,
                                        SectionName: "LeadEdit",
                                        Field: "125",
                                        OldValue: vdata.FitPassStatus,
                                        NewValue: NewLead.FitPassStatus,
                                        ProductId: rootScopeService.getProductId()
                                    });
                                }
                            }
                        });
                    }
                    if (Comment != "") {
                        SetLeadAudits(reqDataAudit);
                        let reqData1 = {
                            CustomerId: rootScopeService.getCustomerId(),
                            ProductId: rootScopeService.getProductId(),
                            ParentLeadId: reqData.LeadID,
                            PrimaryLeadId: PrimaryLeadId,
                            UserId: User.UserId,
                            Comment: Comment,
                            EventType: 12
                        };

                        SetCustomerComment(reqData1).then((result) => {
                            if (result) {
                                enqueueSnackbar("Lead Edited Successfully", { variant: 'success', autoHideDuration: 3000, });
                                props.getLeads();
                                props.handleClose();
                            }
                        }, function () {
                            enqueueSnackbar("Error Updating History", { variant: 'error', autoHideDuration: 3000, });
                        });
                    }
                    else {
                        props.getLeads();
                        enqueueSnackbar("No change", { variant: 'error', autoHideDuration: 3000, });
                    }
                }
                else {
                    props.getLeads();
                    enqueueSnackbar("Unable to Update", { variant: 'error', autoHideDuration: 3000, });
                }
            })
        }
    }

    const ShowSubProduct = (NewLead) => {
        let showSmeSubProduct = false;
        try {
            if (ProductId === 131 && [1, 2, 3, 4].indexOf(NewLead.StatusId) !== -1 && ValidateforRenewal() == false) {
                if (NewLead.SubProductTypeId && NewLead.SubProductTypeId > 0) {
                    showSmeSubProduct = false;
                }
                else {
                    showSmeSubProduct = true;
                }
            }
        }
        catch { }
        return showSmeSubProduct
    }
    const handleClose = () =>{ 
        if(rootScopeService.getProductId()==131)
        {
            setOldInfo([]);
        }
        props.handleClose();
    }

    let isDisabled = !validateEmail(NewLead.EmailID)
        && (ProductId !== 7 || (ProductId == 7 && !NewLead.City == ''))
        && !validatePolicyExpiryDate(NewLead.PolicyExpiryDate);
    const validateSMEColumn = (SubProductId) => {
        if (ProductId == 131 && SubProductId && SubProductId.length != 0) {
            if ((NewLead.SubProductTypeId && (SubProductId.indexOf(NewLead.SubProductTypeId) != -1)) || (NewLead.SubProduct && NewLead.SubProduct.ID && SubProductId.indexOf(NewLead.SubProduct.ID) != -1)) {
                return true;
            }
        }
        return false;
    }
    const AssociationIdPopUp = (AssociationId) => {
        if (AssociationId && [67].indexOf(AssociationId) != -1) {
            if (lead && lead.LeadSource && lead.LeadSource == 'Inbound' && lead.Utm_source && lead.Utm_source == 'medicalassociation'
                && lead.UTM_Medium && lead.UTM_Medium == '18005700242') {
                return true;
            }
            else if (lead && lead.LeadSource && lead.LeadSource == 'Whatsapp' && lead.Utm_source && lead.Utm_source == 'Medical Association'
                && lead.UTM_Medium && ['Indian Dental Association'].indexOf(lead.UTM_Medium) != -1) {
                return true;
            }
        }
        else if (AssociationId && [68].indexOf(AssociationId) != -1) {
            if (lead && lead.LeadSource && lead.LeadSource == 'Inbound' && lead.Utm_source && lead.Utm_source == 'medicalassociation'
                && lead.UTM_Medium && lead.UTM_Medium == '18005700242') {
                return true;
            }
            else if (lead && lead.LeadSource && lead.LeadSource == 'Whatsapp' && lead.Utm_source && lead.Utm_source == 'Medical Association'
                && lead.UTM_Medium && ['Clove Dental'].indexOf(lead.UTM_Medium) != -1) {
                return true;
            }
        }
        else if (AssociationId && [59].indexOf(AssociationId) != -1) {
            if (lead && lead.LeadSource && lead.LeadSource == 'Inbound' && lead.Utm_source && lead.Utm_source == 'medicalassociation'
                && lead.UTM_Medium && lead.UTM_Medium == '18005700242') {
                return true;
            }
            else if (lead && lead.LeadSource && lead.LeadSource == 'Whatsapp' && lead.Utm_source && lead.Utm_source == 'Medical Association'
                && lead.UTM_Medium && ['YOSI'].indexOf(lead.UTM_Medium) != -1) {
                return true;
            }
        }
        return false;
    }
    return (
        <ModalPopup open={props.open} title='Edit Lead' handleClose={handleClose}>
            <div className="EditPopup">
                <Grid container spacing={3}>
                    <TextInput
                        name="Name"
                        label={ProductId == 131 ? "Enter Contact Person's Name" : "Enter Name"}
                        value={NewLead.Name}
                        handleChange={handleChange}
                        show={[131, 2, 117, 106, 118, 130].indexOf(ProductId) !== -1 && ValidateforRenewal() == false}
                        sm={8} md={8} xs={12}
                    />
                    <TextInput
                        name="EmailID"
                        label="Enter Email"
                        value={NewLead.EmailID}
                        handleChange={handleChange}
                        show={[131, 2, 117, 106, 118, 130].indexOf(ProductId) !== -1 && ValidateforRenewal() == false && !(ProductId === 117 && User.RoleId == 13)}
                        sm={8} md={8} xs={12}
                    />
                    <SelectDropdown
                        name="Gender"
                        label="Select Gender"
                        value={NewLead.Gender}
                        options={genderOptions}
                        labelKeyInOptions='_all'
                        valueKeyInOptions='_all'
                        handleChange={handleChange}
                        show={[2, 106, 118, 130].indexOf(ProductId) !== -1 && ValidateforRenewal() == false}
                        sm={8} md={8} xs={12}
                    />
                    <SelectDropdown
                        name="SubProduct"
                        label="Product Type"
                        value={NewLead.SubProduct}
                        options={SubProducts}
                        labelKeyInOptions='Name'
                        valueKeyInOptions='_all'
                        handleChange={handleChange}
                        show={ShowSubProduct(NewLead)}
                        sm={8} md={8} xs={12}
                    />
                    
                    <TextInput
                        name="SA"
                        label="Sum Assured"
                        value={NewLead.SA}
                        handleChange={handleChange}
                        show={ProductId == 131 && ValidateforRenewal() == false}
                        maxLength={12}
                        sm={8} md={8} xs={12}
                    />
                    {
                       validateSMEColumn([13]) &&
                        <SelectDropdown
                            name="TransitType"
                            label="Transit Type"
                            value={NewLead.TransitType}
                            options={TransitTypes}
                            labelKeyInOptions="Name"
                            valueKeyInOptions="Id"
                            handleChange={handleChange}
                            sm={8} md={8} xs={12}
                        />
                    }

                    {
                        validateSMEColumn([1,2,3]) && <TextInput
                            name="NoOfLives"
                            label="No Of Lives"
                            handleChange={handleChange}
                            maxLength="8"
                            value={LeadInfo.NoOfLives}
                            show={ValidateforRenewal() == false}
                            sm={8} md={8} xs={12}
                        />
                    }
                    {
                         validateSMEColumn([1,2,3]) && <TextInput
                            name="NoOfEmployees"
                            label="No Of Employees"
                            handleChange={handleChange}
                            maxLength="8"
                            value={LeadInfo.NoOfEmployees}
                            show={ValidateforRenewal() == false && [1, 2, 3].indexOf(NewLead.SubProductTypeId) != -1}
                            sm={8} md={8} xs={12}
                        />
                    }
                    {
                        !!(ProductId == 131 && NewLead.SubProductTypeId) &&
                         <SelectDropdown
                            name="OccupancyId"
                            label="Occupancy"
                            value={LeadInfo.OccupancyId}
                            options={OccupationsList}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='ID'
                            show={ValidateforRenewal() == false}
                            handleChange={handleChange}
                            sm={8} md={8} xs={12}
                        />
                    }
                    {
                        validateSMEColumn([1, 2, 3, 14]) &&
                        <SelectDropdown
                            name="PolicyTypeId"
                            label="Policy Type"
                            value={LeadInfo.PolicyTypeId}
                            options={PolicyTypes}
                            labelKeyInOptions='PolicyTypeName'
                            valueKeyInOptions='PolicyTypeId'
                            show={ValidateforRenewal() == false}
                            handleChange={handleChange}
                            sm={8} md={8} xs={12}
                        />
                    }
                    {
                        validateSMEColumn([1, 2, 3])&&
                        <SelectDropdown
                            name="SumInsuredType"
                            label="Sum Insured Type"
                            value={LeadInfo.SumInsuredType}
                            handleChange={handleChange}
                            options={SumInsuredTypeOptions}
                            show={ ValidateforRenewal() == false}
                            labelKeyInOptions="Name"
                            valueKeyInOptions="Name"
                            sm={8} md={8} xs={12}
                        />

                    }
                    {
                        validateSMEColumn([1, 2, 3]) &&
                        <SelectDropdown
                            name="FamilyType"
                            label="Family Type"
                            value={LeadInfo.FamilyType}
                            handleChange={handleChange}
                            options={FamilyTypes}
                            show={ ValidateforRenewal() == false && [1, 2, 3].indexOf(NewLead.SubProductTypeId) != -1}
                            labelKeyInOptions="Name"
                            valueKeyInOptions="Name"
                            sm={8} md={8} xs={12}
                        />
                    }

                    {
                        validateSMEColumn([14]) &&
                        <Grid item sm={8} md={8} xs={12}>
                            <Autocomplete
                                name="AssociationId"
                                value={Association.find((e) => e.Id == LeadInfo.AssociationId) || null}
                                onChange={(event, value) => handleChange({ target: { name: 'AssociationId', value } })}
                                options={Association}
                                getOptionLabel={(option) => option.Name || ''}
                                disabled={isDisabled}
                                show={ValidateforRenewal() == false}
                                renderInput={(params) =>
                                    <TextField {...params}
                                        label={"Association"}
                                        variant='outlined'
                                    />}
                            />
                        </Grid>

                    }
                    {
                        validateSMEColumn([13]) && <SelectDropdown
                            name="ShipmentType"
                            label="Shipment Type"
                            value={LeadInfo.ShipmentType}
                            options={ShipmentTypes}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='Id'
                            handleChange={handleChange}
                            show={ ValidateforRenewal() == false}
                            sm={8} md={8} xs={12}
                        />
                    }
                    {
                        validateSMEColumn([19]) && <SelectDropdown
                            name="MedicalExtension"
                            label="Medical Extension"
                            value={LeadInfo.MedicalExtension}
                            options={MedicalExtensions}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='Id'
                            show={ ValidateforRenewal() == false}
                            handleChange={handleChange}
                            sm={8} md={8} xs={12}
                        />
                    }
                    {
                        validateSMEColumn([19]) &&
                        <TextInput
                            name="OtherMedicalExtension"
                            label="Other Medical Extension"
                            value={LeadInfo.OtherMedicalExtension}
                            handleChange={handleChange}
                            show={showOtherMedicalExtention}
                            sm={8} md={8} xs={12}
                            maxLength={10}
                        />

                    }
                    
                    {(ProductId == 7 || ProductId==131) &&
                        <Grid item sm={12} md={8} xs={12}>
                            {/* <TextField type="text" id="City"  label="City" variant="outlined"  />   */}
                            <Autocomplete
                                onChange={(event, value) => handleChange({ target: { name: 'City', value } })}
                                id="combo-box-demo"
                                options={Cities}
                                name="City"
                                value={selectedCity || null}
                                getOptionLabel={(option) => (option.CityStateName || '')}
                                // style={{ width: 355 }}
                                renderInput={(params) =>
                                    <TextField {...params}
                                        label="Enter City"
                                        variant='outlined' />}
                            />

                        </Grid>
                    }
                    {[7, 2, 115, 106, 118, 130].indexOf(ProductId) != -1 && ValidateforRenewal() == false &&
                        <Grid item sm={8} md={8} xs={12} container spacing={1}>
                            <TextInput
                                name="DOBdate"
                                label="Day"
                                value={NewLead.DOBdate}
                                handleChange={handleChange}
                                sm={4} md={4} xs={4}
                            />
                            <SelectDropdown
                                name="DOBmonth"
                                label="Month"
                                value={NewLead.DOBmonth}
                                options={MonthList}
                                labelKeyInOptions='Name'
                                valueKeyInOptions='Value'
                                handleChange={handleChange}
                                // show={show}
                                sm={4} md={4} xs={4}
                            />
                            <TextInput
                                name="DOByear"
                                label="Year"
                                value={NewLead.DOByear}
                                handleChange={handleChange}
                                sm={4} md={4} xs={4}
                            />

                        </Grid>
                    }                 
                   {
                        ProductId === 131 && ValidateforRenewal() === false && (
                            <>
                                <TextInput
                                    name="CompanyName"
                                    label="Enter Company Name"
                                    value={NewLead.CompanyName || ""}
                                    handleChange={handleChange}
                                    sm={8} md={8} xs={12}
                                />
                            </>
                        )
                    }

                    {NewLead.SandBoxEligibility && ValidateforRenewal() == false && [1, 2, 3, 4, 11].includes(NewLead.StatusId) &&
                        <Grid item sm={8} md={8} xs={12} container spacing={1}>
                            <div className="SwitchInput">

                                <div>Fit Pass Membership</div>
                                <div>
                                    <Switch
                                        checked={NewLead.FitPassStatus === 2}
                                        onChange={handleChange}
                                        name="FitPassStatus"
                                    />
                                    {(NewLead.FitPassStatus === 2) ? "Yes" : "No"}
                                </div>
                            </div>
                        </Grid>
                    }
                    <TextInput
                        name="UpSellLeadId"
                        label="Enter Renewal LeadId"
                        value={NewLead.UpSellLeadId}
                        handleChange={handleChange}
                        show={ValidateforRenewal() == true}
                        sm={8} md={8} xs={12}
                    />
                    {[117].indexOf(ProductId) !== -1 && showPolicyExpDateEdit &&
                        <DatePicker
                            name="PolicyExpiryDate"
                            label="Enter Prev. PolicyExpiryDate"
                            value={NewLead.PolicyExpiryDate}
                            handleChange={handleChange}
                            sm={8} md={8} xs={12}
                        />
                    }
                    

                    <Grid item sm={12} md={12} xs={12}>
                        {(!isDisabled || ValidateforRenewal() == true) && <button className="saveButton" onClick={ValidateforRenewal() == false ? SaveLeadEdit : SaveLeadEditForRenewal}>Save</button>}
                    </Grid>
                </Grid>
            </div>
            {ShowAssociationPopUp &&
                <ErrorBoundary name="AssociationIdPopUp">
                    <SMEAssociationPopUp open={ShowAssociationPopUp} associationId={LeadInfo.AssociationId} handleClose={() => { setShowAssociationPopUp(false); }} />
                </ErrorBoundary>
            }
        </ModalPopup>
    )
}