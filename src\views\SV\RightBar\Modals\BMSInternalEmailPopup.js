import { I<PERSON><PERSON><PERSON>on } from '@mui/material';
import { Close } from '@mui/icons-material';
import React, { useEffect, useState } from 'react';
import ModalPopup from '../../../../components/Dialogs/ModalPopup';
import { CALL_API } from '../../../../services';
import User from '../../../../services/user.service';
import { SV_CONFIG } from '../../../../appconfig';

const callSalesEmailsService = (req) => {
    const input = {
        url: 'api/BMS/GetInternalEmailURL',
        method: 'POST',
        service: 'MatrixCoreAPI',
        timeout: 'm',
        requestData: req
    };
    return CALL_API(input);
}

const callSalesEmailsService_New = () => {
    const input = {
        url: `api/BMS/GetMailURL`,
        method: 'GET',
        service: 'MatrixCoreAPI',
    };
    return CALL_API(input);
}

const BMSInternalEmailPopup = (props) => {
    let [key, setKey] = useState(0);
    let [URL, setURL] = useState('');

    useEffect(() => {
        if (props.open) {
            if (SV_CONFIG && SV_CONFIG.CallNewMailBoxApi && SV_CONFIG.CallNewMailBoxApi == "true") {
                callSalesEmailsService_New().then(function (res) {
                    if (res && res.InboxURL) {
                        setKey(key + 1);
                        if (props.IsNewTab) {
                            window.open(res.InboxURL, "_blank");
                            props.handleClose();
                        }
                        else {
                            setURL(res.InboxURL);
                        }
                    }
                    else {
                        props.handleClose();
                    }
                });
            }
            else {
                let payload = {
                    LeadId: 0,
                    UserId: User.UserId,
                    TypeId: 2
                }
                callSalesEmailsService(payload).then(function (res) {
                    if (res && res.InboxURL) {
                        setKey(key + 1);
                        if (props.IsNewTab) {
                            window.open(res.InboxURL, "_blank");
                            props.handleClose();
                        }
                        else {
                            setURL(res.InboxURL);
                        }
                    }
                });
            }
        }
    }, [props.open])

    if (props.IsNewTab) {
        return null;
    }
    else {
        return (
            <ModalPopup open={props.open} handleClose={props.handleClose} className="internalEmailPopup">
                <IconButton onClick={props.handleClose} size="large">
                    <Close />
                </IconButton>
                <iframe
                    key={key}
                    src={URL}
                    title="MailBox"
                    scrolling="no"
                    style={{ width: '100%', height: '100%' }}
                />
            </ModalPopup>
        );
    }
}
export default BMSInternalEmailPopup;