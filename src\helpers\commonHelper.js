// DONOT import this file in user.service.js or rootScopeService.js

import { gaEventTracker } from ".";
import { SV_CONFIG } from "../appconfig";
import { checkUserGroup } from "../services/Common";
import masterService from "../services/masterService";
import rootScopeService from "../services/rootScopeService";
import User from "../services/user.service";
import { eraseCookie, getCookie, setCookie } from "../store/token-store/Token-Store";
import { localStorageCache } from "../utils/utility";


export const ShowAddLeadbtn = function () {
    var result = false;
    var usergrp = User.GroupList;
    if (User.IsProgressive && rootScopeService.getPriority()) {
        return true;
    }


    if (window.location.href.toLowerCase().indexOf("salesview") > -1) {
        return false;
    }
    if (Array.isArray(usergrp)) {
        usergrp.forEach(function (item, key) {
            if ((item.GroupName.toLowerCase().indexOf("renewal") > -1 ||
                item.GroupName.toLowerCase().indexOf("fresh") > -1 ||
                item.GroupName.toLowerCase().indexOf("chat") > -1 ||
                item.GroupName.toLowerCase().indexOf("inbound") > -1
            ) &&
                rootScopeService.getProductId() !== 117) {
                result = true;
            }
        });
    }
    return result;
}

export const setPosCookie = () => {
    try {
        let tokenJson;
        tokenJson = {
            // UserId: User.UserId,
            EmployeeId: User.EmployeeId,
            GroupId: User.GroupId
        }
        let posToken = btoa(JSON.stringify(tokenJson));
        setCookie('posPayment', posToken, 0.34, '.policybazaar.com', true);
    }
    catch (e) {

    }
}

export const getSearchObjFromLS = () => {
    try {
        const searchObj = window.localStorage.getItem("searchObj") || [];
        const _LstAgentLeads = (searchObj && searchObj.length > 0) ? window.JSON.parse(searchObj) : [];
        return _LstAgentLeads
    }
    catch (e) {
        return [];
    }
}

export const getAIPitchIntentList = (Summary) => {
    try {
        let CustomerIntent = [];
        CustomerIntent = Object.values(Summary);
        let CallIds = Object.keys(Summary);
        let IntentList = CustomerIntent && CustomerIntent.length > 0 ? CustomerIntent[0] : []
        Object.keys(IntentList).forEach(function (category) {
            if (IntentList[category]) {
                for (let l = 0; l < IntentList[category].length; l++) {
                    IntentList[category][l].notHelpful = false;
                    IntentList[category][l].CallId = [CallIds[0]];
                }
            }
        })

        for (var i = 1; i <= CustomerIntent.length; i++) {
            if (CustomerIntent[i]) { // obj2 contains this key
                Object.keys(IntentList).forEach(function (category) {
                    let CustomerIntentMoreData = CustomerIntent[i]
                    if (CustomerIntentMoreData[category]) { // CustomerIntentMoreData contains this categoryey
                        for (let j = 0; j < CustomerIntentMoreData[category].length; j++) {
                            CustomerIntentMoreData[category][j].notHelpful = false;
                            const targetIndexOfText = IntentList[category] && IntentList[category].length > 0 ? IntentList[category].findIndex(text => text.ID === CustomerIntentMoreData[category][j].ID) : -1;
                            if (targetIndexOfText !== -1) {
                                IntentList[category][targetIndexOfText].CallId.push(CallIds[i]);
                                CustomerIntentMoreData[category].splice(j, 1);
                                j = j - 1;
                            }
                            else {
                                CustomerIntentMoreData[category][j].CallId = [CallIds[i]];
                            }
                        }
                        if (IntentList && IntentList[category] && IntentList[category].length > 0)
                            IntentList[category].push(...CustomerIntentMoreData[category]) // Add the values from obj2's key to obj1's key
                        else
                            IntentList[category] = CustomerIntentMoreData[category]
                    }
                }); // Add the values from obj2's key to obj1's key
            }
        }

        return IntentList;
    } catch {
        gaEventTracker('AIPitchCard_exception', User.EmployeeId, rootScopeService.getLeadId());
    }

}


const RECENT_IB_LEADS = 'RECENT_IB_LEADS';
export const isRecentIBCall = (leadId) => {
    let recentIBLeads = localStorageCache.readFromCache(RECENT_IB_LEADS)
    let isRecentIBLead = false;
    if (Array.isArray(recentIBLeads)) {
        recentIBLeads.forEach((lead) => {
            if (lead.LeadId == leadId) {
                isRecentIBLead = true;
            }
        })
    }
    return isRecentIBLead;
}

export const pushRecentIBLeadId = (LeadId) => {
    try {

        let recentIBLeads = localStorageCache.readFromCache(RECENT_IB_LEADS);

        if (!Array.isArray(recentIBLeads)) {
            recentIBLeads = [];
        }
        if (recentIBLeads.length > 10) {
            recentIBLeads = recentIBLeads.slice(1);
        }
        recentIBLeads.push({
            LeadId,
            ts: new Date().getTime()
        });
        localStorageCache.writeToCache(RECENT_IB_LEADS, recentIBLeads, 1 * 60 * 60 * 1000);
    }
    catch { }

}

export const IsVCActive = (lastActiveWithinSec = 15) => {
    try {
        if (SV_CONFIG.disableIsVCActiveChk) {
            return false;
        }
        if (SV_CONFIG.lastActiveWithinSec) {
            lastActiveWithinSec = SV_CONFIG.lastActiveWithinSec;
        }
        const lastActiveVC = getCookie('lastActiveVC');

        if (lastActiveVC) {

            const currentTime = new Date().getTime();
            const lastActiveTime = parseInt(lastActiveVC);
            const timeDifference = (currentTime - lastActiveTime) / 1000;

            if (timeDifference < lastActiveWithinSec) {
                return true;
            }
        }
    } catch {

    }
    return false;
}

export const markVideoCallAgentIdle = (agentdata, vcEndThreshold) => {
    try {
        if (agentdata && agentdata.status && agentdata.status.toUpperCase() == "VIDEOMEET") {

            const lastActiveVC = getCookie('lastActiveVC');

            if (lastActiveVC) {

                const currentTime = new Date().getTime();
                const lastActiveTime = parseInt(lastActiveVC);
                const timeDifference = (currentTime - lastActiveTime) / 1000;

                if (timeDifference > vcEndThreshold) {
                    masterService.updateagentloginstatus(User.EmployeeId, 'IDLE')
                    eraseCookie('lastActiveVC')
                }
            }
        }
    }
    catch (err) {
        console.error(err)
    }

}

export const getcustReqCallBack = () => {
    let custReqCallback = JSON.parse(localStorage.getItem("custReqCallBacks") || "{}");
    let nxt15minCallbacks = [];

    if (Object.keys(custReqCallback).length > 0 && custReqCallback['UserId'] == User.UserId) {
        const nxt15MinDateTime = new Date();
        nxt15MinDateTime.setMinutes(nxt15MinDateTime.getMinutes() + 15);
        for (const [key, value] of Object.entries(custReqCallback)) {
            if (key != 'UserId' && (new Date(value.StartDate)) >= (new Date()) && (new Date(value.StartDate)) <= nxt15MinDateTime && value.PopopUpShown == 0) {
                nxt15minCallbacks.push(value);
                custReqCallback[key]['PopopUpShown'] = 1;
            }
        }
        if (nxt15minCallbacks && Array.isArray(nxt15minCallbacks) && nxt15minCallbacks.length > 0) {
            localStorage.setItem('custReqCallBacks', JSON.stringify(custReqCallback));
        }
    }
    return nxt15minCallbacks;
}

export const isChatOrIBAgent = () => {
    const chatUserGrps = Array.isArray(SV_CONFIG.chatUserGrps) ? SV_CONFIG.chatUserGrps : [];
    const IbUserGrps = Array.isArray(SV_CONFIG.IbUserGrps) ? SV_CONFIG.IbUserGrps : [];
    const IsChatOrIBProcessID = User.GroupProcessId && parseInt(User.GroupProcessId) > 0 && [2, 3].includes(parseInt(User.GroupProcessId));
    return IsChatOrIBProcessID || checkUserGroup(chatUserGrps) || checkUserGroup(IbUserGrps);
}

export const checkShowNewChat = () => {
    const newChat = (Array.isArray(SV_CONFIG['ShowNewChatUserRole']) && SV_CONFIG['ShowNewChatUserRole'].indexOf(User.RoleId) > -1) &&
            (
            SV_CONFIG['ShowNewChatAll'] ||
            (
                User.ProductList && User.ProductList.length > 0 && SV_CONFIG['ShowNewChatPrduct'] && User.ProductList.some(product => SV_CONFIG['ShowNewChatPrduct'].includes(product.ProductId))
            ) ||
            (
                User.GroupList && User.GroupList.length > 0 && SV_CONFIG['ShowNewChatGrps'] && User.GroupList.some(grps => SV_CONFIG['ShowNewChatGrps'].includes(grps.GroupId))
            )
        )

    return true;
}

export const showCallBackNotifChurnMsg = () => {
    const showChurnMsg = SV_CONFIG && User.ProductList && Array.isArray(User.ProductList) && User.ProductList.length > 0 && SV_CONFIG['ShowChurnMsgOnPopupPrdct'] && Array.isArray(SV_CONFIG['ShowChurnMsgOnPopupPrdct'])
        && User.ProductList.some(product => SV_CONFIG['ShowChurnMsgOnPopupPrdct'].includes(product.ProductId))

    return showChurnMsg;
}

export const reduceUserCookie = () => {
    try {
        let _userCookie = getCookie('User');
        let _userObj = JSON.parse(atob(_userCookie));

        const extraKeys = Array.isArray(SV_CONFIG.extraKeysUserCookie) ? SV_CONFIG.extraKeysUserCookie : [];
        extraKeys.forEach(key => {
            delete _userObj[key];

        })

        _userCookie = btoa(JSON.stringify(_userObj));

        setCookie('User', _userCookie, 1, '.policybazaar.com');
    }
    catch { }
}

export const removeExtraCookies = () => {
    try {
        const extraKeys = Array.isArray(SV_CONFIG.extraCookies) ? SV_CONFIG.extraCookies : [];
        extraKeys.forEach(key => {
            eraseCookie(key, '.policybazaar.com', '/');
        })

    } catch { }
}

export const SetLastCalledLead = (LeadId) => {
    const LastCalledLead = {
        "LeadId": LeadId,
        "ts": new Date().getTime()
    }
    localStorageCache.writeToCache('restrictToQueue', LastCalledLead, 60 * 1000);
}

export const GetLastCalledLead = () => {
    return localStorageCache.readFromCache('restrictToQueue');
}

export const IsAddLeadRestricted = (leadId, next5leadsfromRedux) => {
    if (SV_CONFIG.removeAddLeadRestrictions) {
        return {
            restrict: false,
        }
    }


    // ----- Restrict add to queue for few seconds after call ends 
    // ----- add lead validations doesnot consider just ended call, as data does not get updated immediately
    // ----- restricting for a few seconds solves

    const LastCalledLead = GetLastCalledLead();
    if (LastCalledLead && LastCalledLead.LeadId === leadId) {
        if (LastCalledLead.ts >= new Date().getTime() - 7000) {
            return {
                restrict: true,
                message: `${leadId}: Kindly wait and retry after 5 seconds`
            }
        }
    }

    if (Array.isArray(next5leadsfromRedux)) {
        let LeadData = next5leadsfromRedux.find(function (x) {
            return (x.LeadId === leadId)
        })
        if (LeadData && !["NotAnswered", 'Answered', "", null, " "].includes(LeadData.CallStatus)) {
            gaEventTracker('AddLeadRestrictedStatus', LeadData.CallStatus, rootScopeService.getLeadId());

            return {
                restrict: true,
                message: `${leadId}: is already present in calling queue`
            }
        }

    }
    
    return {
        restrict: false,
    }
}

export const showGenie = () => {
    try {
        const genie = SV_CONFIG['ShowGenieAll'] ||
        (
            User.ProductList && User.ProductList.length > 0 && SV_CONFIG['ShowGeniePrduct'] && User.ProductList.some(product => SV_CONFIG['ShowGeniePrduct'].includes(product.ProductId))
        ) ||
        (
            User.GroupList && User.GroupList.length > 0 && SV_CONFIG['ShowGenieGrps'] && User.GroupList.some(grps => SV_CONFIG['ShowGenieGrps'].includes(grps.GroupId))
        ) ||
        (
            Array.isArray(SV_CONFIG['ShowGenieEmp']) && SV_CONFIG['ShowGenieEmp'].includes(User.EmployeeId)
        ) || 
        (
            User.GroupProcessId && parseInt(User.GroupProcessId) > 0 && User.ProductList && User.ProductList.length > 0 && User.ProductList.some(product => product.ProductId == 2) && [6,7].includes(parseInt(User.GroupProcessId))
        )

        return genie;
    } 
    catch {
        return false;
    }
}

export const getMongoNotificationData = () => {
    let mongoNotification = [];
    if(showGenie()) {
        mongoNotification = localStorageCache.readFromCache('MongoNotificationData') != null 
        ? JSON.parse(localStorageCache.readFromCache('MongoNotificationData')).filter(item => !item.type.includes("genie")) : [];
    } else {
        mongoNotification = localStorageCache.readFromCache('MongoNotificationData') != null ? JSON.parse(localStorageCache.readFromCache('MongoNotificationData')).filter(item => !item.type.includes("genie_bms")) : [];
    }

    return mongoNotification;
}