import React, { useEffect, useState } from 'react';
// import Paper from '@mui/material/Paper';
// // import Slide from '@mui/material/Slide';
// import { ClickAwayListener } from '@mui/material';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import { ArrowUpward, ArrowForward } from '@mui/icons-material';
import ModalPopup from '../../../../../../components/Dialogs/ModalPopup';
import { CALL_API } from '../../../../../../services';
import User from '../../../../../../services/user.service';
import { Grid } from '@mui/material';
import { OpenAgentDashboard } from '../../helper/sidebarHelper';

const AnalyticsDashboard = (props) => {

    // const classes = useStyles();
    let { open, handleClose } = props;
    const [UserStats, setUserStats] = useState([]);
    const [ShowNewMetrics, setShowNewMetrics] = useState(false);

    let AgentStatsData = (UserStats && UserStats.UsersList !== undefined) ? UserStats.UsersList[0] : null;
    let AvgAgentStatsData = (UserStats && UserStats.AvgGroupList !== undefined) ? UserStats.AvgGroupList[0] : null;
    const GetUserStats = () => {

        const input = {
            url: `coremrs/api/Agent/GetUserStats?UserId=${User.UserId}&ManagerId=0`, method: 'GET', service: 'MatrixCoreAPI',
        }

        CALL_API(input).then((response) => {
            setUserStats(response);
        });
    }
    useEffect(() => {
        if (open)
            GetUserStats();
    }, [open])

    useEffect(() => {
        // Filter ProductList based on the condition
        if (User.ProductList && User.ProductList.length > 0) {
            let productList = [131, 3, 101];
            const UserProductInArray = User.ProductList.some(product => productList.includes(product.ProductId));
            if (!UserProductInArray) {
                setShowNewMetrics(true);
            } else {
                setShowNewMetrics(false);
            }
        }
    }, []);

    const PerformanceCalculationCss = (AvgAgentStatsData, AgentStatsData, isUpRed) => {
        let className = '', icon = '';

        (AvgAgentStatsData > AgentStatsData)
            ? icon = "down"
            : icon = "up";

        if (isUpRed) {
            (AvgAgentStatsData > AgentStatsData)
                ? className = "userStatGreen"
                : className = "userStatRed";
        }
        else {
            (AvgAgentStatsData > AgentStatsData)
                ? className = "userStatRed"
                : className = "userStatGreen";
        }
        return { className, icon };
    }

    const PerformanceCalculation = (AvgAgentStatsData, AgentStatsData) => {
        if (AvgAgentStatsData == 0 || AgentStatsData == 0) {
            return 0;
        }
        let data = ((AgentStatsData / AvgAgentStatsData) - 1) * 100;
        if (data < 0) {
            data = data * -1;
        }
        return data.toFixed(0);
    }
    if (!open) return null;
    return (
        // <Slide direction="right" timeout={{ enter: 500, exit: 200 }} in={open} mountOnEnter unmountOnExit>
        //     <Paper elevation={4} className={classes.paper}>
        //         <ClickAwayListener onClickAway={handleClose}>
        // <ModalPopup>
        <ModalPopup open={open}
            title="Analytics Dashboard"
            handleClose={handleClose}
        >
            <div className="AnalyticsPopup">
                <Grid container spacing={2}>
                    <Grid item sm={6} md={4} xs={6}>
                        <div className="timeDuration">
                            <p>Missed CallBack</p>
                            <h2>{AgentStatsData != null ? AgentStatsData.MissedCB : '0'}</h2>
                            {AgentStatsData != null && AvgAgentStatsData != null &&
                                <span className={PerformanceCalculationCss(AvgAgentStatsData.MissedCB, AgentStatsData.MissedCB, true).className} >

                                    {PerformanceCalculationCss(AvgAgentStatsData.MissedCB, AgentStatsData.MissedCB, true).icon === "up"
                                        ? <ArrowUpward /> : <ArrowDownwardIcon />}
                                    {PerformanceCalculation(AvgAgentStatsData.MissedCB, AgentStatsData.MissedCB)}%
                                </span>
                            }
                        </div>
                    </Grid>
                    <Grid item sm={6} md={4} xs={6}>
                        <div className="timeDuration">
                            <p>Unique Dials</p>
                            <h2>{AgentStatsData != null ? AgentStatsData.UniqueDailed : '0'}</h2>
                            {AgentStatsData != null && AvgAgentStatsData != null &&
                                <span className={PerformanceCalculationCss(AvgAgentStatsData.UniqueDailed, AgentStatsData.UniqueDailed).className} >

                                    {PerformanceCalculationCss(AvgAgentStatsData.UniqueDailed, AgentStatsData.UniqueDailed).icon === "up"
                                        ? <ArrowUpward /> : <ArrowDownwardIcon />}
                                    {PerformanceCalculation(AvgAgentStatsData.UniqueDailed, AgentStatsData.UniqueDailed)}%
                                </span>
                            }

                        </div>
                    </Grid>
                    {!ShowNewMetrics && <><Grid item sm={6} md={4} xs={6}>
                        <div className="timeDuration">
                            <p>Login Time (hrs)</p>
                            <h2>{AgentStatsData != null ? (AgentStatsData.LoginHours / 60).toFixed(1) : '0'}</h2>
                            {AgentStatsData != null && AvgAgentStatsData != null &&
                                <span className={PerformanceCalculationCss(AvgAgentStatsData.LoginHours, AgentStatsData.LoginHours).className} >
                                    {PerformanceCalculationCss(AvgAgentStatsData.LoginHours, AgentStatsData.LoginHours).icon === "up"
                                        ? <ArrowUpward /> : <ArrowDownwardIcon />}
                                    {PerformanceCalculation(AvgAgentStatsData.LoginHours, AgentStatsData.LoginHours)}%
                                </span>
                            }
                        </div>
                    </Grid>
                        <Grid item sm={6} md={4} xs={6}>
                            <div className="timeDuration">
                                <p>Idle Time (min)</p>
                                <h2>{AgentStatsData != null ? AgentStatsData.IdleTime : '0'}</h2>
                                {AgentStatsData != null && AvgAgentStatsData != null &&
                                    <span className={PerformanceCalculationCss(AvgAgentStatsData.IdleTime, AgentStatsData.IdleTime, true).className} >
                                        {PerformanceCalculationCss(AvgAgentStatsData.IdleTime, AgentStatsData.IdleTime, true).icon === "up"
                                            ? <ArrowUpward /> : <ArrowDownwardIcon />}
                                        {PerformanceCalculation(AvgAgentStatsData.IdleTime, AgentStatsData.IdleTime)}%
                                    </span>
                                }
                            </div>
                        </Grid>
                        <Grid item sm={6} md={4} xs={6}>
                            <div className="timeDuration">
                                <p>Talktime (min)</p>
                                <h2>{AgentStatsData != null ? (AgentStatsData.Talktime / 60).toFixed(1) : '0'}</h2>
                                {AgentStatsData != null && AvgAgentStatsData != null &&
                                    <span className={PerformanceCalculationCss(AvgAgentStatsData.Talktime, AgentStatsData.Talktime).className} >
                                        {PerformanceCalculationCss(AvgAgentStatsData.Talktime, AgentStatsData.Talktime).icon === "up"
                                            ? <ArrowUpward /> : <ArrowDownwardIcon />}
                                        {PerformanceCalculation(AvgAgentStatsData.Talktime, AgentStatsData.Talktime)}%
                                    </span>
                                }
                            </div>
                        </Grid>
                        <Grid item sm={6} md={4} xs={6}>
                            <div className="timeDuration">
                                <p>Attempts</p>
                                <h2>{AgentStatsData != null ? AgentStatsData.Attempts : '0'}</h2>
                                {AgentStatsData != null && AvgAgentStatsData != null &&
                                    <span className={PerformanceCalculationCss(AvgAgentStatsData.Attempts, AgentStatsData.Attempts).className} >

                                        {PerformanceCalculationCss(AvgAgentStatsData.Attempts, AgentStatsData.Attempts).icon === "up"
                                            ? <ArrowUpward /> : <ArrowDownwardIcon />}
                                        {PerformanceCalculation(AvgAgentStatsData.Attempts, AgentStatsData.Attempts)}%
                                    </span>
                                }
                            </div>
                        </Grid>
                        <Grid item sm={6} md={4} xs={6}>
                            <div className="timeDuration">
                                <p>Total Break</p>
                                <h2>{AgentStatsData != null ? AgentStatsData.TotalBreaks : '0'}</h2>
                                {AgentStatsData != null && AvgAgentStatsData != null &&
                                    <span className={PerformanceCalculationCss(AvgAgentStatsData.TotalBreaks, AgentStatsData.TotalBreaks, true).className} >

                                        {PerformanceCalculationCss(AvgAgentStatsData.TotalBreaks, AgentStatsData.TotalBreaks, true).icon === "up"
                                            ? <ArrowUpward /> : <ArrowDownwardIcon />}
                                        {PerformanceCalculation(AvgAgentStatsData.TotalBreaks, AgentStatsData.TotalBreaks)}%
                                    </span>
                                }
                            </div>
                        </Grid>
                        <Grid item sm={6} md={4} xs={6}>
                            <div className="timeDuration">
                                <p>APE (MTD)</p>
                                <h2>{AgentStatsData != null ? AgentStatsData.APE : '0'}</h2>
                                {AgentStatsData != null && AvgAgentStatsData != null &&
                                    <span className={PerformanceCalculationCss(AvgAgentStatsData.APE, AgentStatsData.APE).className} >

                                        {PerformanceCalculationCss(AvgAgentStatsData.APE, AgentStatsData.APE).icon === "up"
                                            ? <ArrowUpward /> : <ArrowDownwardIcon />}
                                        {PerformanceCalculation(AvgAgentStatsData.APE, AgentStatsData.APE)}%
                                    </span>
                                }
                            </div>
                        </Grid>
                        <Grid item sm={6} md={4} xs={6}>
                            <div className="timeDuration">
                                <p>Bkgs (MTD)</p>
                                <h2>{AgentStatsData != null ? AgentStatsData.BKGS : '0'}</h2>
                                {AgentStatsData != null && AvgAgentStatsData != null &&
                                    <span className={PerformanceCalculationCss(AvgAgentStatsData.BKGS, AgentStatsData.BKGS).className} >

                                        {PerformanceCalculationCss(AvgAgentStatsData.BKGS, AgentStatsData.BKGS).icon === "up"
                                            ? <ArrowUpward /> : <ArrowDownwardIcon />}
                                        {PerformanceCalculation(AvgAgentStatsData.BKGS, AgentStatsData.BKGS)}%
                                    </span>
                                }
                            </div>
                        </Grid></>}
                    {ShowNewMetrics && <><Grid item sm={6} md={4} xs={6}>
                        <div className="timeDuration">
                            <p>Callable Leads</p>
                            <h2>{AgentStatsData != null ? AgentStatsData.CallableLeads : '0'}</h2>
                            {AgentStatsData != null && AvgAgentStatsData != null &&
                                <span className={PerformanceCalculationCss(AvgAgentStatsData.CallableLeads, AgentStatsData.CallableLeads).className} >

                                    {PerformanceCalculationCss(AvgAgentStatsData.CallableLeads, AgentStatsData.CallableLeads).icon === "up"
                                        ? <ArrowUpward /> : <ArrowDownwardIcon />}
                                    {PerformanceCalculation(AvgAgentStatsData.CallableLeads, AgentStatsData.CallableLeads)}%
                                </span>

                            }
                        </div>
                    </Grid>
                    <Grid item sm={6} md={4} xs={6}>
                            <div className="timeDuration">
                                <p>Overall BU%</p>
                                <h2>{AgentStatsData != null ? AgentStatsData.BUPercentage + '%' : '0%'}</h2>
                                {AgentStatsData != null && AvgAgentStatsData != null &&
                                    <span className={PerformanceCalculationCss(AvgAgentStatsData.BUPercentage, AgentStatsData.BUPercentage).className} >

                                        {PerformanceCalculationCss(AvgAgentStatsData.BUPercentage, AgentStatsData.BUPercentage).icon === "up"
                                            ? <ArrowUpward /> : <ArrowDownwardIcon />}
                                        {PerformanceCalculation(AvgAgentStatsData.BUPercentage, AgentStatsData.BUPercentage)}%
                                    </span>
                                }

                            </div>
                        </Grid>
                        <Grid item sm={6} md={4} xs={6}>
                            <div className="timeDuration">
                                <p>EffectiveBU%</p>
                                <h2>{AgentStatsData != null ? AgentStatsData.EffectiveBU + '%' : '0%'}</h2>
                                {AgentStatsData != null && AvgAgentStatsData != null &&
                                    <span className={PerformanceCalculationCss(AvgAgentStatsData.EffectiveBU, AgentStatsData.EffectiveBU).className} >

                                        {PerformanceCalculationCss(AvgAgentStatsData.EffectiveBU, AgentStatsData.EffectiveBU).icon === "up"
                                            ? <ArrowUpward /> : <ArrowDownwardIcon />}
                                        {PerformanceCalculation(AvgAgentStatsData.EffectiveBU, AgentStatsData.EffectiveBU)}%
                                    </span>
                                }
                            </div>
                        </Grid>
                        <div className="knowmore" onClick={OpenAgentDashboard} >
                            Know more <ArrowForward />
                        </div>
                    </>}
                </Grid>

            </div>

        </ModalPopup >
        //         </ClickAwayListener>
        //     </Paper>
        // </Slide>

    );
    //}
}
export default AnalyticsDashboard;