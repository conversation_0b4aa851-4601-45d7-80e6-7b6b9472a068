import { Button, Grid } from "@mui/material";
import { ExpandMore } from "@mui/icons-material";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ErrorBoundary } from "../../../hoc";
import { CALL_API } from "../../../services";
import rootScopeService from "../../../services/rootScopeService";
import User from "../../../services/user.service";
import { updateStateInRedux } from "../../../store/actions/SalesView/SalesView";
import { ContinueJourneyPopUp } from "../Main/Modals/ContinueJourneyPopUp";


const GridRow = (props) => {
    let { label, value, show, title } = props;
    if (!title) { title = value };

    if (!show) return null;
    return (
        <Grid container item xs={12} className="GridRow">
            <div>{label}</div>
            <div>
                <span title={title}>{value === undefined ? "N/A" : value}</span>
            </div>
        </Grid>
    )
}

const CustomerActivity = () => {
    let [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);
    let [customerProfile, setCustomerProfile] = useState([]);
    const [OpenContinueLink, setOpenContinueLink] = useState(false);
    const [show, setShow] = useState(false);
    let LeadID = rootScopeService.getLeadId();
    let ProductID = rootScopeService.getProductId();
    const dispatch = useDispatch();

    const openCutomerDetails = () => {

        if ([7, 115].includes(ProductID)
            // || (IsShowContjourneylink == true && lead.ProductID == 2)
        ) {
            setOpenContinueLink(true);
        }
        // else if (lead.ContinueJourneyURL && (IntProducts.indexOf(ProductId) != -1 || !(User.RoleId == 13 ? (!(['1', '2', '3'].includes(lead.LeadOrder))) : !(['1', '2', '3', '5'].includes(lead.LeadOrder)) && lead.ProductID != 7 && lead.ProductID != 115 && IsShowContjourneylink == false))) {
        // window.open(lead.ContinueJourneyURL);
        // }
        // else {
        //     enqueueSnackbar("No Continue Journey found", { variant: 'error', autoHideDuration: 2000, });
        // }

    }

    const getCustomerProfile = () => {
        if ([7, 1000, 115].indexOf(ProductID) === -1) {
            return;
        }
        let customerId = rootScopeService.getCustomerId();
        const input = {
            url: "/bu/cust/event?cust_id=" + customerId + "&product_id=" + ProductID,
            method: 'GET', service: 'TermInternal'
        }
        CALL_API(input).then((response) => {
            if (response != undefined) {
                setCustomerProfile(response);
                if (response && response.custId > 0) {
                    dispatch(updateStateInRedux({ key: "CustomerProfileData", value: response }));
                    dispatch(updateStateInRedux({ key: "TermEligibleSA", value: response.eligibleSumAssured }));
                    if (ProductID === 7) {
                        dispatch(updateStateInRedux({ key: "PreApprovedData", value: response.preApprovedMatrixLeadIds }));
                        if(!!response.payTermSelected && response.payTermSelected.toUpperCase() === "LIMITED PAY")
                        {
                            dispatch(updateStateInRedux({ key: "IsInterestedinLimitedPay", value: 1 }));
                        }
                    }
                }
            }
        }).catch(() => {
            setCustomerProfile({})
        });
    }

    const handleToggle = () => {
        if (!show) {
            // getCustomerProfile();
        }
        setShow(!show);
    }

    useEffect(() => {
        if (RefreshLead) {
            getCustomerProfile();
            setShow(false);
        }
    }, [RefreshLead]);

    useEffect(() => {
        getCustomerProfile();
    }, []);

    if (User.RoleId !== 2 && ([7, 1000, 115, 2].indexOf(ProductID) === -1)) return null;
    return <>
        <Grid item sm={12} md={12} xs={12}>
            <div className="customerActivity">
                <h3>Customer Activity</h3>

                <div className="expandmoreIcon">
                    <ExpandMore onClick={handleToggle} style={{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)' }} /></div>
                {show &&
                    <>
                        <GridRow
                            label="Sum Assured"
                            value={customerProfile.sumAssured}
                            show={customerProfile.sumAssured}
                        />
                        <GridRow
                            label="Cover upto"
                            value={customerProfile.coverUpto}
                            show={customerProfile.coverUpto}
                        />
                        <GridRow
                            label="Pay Term"
                            value={customerProfile.payTerm}
                            show={customerProfile.payTerm}
                        />
                        <GridRow
                            label="Payment Type"
                            value={customerProfile.paymentMode}
                            show={customerProfile.paymentMode}
                        />
                        <GridRow
                            label="Selected Plan	"
                            value={customerProfile.lastSelectedPlan}
                            show={customerProfile.lastSelectedPlan}
                        />
                        <GridRow
                            label="Compared plans"
                            value={customerProfile.comparePlans}
                            show={customerProfile.comparePlans}
                        />
                        <GridRow
                            label="Sorted quotes by"
                            value={customerProfile.sortBy}
                            show={customerProfile.sortBy}
                        />
                        <GridRow
                            label="Filter used"
                            value={customerProfile.filterUsed}
                            show={customerProfile.filterUsed}
                        />
                        <GridRow
                            label="Profession Type"
                            value={customerProfile.professionType}
                            show={customerProfile.professionType}
                        />
                        <GridRow
                            label="Education"
                            value={customerProfile.educationQualification}
                            show={customerProfile.educationQualification}
                        />

                        <Button
                            variant="outlined"
                            color="secondary"
                            // size="large"
                            onClick={openCutomerDetails}
                            className=""
                        >
                            Customer Details
                        </Button>
                    </>
                }
            </div>
        </Grid>
        <ErrorBoundary name="ContinueJourneyPopUp">
            <ContinueJourneyPopUp
                open={OpenContinueLink}
                handleClose={() => {
                    setOpenContinueLink(false);
                }}
                leadId={LeadID}
            />
        </ErrorBoundary>
    </>
}
export default CustomerActivity;
