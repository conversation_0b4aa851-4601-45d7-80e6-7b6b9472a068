import React from "react";
import { GetIframeURL } from "../../../services";
import { useSelector } from "react-redux";
import User from "../../../services/user.service";
import { SV_CONFIG } from "../../../../src/appconfig/app.config";
import { IsValidUserGroup } from "../../../services/Common";

export default function ChatNew(props) {
    const AgentChatLeadId = useSelector(state => state.salesview.AgentChatLeadId) || 0;
    let Url = null;

    //For sme hoc group open health chat window
    if (IsValidUserGroup([3330], [12, 13])) {
        Url = GetIframeURL("ChatNew", null, null);
    }
    else if (([12, 13].indexOf(User.RoleId) !== -1) && User.ProductList && User.ProductList.length > 0 && User.ProductList.some(product => product.ProductId == 131)) {
        Url = GetIframeURL("ChatNewSME", null, null);
    }
    //Health Ultra HNI group agent
    else if (([13].indexOf(User.RoleId) !== -1)
        && User.ProductList && User.ProductList.length > 0 && User.ProductList.some(product => product.ProductId == 2)
        && ((User.UserGroupList && User.UserGroupList.length > 0 && SV_CONFIG['HealthUHNIGrp'] && User.UserGroupList.some(grps => SV_CONFIG['HealthUHNIGrp'].includes(grps.GroupId)))
            || (Array.isArray(SV_CONFIG['HealthUHNIEcodes']) && SV_CONFIG['HealthUHNIEcodes'].includes(User.EmployeeId)))) {
        Url = GetIframeURL("ChatUHNI", null, null);
    }
    else {
        Url = GetIframeURL("ChatNew", null, null);
    }

    let finalUrl = Url;

    if (props.leadCardChatClick) {
        if (AgentChatLeadId) {
            finalUrl = Url + "/lead/" + AgentChatLeadId;
        }
    }

    return (
        <div style={{ width: props.width + '%', height: props.width + '%' }} >
            <iframe
                key={AgentChatLeadId || 0}
                src={finalUrl}
                style={{ width: '100%', height: '100%' }}
                title="Chat"
            />
        </div>
    )
}