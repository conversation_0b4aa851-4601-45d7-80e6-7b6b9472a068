/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Panel Layout */
.panel-container {
  display: flex;
  min-height: 100vh;
}

.panel-content {
  flex: 1;
  padding: 20px;
  overflow: auto;
}

/* Custom Scrollbar for panel-content */
.panel-content::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #cccccc;
  border-radius: 4px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #bbbbbb;
}

.panel-title {
  margin-bottom: 20px;
  color: #333;
}

.panel-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 18px;
  color: #666;
}

.panel-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 18px;
  color: #e74c3c;
  background-color: #fadbd8;
  border-radius: 4px;
  padding: 20px;
}

/* Panel Navigation */
.panel-nav {
  width: 220px;
  background-color: #2c3e50;
  color: white;
  padding: 20px 0;
  height: 100vh;
}

.panel-nav ul {
  list-style: none;
}

.panel-nav-item {
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.panel-nav-item:hover {
  background-color: #34495e;
}

.panel-nav-item-active {
  background-color: #3498db;
  font-weight: bold;
}

/* Panel Table */
.panel-table-wrapper {
  width: 100%;
  overflow-x: auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  background-color: white;
}

/* Custom Scrollbar Styles */
.panel-table-wrapper::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.panel-table-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.panel-table-wrapper::-webkit-scrollbar-thumb {
  background: #cccccc;
  border-radius: 4px;
}

.panel-table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #bbbbbb;
}

.panel-table {
  width: 100%;
  table-layout: auto;
  border-collapse: collapse;
  min-width: 600px;
}

.panel-table th,
.panel-table td {
  padding: 10px 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 100px;
  max-width: 200px;
  cursor: pointer;
}

.panel-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  padding: 12px 15px;
}

.panel-table-cell-expanded {
  white-space: normal;
  max-width: none;
  word-break: break-word;
}

.panel-table th:hover {
  background-color: #e9ecef;
}

.panel-table tbody tr:hover {
  background-color: #f5f5f5;
}

.panel-sort-indicator {
  margin-left: 5px;
  color: #3498db;
}

.panel-filter-row th {
  padding: 8px 15px;
}

.panel-filter-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.panel-filter-select {
  width: 80px;
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  font-size: 12px;
  background-color: #f8f9fa;
}

.panel-filter-input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.panel-filter-container .panel-filter-input {
  flex: 1;
  border-radius: 0 4px 4px 0;
  border-left: none;
}

.date-range-filter {
  display: flex;
  width: 100%;
}

.date-range-filter .date-input {
  flex: 1;
  min-width: 0;
  font-size: 12px;
  padding: 4px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.date-range-filter .date-input:first-child {
  margin-right: 4px;
}