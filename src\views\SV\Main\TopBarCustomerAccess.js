/// Topbar for <Main />,
/// Find header.js if looking for dialer and progressive bar

import { Grid } from "@mui/material";
import React from "react";
import { useSelector } from "react-redux";
import { ErrorBoundary } from "../../../hoc";
// import CallTransfer from "../RightBlock/CallTransfer";
// import Vip from "./Vip";
import rootScopeService from "../../../services/rootScopeService";
import AddToQueue from "./actions/AddToQueue";
import User from "../../../services/user.service";
import { hideCallButton } from "../../../helpers";
import CallButton from "../RightBlock/CallButton";
import { ShowAddLeadbtn } from "../../../helpers/commonHelper";
import SearchAppointment from "../Header/SearchAppointment";
import { IsApptfeedbackLead } from "../../../services/Common";
import Search from "../Header/Search";
import ActionButtons from "../Header/ActionButtons";

export default function TopBarCustomerAccess() {
    let [ParentLeadId, IsAddCarVisible] = useSelector(state => {
        let { parentLeadId, IsAddCarVisible } = state.salesview;
        return [parentLeadId, IsAddCarVisible]
    });

    const IsPriorityUser = rootScopeService.getPriority();
    const Isshow = !!ParentLeadId;
    const isMobile = useSelector(state => state.common.isMobile);

    return (
        <>
            <Grid item sm={10} md={10} xs={12}>
                {IsPriorityUser ? (IsApptfeedbackLead() ? <Search className="apptSearchbox" /> : <SearchAppointment className="searchbox" />) : null}
            </Grid>
            <Grid item sm={2} md={2} xs={12}>
                {/* <Vip /> */}
                <div className={isMobile ? 'topbar-scroll-mobile' : 'dflex'}>
                    <>
                        {/* {!IsPriorityUser ?
                            <ErrorBoundary name="CallTransfer">
                                <CallTransfer isVisible={true} />
                            </ErrorBoundary>
                            : null
                        } */}
                        {
                            <>
                                {IsPriorityUser &&
                                    <ErrorBoundary name="AddToQueue">
                                        {Isshow && ShowAddLeadbtn() ? <AddToQueue hideText={IsAddCarVisible} /> : null}
                                    </ErrorBoundary>
                                }
                                {
                                    (!User.IsProgressive && <ErrorBoundary name="CallButton">
                                        {Isshow && !hideCallButton(User) ? <CallButton />
                                            : null}
                                    </ErrorBoundary>)
                                }
                                {IsApptfeedbackLead() &&
                                    <ErrorBoundary name="ActionButtons">
                                        <ActionButtons isLeadOpen={!!ParentLeadId} />
                                    </ErrorBoundary>
                                }
                            </>

                        }

                    </>

                </div>

            </Grid>



        </>
    )
}
