import { Grid, Tooltip } from "@mui/material";
import makeStyles from '@mui/styles/makeStyles';
import { ExpandMore } from "@mui/icons-material";
import dayjs from "dayjs";
import React, { useState } from "react";
import { CALL_API } from "../../../services";
import rootScopeService from "../../../services/rootScopeService";
import RenewalAddOnLeadsPopup from "./Modals/RenewalAddOnLeadsPopup";

const useStyles = makeStyles((theme) => ({
    customSize: {
        padding: 0,
        maxWidth: 800,
        maxHeight: '400px',
        overflow: 'auto',
        '& .MuiTableContainer-root': {
            maxHeight: 350,
            maxWidth: 750,
            overflow: 'auto',
        },
        '& p': {
            padding: '8px 16px',
            fontSize: '15px',
        }
    },
}));
const RenewalAddonLead = () => {
    const classes = useStyles();
    const [show, setShow] = useState(false);
    const [policies, setPolicies] = useState([]);
    const getRenewalAddonLead = () => {
        //let UserId = User.UserId;
        const input = {
            url: `api/MRSCore/GetAddOnRenewalLeads/${rootScopeService.getCustomerId()}/${rootScopeService.getProductId()}`, method: 'GET', service: 'MatrixCoreAPI',
            timeout: "s"
        };
        CALL_API(input).then(function (response) {
            // Success
            switch (response.ErrorCode) {
                case 0:
                    var policies = [];
                    response.Data.forEach(function (val, key) {
                        policies.push({
                            Id: { key: "LeadId", value: val["LeadId"] },
                            ProductName: { key: "Product", value: val["ProductName"] },
                            SupplierName: { key: "Supplier", value: val["SupplierName"] },
                            PlanName: { key: "Plan", value: val["PlanName"] },
                            PolicyNumber: { key: "PolicyNo", value: val["PolicyNumber"] },
                            NoticePremium: { key: "Notice Premium", value: val["NoticePremium"] },
                            PED: { key: "PED", value: dayjs(val["PolicyExpiryDate"]).format('DD/MM/YYYY') },
                        });
                    });
                    setPolicies(policies);
                    break;
                //return policies;
                default:
                    break;

            }
        }, function (response) {
            console.log("Error in getRenewalAddonLead");
        });
    }



    const handleToggle = (e) => {
        if (!show) {
            //getClaimComments();
            getRenewalAddonLead();
        }
        setShow(!show);
    }
    // if (policies.length === 0) {
    //     return null;
    // }
    return <>
        <Grid item sm={12} md={12} xs={12}>

            <div className="RenewalAddonLead">
                <h3>Renewal AddOn Leads</h3>

                <div className="expandmoreIcon">
                    <ExpandMore onClick={handleToggle} style={{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)' }} /></div>
                {show && <>
                    <Tooltip enterTouchDelay={0} leaveTouchDelay={20000} interactive placement="left" classes={{ tooltip: classes.customSize, popper: "addmoreLeadPopup" }}
                        title={<RenewalAddOnLeadsPopup data={policies}></RenewalAddOnLeadsPopup>}>
                        <Grid container spacing={1} className="">
                            <Grid item md={6}><strong>LeadID</strong></Grid>
                            <Grid item md={6}><strong>Policy Expiry Date</strong></Grid>
                            {policies.map(data => {
                                return (
                                    <>
                                        <Grid item md={6}>
                                            {data.Id.value}({data.ProductName.value})
                                        </Grid>
                                        <Grid item md={6}>
                                            <span>
                                                {data.PED.value}
                                            </span>
                                        </Grid>
                                    </>
                                )
                            })}

                        </Grid>
                    </Tooltip>
                    {/* <RenewalAddOnLeadsPopup data={policy}></RenewalAddOnLeadsPopup> */}
                </>
                }

            </div>
        </Grid>
    </>
}
export default RenewalAddonLead;
