var empHideHangupArray = [];
empHideHangupArray[0] = 'AU00122';
empHideHangupArray[1] = 'AU00124';
var hangTimer = 5;
var feedbackTimer = 182;
var secretToken = window.localStorage.getItem('AsteriskToken');
var AsteriskToken = null;
var IsInboundCallType = false;

var sTransferNumber;
var oRingTone,
    oRingbackTone;
var oSipStack,
    oSipSessionRegister,
    oSipSessionCall,
    oSipSessionTransferCall;
var videoRemote,
    videoLocal,
    audioRemote;
var bFullScreen = false;
var oNotifICall;
var bDisableVideo = false;
var viewVideoLocal,
    viewVideoRemote,
    viewLocalScreencast; // <video> (webrtc) or <div> (webrtc4all)
var oConfigCall;
var oReadyStateTimer;

var talkTime = 0; // CALL TIMER INITIAL VALUE
var myCallTimerVar = null; // CALL TIMER INITIAL OBJ;
var callStartTime = '';
var callEndTime = '';
var checkInactiveTime = 0;
var restrictApiForIncoming = 0;
var CallTryCount = 0;
var ConnectCallSFObj = null;
var UserID = 0;
var userobj = window.atob(window.localStorage.getItem('User'));
var alwaysConnectCall = null;
var IsMicrophoneEnable = false;
var callingCompany = '';
if (userobj && userobj != null) {
    userobj = jQuery.parseJSON(userobj);
    UserID = userobj.UserId;
    callingCompany = userobj.CallingCompany;
}

C = {
    divKeyPadWidth: 220
};
// Export.html Settings Start Here
var cbVideoDisable;
var cbAVPFDisable;
var txtWebsocketServerUrl;
var txtSIPOutboundProxyUrl;
var txtInfo;
var parentNotExistCount = 0;
var svNotExistCount = 0;
var isInCall = 0;
var isRunning = false;
// Export.html Settings End Here
var uid = 0;
var ibleadid = 0;
window
    .localStorage
    .setItem("CallDataId", uid);

const configName = localStorage.getItem('configName') || 'SVConfig';

let SV_CONFIG = readFromLSCache(configName);

try {
    SV_CONFIG = JSON.parse(SV_CONFIG);
}
catch (e) { }

let noCallRunningTimerId = null;

function WindowLogout() {

    window.onbeforeunload = null;
    sipUnRegister();
    setTimeout(function () {
        window.close();
    }, 500);

}

function checkforRegistraion() {
    if (oSipStack && oSipSessionRegister) {
        return true;
    }
    return false;
}

const parentWindow = () => window.opener || window.parent || null;

var timeout;
window.onbeforeunload = function () {
    if (alwaysConnectCall) {
        alwaysConnectCall.hangup({
            events_listener: {
                events: '*',
                listener: onSipEventSession
            }
        });
    }
    timeout = setTimeout(function () {
        window.opener && window.opener.IsWindowOpen(1);
    }, 1000);
    //sipUnRegister();
    return "please dont";
}
window.unload = function () {
    window.opener && window.opener.IsWindowOpen(0);
};

//Cookie related code end here
function checkWindowExist() {
    window
        .localStorage
        .setItem('CallWindow', new Date());
}

function checkParentSV() {
    try {
        return window && parentWindow() && parentWindow().document && parentWindow().document.getElementsByClassName("agentInfo").length > 0
    } catch (e) {
        return true;
    }
}

window.onload = function () {
    window
        .localStorage
        .setItem('setMuteText', "Mute");
    window
        .localStorage
        .setItem('setHoldText', "Hold");
    window
        .localStorage
        .setItem('setCallStatus', "Call");

    MicrophoneEnable();

    setInterval(function () {
        MicrophoneEnable();

        checkWindowExist();
        if (!isAswatIndiaUser()) {
            if (parentWindow() == undefined || parentWindow() == null) {
                parentNotExistCount = parseInt(parentNotExistCount) + 1;
            } else {
                parentNotExistCount = 0;
            }
            if (parentNotExistCount > 3) {
                WindowLogout();
            }
        }
        if (!oSipStack || !oSipSessionRegister) {
            sipRegister();
        } else
            retryCount = 0;

    }, 1000);
    setInterval(function () {
        // skip checks for non-agent
        if (userobj.RoleId != 13) return;

        // Logout user if salesview is not open in parent tab.
        if (!checkParentSV()) {
            svNotExistCount = parseInt(svNotExistCount) + 1;
        } else {
            svNotExistCount = 0;
        }
        if (svNotExistCount > 10) {
            if (SV_CONFIG && !SV_CONFIG.DISABLE_MATRIX_LOGOUT_URL_CHANGE && !isAswatIndiaUser()) {
                handleAuthFail(true, 1000, 17);
                gaEventTracker('MATRIX_LOGOUT_URL_CHANGE', userobj.EmployeeId + '__' + new Date().toLocaleTimeString() + "__" + parentWindow().location.href);
            }
        }
    }, 2000);

    setInterval(function () { verifySecretToken(); }, 5000);

    setInterval(() => {
        try {
            window.parent.postMessage({ type: "oSipSessionCall", oSipSessionCall: oSipSessionCall && oSipSessionCall.getRemoteFriendlyName(), phone: 'mtxPhone' }, '*');

            window.parent.postMessage({ type: "webPhoneRegistered", isRegistered: checkforRegistraion(), phone: 'mtxPhone' }, '*')

        }
        catch (e) {
            console.error(e)
        }
    }, 1000)

    window.addEventListener("message", function (event) {
        if (event.data) {
            switch (event.data.type) {
                case 'sipHangUp':
                    console.log(event.data);
                    sipHangUp();
                    break;
                case 'sipToggleMute':
                    console.log(event.data);
                    sipToggleMute();
                    break;
                case 'sipToggleHoldResume':
                    console.log(event.data);
                    sipToggleHoldResume();
                    break;
                case 'UpdateAgentStatus':
                    console.log(event.data);
                    UpdateAgentStatus(event.data.agentStatus);
                    break;
                case 'UpdateAgentLoginStatus':
                    console.log(event.data);
                    UpdateAgentLoginStatus(event.data.agentStatus);
                    break;
                case 'WindowLogout':
                    console.log(event.data);
                    WindowLogout();
                    break;
                case 'SendDTMF':
                    sipSendDTMF(event.data.value)
                    break;
                case 'sipAccept':
                    sipAccept(event.data.toOpenSVLead);
                    break;
                default:
                    break;
            }
        }
    });


    callButtonTrId.style.visibility = 'hidden';
    var classContent = callButtonTrId.className;
    callButtonTrId.className = classContent
        .replace("displaynone", "")
        .trim();

    // Export.html Settings Start Here
    cbVideoDisable = document.getElementById("cbVideoDisable");
    cbRTCWebBreaker = document.getElementById("cbRTCWebBreaker");
    txtWebsocketServerUrl = document.getElementById("txtWebsocketServerUrl");
    txtSIPOutboundProxyUrl = document.getElementById("txtSIPOutboundProxyUrl");
    txtInfo = document.getElementById("txtInfo");

    // txtWebsocketServerUrl.disabled = !window.WebSocket || navigator.appName ==
    // "Microsoft Internet Explorer"; // Do not use WS on IE
    document
        .getElementById("btnSave")
        .disabled = !window.localStorage;
    document
        .getElementById("btnRevert")
        .disabled = !window.localStorage;

    if (window.localStorage) {
        settingsRevert(true);
    }
    // Export.html Settings End Here window.console && window.console.info &&
    // window.console.info("location=" + window.location);

    videoLocal = document.getElementById("video_local");
    videoRemote = document.getElementById("video_remote");
    audioRemote = document.getElementById("audio_remote");

    document.onkeyup = onKeyUp;
    document.body.onkeyup = onKeyUp;
    divCallCtrl.onmousemove = onDivCallCtrlMouseMove;

    // set debug level
    SIPml.setDebugLevel((window.localStorage && window.localStorage.getItem('org.doubango.expert.disable_debug') == "true") ?
        "error" :
        "info");

    loadCredentials();
    loadCallOptions();

    // Initialize call button
    uiBtnCallSetText("Call");

    var getPVal = function (PName) {
        var query = window
            .location
            .search
            .substring(1);
        var vars = query.split('&');
        for (var i = 0; i < vars.length; i++) {
            var pair = vars[i].split('=');
            if (decodeURIComponent(pair[0]) === PName) {
                return decodeURIComponent(pair[1]);
            }
        }
        return null;
    }

    var preInit = function () {
        // set default webrtc type (before initialization)
        var s_webrtc_type = getPVal("wt");
        var s_fps = getPVal("fps");
        var s_mvs = getPVal("mvs"); // maxVideoSize
        var s_mbwu = getPVal("mbwu"); // maxBandwidthUp (kbps)
        var s_mbwd = getPVal("mbwd"); // maxBandwidthUp (kbps)
        var s_za = getPVal("za"); // ZeroArtifacts
        var s_ndb = getPVal("ndb"); // NativeDebug

        if (s_webrtc_type)
            SIPml.setWebRtcType(s_webrtc_type);

        // initialize SIPML5
        SIPml.init(postInit);

        // set other options after initialization
        if (s_fps)
            SIPml.setFps(parseFloat(s_fps));
        if (s_mvs)
            SIPml.setMaxVideoSize(s_mvs);
        if (s_mbwu)
            SIPml.setMaxBandwidthUp(parseFloat(s_mbwu));
        if (s_mbwd)
            SIPml.setMaxBandwidthDown(parseFloat(s_mbwd));
        if (s_za)
            SIPml.setZeroArtifacts(s_za === "true");
        if (s_ndb == "true")
            SIPml.startNativeDebug();
    }

    oReadyStateTimer = setInterval(function () {
        if (document.readyState === "complete") {
            clearInterval(oReadyStateTimer);
            // initialize SIPML5
            preInit();

            // Hard Code Changes By Amrish For Auto Login Start Here.
            var empId = getParamValue('empId').trim();
            var ipAddress = getParamValue('ipAddress');
            var empPassword = getParamValue('empPassword').trim();
            var custPhone = getParamValue('custPhone');

            txtWebsocketServerUrl.value = 'wss://' + ipAddress + ':8089/ws';
            txtDisplayName.value = empId;
            txtPrivateIdentity.value = empId;
            txtPublicIdentity.value = 'sip:' + empId + '@' + ipAddress;
            txtPassword.value = empPassword;
            txtRealm.value = ipAddress;
            txtPhoneNumber.value = custPhone;
            txtIceServers.value = [];
            settingsSave();
            sipUnRegister();
            sipRegister();
            txtRegStatus.innerHTML = '<b>Connecting....</b>';
            // Hard Code Changes By Amrish For Auto Login End Here.
        }
    }, 500);
};

function postInit() {
    // check for WebRTC support
    if (!SIPml.isWebRtcSupported()) {
        // is it chrome?
        if (SIPml.getNavigatorFriendlyName() == 'chrome') {
            if (confirm("You're using an old Chrome version or WebRTC is not enabled.\nDo you want to see" +
                " how to enable WebRTC?")) {
                window.location = 'http://www.webrtc.org/running-the-demos';
            } else {
                window.location = "index.html";
            }
            return;
        } else {
            if (confirm("webrtc-everywhere extension is not installed. Do you want to install it?\nIMPORT" +
                "ANT: You must restart your browser after the installation.")) {
                window.location = 'https://github.com/sarandogou/webrtc-everywhere';
            } else {
                // Must do nothing: give the user the chance to accept the extension
                // window.location = "index.html";
            }
        }
    }

    // checks for WebSocket support
    if (!SIPml.isWebSocketSupported()) {
        if (confirm('Your browser don\'t support WebSockets.\nDo you want to download a WebSocket-cap' +
            'able browser?')) {
            window.location = 'https://www.google.com/intl/en/chrome/browser/';
        } else {
            window.location = "index.html";
        }
        return;
    }

    // FIXME: displays must be per session
    viewVideoLocal = videoLocal;
    viewVideoRemote = videoRemote;

    if (!SIPml.isWebRtcSupported()) {
        if (confirm('Your browser don\'t support WebRTC.\naudio/video calls will be disabled.\nDo you' +
            ' want to download a WebRTC-capable browser?')) {
            window.location = 'https://www.google.com/intl/en/chrome/browser/';
        }
    }

    btnRegister.disabled = false;
    document.body.style.cursor = 'default';
    oConfigCall = {
        audio_remote: audioRemote,
        video_local: viewVideoLocal,
        video_remote: viewVideoRemote,
        screencast_window_id: 0x00000000, // entire desktop
        bandwidth: {
            audio: undefined,
            video: undefined
        },
        video_size: {
            minWidth: undefined,
            minHeight: undefined,
            maxWidth: undefined,
            maxHeight: undefined
        },
        events_listener: {
            events: '*',
            listener: onSipEventSession
        },
        sip_caps: [{
            name: '+g.oma.sip-im'
        }, {
            name: 'language',
            value: '\"en,fr\"'
        }]
    };
}

function loadCallOptions() {
    if (window.localStorage) {
        var s_value;
        if ((s_value = window.localStorage.getItem('org.doubango.call.phone_number')))
            txtPhoneNumber.value = s_value;
        bDisableVideo = (window.localStorage.getItem('org.doubango.expert.disable_video') == "true");
        // txtCallStatus.innerHTML = '<i>Video ' + (bDisableVideo ? 'disabled' :
        // 'enabled') + '</i>';
    }
}

function saveCallOptions() {
    if (window.localStorage) {
        window
            .localStorage
            .setItem('org.doubango.call.phone_number', txtPhoneNumber.value);
        window
            .localStorage
            .setItem('org.doubango.expert.disable_video', bDisableVideo ?
                "true" :
                "false");
    }
}

function loadCredentials() {
    if (window.localStorage) {
        // IE retuns 'null' if not defined
        var s_value;
        if ((s_value = window.localStorage.getItem('org.doubango.identity.display_name')))
            txtDisplayName.value = s_value;
        if ((s_value = window.localStorage.getItem('org.doubango.identity.impi')))
            txtPrivateIdentity.value = s_value;
        if ((s_value = window.localStorage.getItem('org.doubango.identity.impu')))
            txtPublicIdentity.value = s_value;
        if ((s_value = window.localStorage.getItem('org.doubango.identity.password')))
            txtPassword.value = s_value;
        if ((s_value = window.localStorage.getItem('org.doubango.identity.realm')))
            txtRealm.value = s_value;
    } else {
        // txtDisplayName.value = "005"; txtPrivateIdentity.value = "005";
        // txtPublicIdentity.value = "sip:<EMAIL>"; txtPassword.value = "005";
        // txtRealm.value = "sip2sip.info"; txtPhoneNumber.value = "701020";
    }
}

function saveCredentials() {
    if (window.localStorage) {
        window
            .localStorage
            .setItem('org.doubango.identity.display_name', txtDisplayName.value);
        window
            .localStorage
            .setItem('org.doubango.identity.impi', txtPrivateIdentity.value);
        window
            .localStorage
            .setItem('org.doubango.identity.impu', txtPublicIdentity.value);
        window
            .localStorage
            .setItem('org.doubango.identity.password', txtPassword.value);
        window
            .localStorage
            .setItem('org.doubango.identity.realm', txtRealm.value);
    }
}

// sends SIP REGISTER request to login
function sipRegister() {
    // catch exception for IE (DOM not ready)
    try {
        btnRegister.disabled = true;
        if (!txtRealm.value || !txtPrivateIdentity.value || !txtPublicIdentity.value) {
            txtRegStatus.innerHTML = '<b>Please fill madatory fields (*)</b>';
            btnRegister.disabled = false;
            return;
        }
        var o_impu = tsip_uri
            .prototype
            .Parse(txtPublicIdentity.value);
        if (!o_impu || !o_impu.s_user_name || !o_impu.s_host) {
            txtRegStatus.innerHTML = "<b>[" + txtPublicIdentity.value + "] is not a valid Public identity</b>";
            btnRegister.disabled = false;
            return;
        }

        // enable notifications if not already done
        if (window.webkitNotifications && window.webkitNotifications.checkPermission() != 0) {
            window
                .webkitNotifications
                .requestPermission();
        }

        // save credentials
        saveCredentials();

        // update debug level to be sure new values will be used if the user haven't
        // updated the page
        SIPml.setDebugLevel((window.localStorage && window.localStorage.getItem('org.doubango.expert.disable_debug') == "true") ?
            "error" :
            "info");

        // create SIP stack
        oSipStack = new SIPml.Stack({
            realm: txtRealm.value,
            impi: txtPrivateIdentity.value,
            impu: txtPublicIdentity.value,
            password: txtPassword.value,
            display_name: txtDisplayName.value,
            websocket_proxy_url: (window.localStorage ?
                window.localStorage.getItem('org.doubango.expert.websocket_server_url') :
                null),
            outbound_proxy_url: (window.localStorage ?
                window.localStorage.getItem('org.doubango.expert.sip_outboundproxy_url') :
                null),
            ice_servers: (window.localStorage ?
                window.localStorage.getItem('org.doubango.expert.ice_servers') :
                null),
            enable_rtcweb_breaker: (window.localStorage ?
                window.localStorage.getItem('org.doubango.expert.enable_rtcweb_breaker') == "true" :
                false),
            events_listener: {
                events: '*',
                listener: onSipEventStack
            },
            enable_early_ims: (window.localStorage ?
                window.localStorage.getItem('org.doubango.expert.disable_early_ims') != "true" :
                true), // Must be true unless you're using a real IMS network
            enable_media_stream_cache: (window.localStorage ?
                window.localStorage.getItem('org.doubango.expert.enable_media_caching') == "true" :
                false),
            bandwidth: (window.localStorage ?
                tsk_string_to_object(window.localStorage.getItem('org.doubango.expert.bandwidth')) :
                null), // could be redefined a session-level
            video_size: (window.localStorage ?
                tsk_string_to_object(window.localStorage.getItem('org.doubango.expert.video_size')) :
                null), // could be redefined a session-level
            sip_headers: [{
                name: 'User-Agent',
                value: 'IM-client/OMA1.0 sipML5-v1.2016.03.04'
            }, {
                name: 'Organization',
                value: 'Doubango Telecom'
            }]
        });
        if (oSipStack.start() != 0) {
            txtRegStatus.innerHTML = '<b>Failed to start the SIP stack</b>';
        } else
            return;
    } catch (e) {
        txtRegStatus.innerHTML = "<b>2:" + e + "</b>";
    }
    btnRegister.disabled = false;
}

// sends SIP REGISTER (expires=0) to logout
function sipUnRegister() {
    if (oSipStack) {
        oSipStack.stop(); // shutdown all sessions
    }
}

// makes a call (SIP INVITE)
function alwaysConnectAgent(s_type) {

    if (oSipStack && !oSipSessionCall) {

        //btnCall.disabled = true;
        // btnHangUp.disabled = false;

        if (window.localStorage) {
            oConfigCall.bandwidth = tsk_string_to_object(window.localStorage.getItem('org.doubango.expert.bandwidth')); // already defined at stack-level but redifined to use latest values
        }

        // create call session
        console.log('GST11', !oSipSessionCall);
        oSipSessionCall = oSipStack.newSession(s_type, oConfigCall);
        // make call
        console.log('GST', oSipSessionCall);
        let callExten = `*777${txtDisplayName.value}`;
        if (oSipSessionCall.call(callExten) != 0) {
            oSipSessionCall = null;
            txtCallStatus.value = 'Failed to make call';
            btnCall.disabled = false;
            btnHangUp.disabled = true;

            console.log('GST2', oSipSessionCall);

            return;
        }
        alwaysConnectCall = oSipSessionCall;
        setRealtimeDisposition('new webphone connected')
    }
}

// makes a call (SIP INVITE)
function sipCall(s_type) {
    if (oSipStack && !oSipSessionCall && !tsk_string_is_null_or_empty(txtPhoneNumber.value)) {
        if (s_type == 'call-screenshare') {
            if (!SIPml.isScreenShareSupported()) {
                alert('Screen sharing not supported. Are you using chrome 26+?');
                return;
            }
            if (!location.protocol.match('https')) {
                if (confirm("Screen sharing requires https://. Do you want to be redirected?")) {
                    sipUnRegister();
                    window.location = 'https://ns313841.ovh.net/call.htm';
                }
                return;
            }
        }
        btnCall.disabled = true;
        btnHangUp.disabled = false;

        if (window.localStorage) {
            oConfigCall.bandwidth = tsk_string_to_object(window.localStorage.getItem('org.doubango.expert.bandwidth')); // already defined at stack-level but redifined to use latest values
            oConfigCall.video_size = tsk_string_to_object(window.localStorage.getItem('org.doubango.expert.video_size')); // already defined at stack-level but redifined to use latest values
        }

        // create call session
        oSipSessionCall = oSipStack.newSession(s_type, oConfigCall);
        // make call
        var data = window
            .localStorage
            .getItem('ConnectCallSF');
        var ProductID = window.localStorage.getItem('ConnectedLeadProductID');
        if (data != null && data != "") {
            data = JSON.parse(data);
            if (data.campaign == "ntermob" || data.campaign == "termob") {
                hangTimer = 15;
            }
            if (data.campaign && data.campaign.toLowerCase() == "healthrenewal") {
                hangTimer = 10;
            }
        }
        if (parseInt(ProductID) == 106) {
            hangTimer = 30;
        }
        // if (oSipSessionCall.call(txtPhoneNumber.value) != 0) { alert('call ' +
        // data.SF);
        if (data && oSipSessionCall.call(txtPhoneNumber.value, '', data.CallId, data.LeadID, data.empId, data.uid, data.SF, data.campaign, data.CountryCode, callingCompany) != 0) {
            oSipSessionCall = null;
            // ///txtCallStatus.value = 'Failed to make call';
            // ///window.localStorage.setItem('callDisposition','Failed to make call');
            setRealtimeDisposition('Connecting...');
            btnCall.disabled = false;
            btnHangUp.disabled = true;
            return;
        }
        /* else {
                 var d = new Date();
                 callButtonTrId.style.visibility = 'visible';
                 isInCall = 1;
                 talkTime = 0;
                 callStartTime = d.toLocaleTimeString();
                 myCallTimerVar = setInterval(myTimer, 1000); // START CALL TIMER
                 }*/
        saveCallOptions();
    } else if (oSipSessionCall) {
        // ///txtCallStatus.innerHTML = '<i>Connecting...</i>';
        // ///window.localStorage.setItem('callDisposition','Connecting...');
        setRealtimeDisposition('Connecting...');
        oSipSessionCall.accept(oConfigCall);
        setTimeout(function () {
            oSipSessionCall.accept(oConfigCall);
        }, 3000);

    }
}

// Share entire desktop aor application using BFCP or WebRTC native
// implementation
function sipShareScreen() {
    if (SIPml.getWebRtcType() === 'w4a') {
        // Sharing using BFCP -> requires an active session
        if (!oSipSessionCall) {
            // ///txtCallStatus.innerHTML = '<i>No active session</i>';
            // ///window.localStorage.setItem('callDisposition','No active session');
            setRealtimeDisposition('No active session');
            return;
        }
        if (oSipSessionCall.bfcpSharing) {
            if (oSipSessionCall.stopBfcpShare(oConfigCall) != 0) {
                // ///txtCallStatus.value = 'Failed to stop BFCP share';
                // ///window.localStorage.setItem('callDisposition','Failed to stop BFCP share');
                setRealtimeDisposition('Failed to stop BFCP share');
            } else {
                oSipSessionCall.bfcpSharing = false;
            }
        } else {
            oConfigCall.screencast_window_id = 0x00000000;
            if (oSipSessionCall.startBfcpShare(oConfigCall) != 0) {
                // ///txtCallStatus.value = 'Failed to start BFCP share';
                // ///window.localStorage.setItem('callDisposition','Failed to start BFCP
                // share');
                setRealtimeDisposition('Failed to start BFCP share');
            } else {
                oSipSessionCall.bfcpSharing = true;
            }
        }
    } else {
        sipCall('call-screenshare');
    }
}

// transfers the call
function sipTransfer() {
    if (oSipSessionCall) {
        let allowtransfer = false;
        try {
            var user = window.localStorage.getItem("User");
            if (user) {
                user = JSON.parse(window.atob(user));
                for (var i = 0; i < user.UserGroupList.length; i++) {
                    if ($.inArray(parseInt(user.UserGroupList[i].GroupId), AppConfig.allowPredictiveAndProgressive) > -1) {
                        allowtransfer = true;
                    }
                }
            }
        } catch (e) { }

        if (allowtransfer) {
            $('#transferModal').modal('show');
            $(document).on('change', '#transferMenu', function (event) {
                var el = $(this);
                var s_destination = el.val();
                let retval = oSipSessionCall.transfer(s_destination);
                console.log("retval", retval);
                //if (oSipSessionCall.transfer(s_destination) != 0) {
                $('#transferModal').modal('hide');
                el.val('0');
                setRealtimeDisposition('Transfering the call...');
                btnTransfer.disabled = false;
                //  return;
                //}
            });
        }
        else {
            var s_destination = prompt('Enter destination number', '');
            if (!tsk_string_is_null_or_empty(s_destination)) {
                btnTransfer.disabled = true;
                if (oSipSessionCall.transfer(s_destination) != 0) {
                    // ///txtCallStatus.innerHTML = '<i>Call transfer failed</i>';
                    // ///window.localStorage.setItem('callDisposition','Call transfer failed');
                    setRealtimeDisposition('Transfering the call...');
                    btnTransfer.disabled = false;
                    return;
                }
                // ///txtCallStatus.innerHTML = '<i>Transfering the call...</i>';
                // ///window.localStorage.setItem('callDisposition','Transfering the call...');
                setRealtimeDisposition('Transfering the call...');
            }
        }
    }
}

// holds or resumes the call
function sipToggleHoldResume() {
    if (oSipSessionCall) {
        var i_ret;
        btnHoldResume.disabled = true;
        // ///txtCallStatus.innerHTML = oSipSessionCall.bHeld ? '<i>Resuming the
        // call...</i>' : '<i>Holding the call...</i>';
        // ///window.localStorage.setItem('callDisposition',oSipSessionCall.bHeld ?
        // 'Resuming the call...' : 'Holding the call...');
        setRealtimeDisposition(oSipSessionCall.bHeld ?
            'Resuming the call...' :
            'Holding the call...');
        i_ret = oSipSessionCall.bHeld ?
            oSipSessionCall.resume() :
            oSipSessionCall.hold();
        if (i_ret != 0) {
            // ///txtCallStatus.innerHTML = '<i>Hold / Resume failed</i>';
            // ///window.localStorage.setItem('callDisposition','Hold / Resume failed');
            setRealtimeDisposition('Hold / Resume failed');
            btnHoldResume.disabled = false;
            return;
        }
    }
}

// Mute or Unmute the call
function sipToggleMute() {
    if (oSipSessionCall) {
        var i_ret;
        var bMute = !oSipSessionCall.bMute;
        // ///txtCallStatus.innerHTML = bMute ? '<i>Mute the call...</i>' : '<i>Unmute
        // the call...</i>'; ///window.localStorage.setItem('callDisposition',bMute ?
        // 'Mute the call...' : 'Unmute the call...');
        setRealtimeDisposition(bMute ?
            'Mute the call...' :
            'Unmute the call...');
        i_ret = oSipSessionCall.mute('audio',
            /*could be 'video'*/
            bMute);
        if (i_ret != 0) {
            // ///txtCallStatus.innerHTML = '<i>Mute / Unmute failed</i>';
            // ///window.localStorage.setItem('callDisposition','Mute / Unmute failed');
            setRealtimeDisposition('Mute / Unmute failed');
            return;
        }
        oSipSessionCall.bMute = bMute;
        btnMute.value = bMute ?
            "Unmute" :
            "Mute";
        window
            .localStorage
            .setItem('setMuteText', bMute ?
                "Unmute" :
                "Mute");
    }
}
//CustFeedback 
function CustFeedback() {
    var ConnectCallSF = window.localStorage.getItem('ConnectCallSF');
    if (ConnectCallSF) {
        ConnectCallSF = JSON.parse(ConnectCallSF);
        if (oSipSessionCall) {
            var s_destination = "FEEDBACK-" + ConnectCallSF.LeadID + "-" + ConnectCallSF.EmployeeId;
            if (!tsk_string_is_null_or_empty(s_destination)) {
                btnTransfer.disabled = true;
                if (oSipSessionCall.transfer(s_destination) != 0) {
                    setRealtimeDisposition('Transfering the call...');
                    btnTransfer.disabled = false;
                    window
                        .localStorage
                        .setItem('feedbackTimer', "hide");
                    clearInterval(myCallTimerVar);
                    myCallTimerVar = null;
                    return;
                }
                //setRealtimeDisposition('Transfering the call...');
            }
            // InsertIVRFeedBack(ConnectCallSF.EmployeeId, ConnectCallSF.LeadID, -1);
        }
    }
}

function InitFeedback() {
    //     try {
    //         var ConnectCallSF = window.localStorage.getItem('ConnectCallSF');
    //         if (ConnectCallSF) {
    //             ConnectCallSF = JSON.parse(ConnectCallSF);
    //             if (oSipSessionCall) {
    //                 InsertIVRFeedBack(ConnectCallSF.EmployeeId, ConnectCallSF.LeadID, -2)
    //             }
    //         }
    //     } catch (e) {

    //     }
}

function InsertIVRFeedBack(agentid, leadid, rating) {
   
}
// terminates the call (SIP BYE or CANCEL)
function sipHangUp() {
    if (oSipSessionCall) {
        // ///txtCallStatus.innerHTML = '<i>Terminating the call...</i>';
        // ///window.localStorage.setItem('callDisposition','Terminating the call...');
        setRealtimeDisposition('Terminating the call...');
        oSipSessionCall.hangup({
            events_listener: {
                events: '*',
                listener: onSipEventSession
            }
        });
    }
    callButtonTrId.style.visibility = 'hidden';
    customHangupEventHandler();
    CallTryCount = 1;
}

function sipSendDTMF(c) {
    if (oSipSessionCall && c) {
        if (oSipSessionCall.dtmf(c) == 0) {
            try {
                dtmfTone.play();
            } catch (e) { }
        }
    }
}

function startRingTone() {
    try {
        ringtone.play();
    } catch (e) { }
}

function stopRingTone() {
    try {
        ringtone.pause();
    } catch (e) { }
}

function startRingbackTone() {
    try {
        ringbacktone.play();
    } catch (e) { }
}

function stopRingbackTone() {
    try {
        ringbacktone.pause();
    } catch (e) { }
}

function toggleFullScreen() {
    if (videoRemote.webkitSupportsFullscreen) {
        fullScreen(!videoRemote.webkitDisplayingFullscreen);
    } else {
        fullScreen(!bFullScreen);
    }
}

function openKeyPad() {
    divKeyPad.style.visibility = 'visible';
    divKeyPad.style.left = ((document.body.clientWidth - C.divKeyPadWidth) >> 1) + 'px';
    divKeyPad.style.top = '70px';
    divGlassPanel.style.visibility = 'visible';
}

function closeKeyPad() {
    divKeyPad.style.left = '0px';
    divKeyPad.style.top = '0px';
    divKeyPad.style.visibility = 'hidden';
    divGlassPanel.style.visibility = 'hidden';
}

function fullScreen(b_fs) {
    bFullScreen = b_fs;
    if (tsk_utils_have_webrtc4native() && bFullScreen && videoRemote.webkitSupportsFullscreen) {
        if (bFullScreen) {
            videoRemote.webkitEnterFullScreen();
        } else {
            videoRemote.webkitExitFullscreen();
        }
    } else {
        if (tsk_utils_have_webrtc4npapi()) {
            try {
                if (window.__o_display_remote)
                    window.__o_display_remote.setFullScreen(b_fs);
            } catch (e) {
                divVideo.setAttribute("class", b_fs ?
                    "full-screen" :
                    "normal-screen");
            }
        } else {
            divVideo.setAttribute("class", b_fs ?
                "full-screen" :
                "normal-screen");
        }
    }
}

function showNotifICall(s_number) {
    // permission already asked when we registered
    if (window.webkitNotifications && window.webkitNotifications.checkPermission() == 0) {
        if (oNotifICall) {
            oNotifICall.cancel();
        }
        oNotifICall = window
            .webkitNotifications
            .createNotification('images/sipml-34x39.png', 'Incaming call', 'Incoming call from ' + s_number);
        oNotifICall.onclose = function () {
            oNotifICall = null;
        };
        oNotifICall.show();
    }
}

function onKeyUp(evt) {
    evt = (evt || window.event);
    if (evt.keyCode == 27) {
        fullScreen(false);
    } else if (evt.ctrlKey && evt.shiftKey) { // CTRL + SHIFT
        if (evt.keyCode == 65 || evt.keyCode == 86) { // A (65) or V (86)
            bDisableVideo = (evt.keyCode == 65);
            // ///txtCallStatus.innerHTML = '<i>Video ' + (bDisableVideo ? 'disabled' :
            // 'enabled') + '</i>'; ///window.localStorage.setItem('callDisposition','Video
            // ' + (bDisableVideo ? 'disabled' : 'enabled'));
            setRealtimeDisposition('Video ' + (bDisableVideo ?
                'disabled' :
                'enabled'));
            window
                .localStorage
                .setItem('org.doubango.expert.disable_video', bDisableVideo);
        }
    }
}

function onDivCallCtrlMouseMove(evt) {
    try { // IE: DOM not ready
        if (tsk_utils_have_stream()) {
            btnCall.disabled = (!tsk_utils_have_stream() || !oSipSessionRegister || !oSipSessionRegister.is_connected());
            document
                .getElementById("divCallCtrl")
                .onmousemove = null; // unsubscribe
        }
    } catch (e) { }
}

function uiOnConnectionEvent(b_connected, b_connecting) { // should be enum: connecting, connected, terminating, terminated
    btnRegister.disabled = b_connected || b_connecting;
    btnUnRegister.disabled = !b_connected && !b_connecting;
    btnCall.disabled = !(b_connected && tsk_utils_have_webrtc() && tsk_utils_have_stream());
    btnHangUp.disabled = !oSipSessionCall;
}

function uiVideoDisplayEvent(b_local, b_added) {
    var o_elt_video = b_local ?
        videoLocal :
        videoRemote;

    if (b_added) {
        o_elt_video.style.opacity = 1;
        uiVideoDisplayShowHide(true);
    } else {
        o_elt_video.style.opacity = 0;
        fullScreen(false);
    }
}

function uiVideoDisplayShowHide(b_show) {
    if (b_show) {
        tdVideo.style.height = '340px';
        divVideo.style.height = navigator.appName == 'Microsoft Internet Explorer' ?
            '100%' :
            '340px';
    } else {
        tdVideo.style.height = '0px';
        divVideo.style.height = '0px';
    }
    btnFullScreen.disabled = !b_show;
}

function uiDisableCallOptions() {
    if (window.localStorage) {
        window
            .localStorage
            .setItem('org.doubango.expert.disable_callbtn_options', 'true');
        uiBtnCallSetText('Call');
        alert('Use expert view to enable the options again (/!\\requires re-loading the page)');
    }
}

function uiBtnCallSetText(s_text) {
    switch (s_text) {
        case "Call":
            {
                var bDisableCallBtnOptions = (window.localStorage && window.localStorage.getItem('org.doubango.expert.disable_callbtn_options') == "true");
                btnCall.value = btnCall.innerHTML = bDisableCallBtnOptions ?
                    'Call<span id="callTimer"></span>' : 'Call<span id="callTimer"></span>';
                btnCall.setAttribute("onclick", bDisableCallBtnOptions ?
                    "" :
                    "sipCall('call-audio');");
                btnCall.setAttribute("class", bDisableCallBtnOptions ?
                    "btn btn-primary" :
                    "btn btn-primary");
                // btnCall.onclick = bDisableCallBtnOptions ? function () {sipCall(bDisableVideo
                // ? 'call-audio' : 'call-audiovideo');} : null;
                ulCallOptions.style.visibility = bDisableCallBtnOptions ?
                    "hidden" : "visible";
                if (!bDisableCallBtnOptions && ulCallOptions.parentNode != divBtnCallGroup) {
                    divBtnCallGroup.appendChild(ulCallOptions);
                } else if (bDisableCallBtnOptions && ulCallOptions.parentNode == divBtnCallGroup) {
                    document
                        .body
                        .appendChild(ulCallOptions);
                }
                break;
            }
        default:
            {
                //alert('HER'); callButtonTrId.removeAttribute("class", "display_none");
                btnCall.value = btnCall.innerHTML = s_text;
                btnCall.setAttribute("class", "btn btn-primary");
                btnCall.onclick = function () {
                    sipCall(bDisableVideo ?
                        'call-audio' :
                        'call-audio');
                };
                ulCallOptions.style.visibility = "hidden";
                if (ulCallOptions.parentNode == divBtnCallGroup) {
                    document
                        .body
                        .appendChild(ulCallOptions);
                }
                break;
            }
    }
}

function uiCallTerminated(s_description, s_code) {
    uiBtnCallSetText("Call");
    btnHangUp.value = 'HangUp';
    btnHoldResume.value = 'Hold';
    btnMute.value = "Mute";
    window
        .localStorage
        .setItem('setMuteText', "Mute");
    window
        .localStorage
        .setItem('setHoldText', "Hold");
    btnCall.disabled = false;
    btnHangUp.disabled = true;
    if (window.btnBFCP)
        window.btnBFCP.disabled = true;

    oSipSessionCall = null;

    stopRingbackTone();
    stopRingTone();
    customHangupEventHandler();
    callButtonTrId.style.visibility = 'hidden';
    // ///txtCallStatus.innerHTML = "<i>" + s_description + "</i>";
    // ///window.localStorage.setItem('callDisposition',s_description);
    setRealtimeDisposition(s_description, s_code);
    uiVideoDisplayShowHide(false);
    divCallOptions.style.opacity = 0;

    if (oNotifICall) {
        oNotifICall.cancel();
        oNotifICall = null;
    }

    uiVideoDisplayEvent(false, false);
    uiVideoDisplayEvent(true, false);

    setTimeout(function () {
        if (!oSipSessionCall) {
            // ///txtCallStatus.innerHTML = '';
            // ///window.localStorage.setItem('callDisposition',"");
            setRealtimeDisposition("");
        }

    }, 2500);
    parentWindow() && parentWindow().hideAlert();
}

// Callback function for SIP Stacks
function onSipEventStack(e /*SIPml.Stack.Event*/) {
    tsk_utils_log_info('==stack event = ' + e.type);
    switch (e.type) {
        case 'started':
            {
                // catch exception for IE (DOM not ready)
                try {
                    // LogIn (REGISTER) as soon as the stack finish starting
                    oSipSessionRegister = this.newSession('register', {
                        expires: 300,
                        events_listener: {
                            events: '*',
                            listener: onSipEventSession
                        },
                        sip_caps: [{
                            name: '+g.oma.sip-im',
                            value: null
                        },
                        //{ name: '+sip.ice' }, // rfc5768: FIXME doesn't work with Polycom TelePresence
                        {
                            name: '+audio',
                            value: null
                        }, {
                            name: 'language',
                            value: '\"en,fr\"'
                        }
                        ]
                    });
                    oSipSessionRegister.register();
                } catch (e) {
                    txtRegStatus.value = txtRegStatus.innerHTML = "<b>1:" + e + "</b>";
                    btnRegister.disabled = false;
                    console.error('txtRegStatus', e);
                }
                break;
            }
        case 'stopping':
        case 'stopped':
        case 'failed_to_start':
        case 'failed_to_stop':
            {
                var bFailure = (e.type == 'failed_to_start') || (e.type == 'failed_to_stop');
                oSipStack = null;
                oSipSessionRegister = null;
                oSipSessionCall = null;

                uiOnConnectionEvent(false, false);

                stopRingbackTone();
                stopRingTone();

                uiVideoDisplayShowHide(false);
                divCallOptions.style.opacity = 0;

                // ///txtCallStatus.innerHTML = '';
                // ///window.localStorage.setItem('callDisposition',"");
                setRealtimeDisposition("");
                txtRegStatus.innerHTML = bFailure ?
                    "<i>Disconnected: <b>" + e.description + "</b></i>" : "<i>Disconnected</i>";

                uiCallTerminated(e.description, e.getSipResponseCode());
                AgentHangUp(e.description, "Answered", e.getSipResponseCode());

                break;
            }

        case 'i_new_call':
            {
                if (noCallRunningTimerId) {
                    try {
                        let leadId;

                        if (oSipSessionCall) {
                            const remoteFriendlyName = (sRemoteNumber == 'unknown' ? sRemoteNumber : JSON.parse(atob(oSipSessionCall.getRemoteFriendlyName())));
                            leadId = remoteFriendlyName.leadid;
                        }

                        const callData = findLeadIdAndCallIdOfSS();

                        if (callData && callData.leadId === leadId) {
                            clearTimeout(noCallRunningTimerId);
                            noCallRunningTimerId = null;
                        }
                    } catch (error) {
                        console.error('An error occurred:', error);
                    }

                }

                if (oSipSessionCall || IsVCActive(20)) {
                    console.log("i_new_call: do not accept the incoming call if we're already 'in call'");
                    // do not accept the incoming call if we're already 'in call'
                    e
                        .newSession
                        .hangup(); // comment this line for multi-line support
                } else {
                    oSipSessionCall = e.newSession;
                    localStorage.setItem('onCall', true);
                    // start listening for events
                    oSipSessionCall.setConfiguration(oConfigCall);

                    uiBtnCallSetText("Answer <span id='callTimer'></span>");
                    // btnHangUp.value = 'Reject';
                    // btnCall.disabled = false;
                    // btnHangUp.disabled = false;

                    startRingTone();

                    var sRemoteNumber = (oSipSessionCall.getRemoteFriendlyName() || 'unknown');
                    // ///txtCallStatus.innerHTML = "<i>Incoming call from [<b>" + sRemoteNumber +
                    // "</b>]</i>"; ///window.localStorage.setItem('callDisposition',"Incoming call
                    // from [" + sRemoteNumber + "]");
                    IsInboundCallType = false;
                    try {
                        if (sRemoteNumber.indexOf('1111111111') > -1) {
                            sRemoteNumber = 'unknown';
                        }
                        RemoteFriendlyName = (sRemoteNumber == 'unknown' ? sRemoteNumber : JSON.parse(atob(oSipSessionCall.getRemoteFriendlyName())));
                        if (RemoteFriendlyName && RemoteFriendlyName.calltype &&
                            (RemoteFriendlyName.calltype.indexOf('IB') > -1 ||
                                RemoteFriendlyName.calltype.indexOf('C2C') > -1 ||
                                RemoteFriendlyName.calltype.indexOf('POD') > -1 ||
                                RemoteFriendlyName.calltype.indexOf('PDOB') > -1)) {
                            IsInboundCallType = true;
                        }

                        if (RemoteFriendlyName.calltype === "VC_IB") {
                            IsInboundCallType = false;
                        }

                        if (IsInboundCallType || RemoteFriendlyName.calltype === "VC_IB") {
                            sRemoteNumber = ibleadid = RemoteFriendlyName.leadid;
                            //Change for ConnectCallSFObj
                            ConnectCallSFObj = { "LeadID": RemoteFriendlyName.leadid, "CallInitTime": JSON.stringify(new Date()).replace(/"/g, '') };
                            window.localStorage.setItem('ConnectCallSF', JSON.stringify(ConnectCallSFObj));
                            if(RemoteFriendlyName.context && RemoteFriendlyName.context == 'crttosalescrosssell'){
                            window.localStorage.setItem('IBContext', RemoteFriendlyName.context);
                            }
                        }
                        if (!IsInboundCallType) {
                            let connectCallSF_LS = getConnectCallSFFromLS();
                            if (connectCallSF_LS && connectCallSF_LS.LeadID != RemoteFriendlyName.leadid) {
                                ConnectCallSFObj = { "LeadID": RemoteFriendlyName.leadid, "CallInitTime": JSON.stringify(new Date()).replace(/"/g, '') };
                                window.localStorage.setItem('ConnectCallSF', JSON.stringify(ConnectCallSFObj));
                            }
                            window.localStorage.setItem('RemoteFriendlyLeadId', RemoteFriendlyName.leadid);
                        }

                        if (RemoteFriendlyName.calltype === "VC_IB") {
                            localStorage.setItem('IsInboundVC', true);
                            localStorage.setItem('IBVCData', JSON.stringify({
                                meetingId: RemoteFriendlyName.meetingID || RemoteFriendlyName.uid,
                                leadId: RemoteFriendlyName.leadid
                            }));
                        } else {
                            localStorage.removeItem('IsInboundVC');
                            localStorage.removeItem('IBVCData');
                        }

                    } catch (e) {
                        RemoteFriendlyName = 'unknown';
                        console.error('Error i_new_call 1', e);
                    }

                    setRealtimeDisposition("Incoming call from " + sRemoteNumber + "");
                    showNotifICall(sRemoteNumber);
                    localStorage.setItem("calltype", RemoteFriendlyName.calltype);
                    if (SV_CONFIG.revert_maccepted && RemoteFriendlyName && AppConfig.PreditiveIBCallType.indexOf(RemoteFriendlyName.calltype) > -1) {
                        callButtonTrId.style.visibility = 'visible';
                        runtimer();

                        parentWindow().showAlert(RemoteFriendlyName.calltype);
                    }
                    else {
                        if (IsInboundCallType) {
                            localStorage.setItem("opensvleadid", ibleadid);
                        }
                        try {
                            var ProductID = window.localStorage.getItem('ConnectedLeadProductID');
                            if (RemoteFriendlyName && ['ntermob', 'termob'].includes(RemoteFriendlyName.campaign)) {
                                hangTimer = 15;
                            }
                            if (RemoteFriendlyName && RemoteFriendlyName.campaign &&
                                RemoteFriendlyName.campaign.toLowerCase &&
                                RemoteFriendlyName.campaign.toLowerCase() == "healthrenewal"
                            ) {
                                hangTimer = 10;
                            }
                            if (parseInt(ProductID) == 106) {
                                hangTimer = 30;
                            }
                        }
                        catch (e) {
                            console.error('Error i_new_call 2', RemoteFriendlyName && RemoteFriendlyName.leadid, e);
                        }
                        window.localStorage.setItem('setCallStatus', "Call");
                        if (RemoteFriendlyName.calltype !== "VC_IB") {
                            customCallAcceptEventHandler(e);
                            setTimeout(function () {
                                oSipSessionCall && oSipSessionCall.accept(oConfigCall);
                            }, 3000);
                        }
                    }
                }

                break;
            }

        case 'm_permission_requested':
            {
                divGlassPanel.style.visibility = 'visible';
                break;
            }
        case 'm_permission_accepted':
            if (!SV_CONFIG.revert_maccepted && RemoteFriendlyName && AppConfig.PreditiveIBCallType.indexOf(RemoteFriendlyName.calltype) > -1) {
                callButtonTrId.style.visibility = 'visible';
                runtimer();

                parentWindow().showAlert(RemoteFriendlyName.calltype);
            }
            break;
        case 'm_permission_refused':
            {
                divGlassPanel.style.visibility = 'hidden';
                if (e.type == 'm_permission_refused') {
                    uiCallTerminated('Media stream permission denied');
                }
                break;
            }

        case 'starting':
        default:
            break;
    }
}

var retryCount = 0;
var RemoteFriendlyName = null;
// Callback function for SIP sessions (INVITE, REGISTER, MESSAGE...)
function onSipEventSession(e /* SIPml.Session.Event */) {
    tsk_utils_log_info('==session event = ' + e.type);

    // if (e.getSipResponseCode() == 200) {
    // AgentAnswerd();
    // }

    //486 AgentHangUp(Disposition,status)

    switch (e.type) {
        case 'connecting':
        case 'connected':
            {
                var bConnected = (e.type == 'connected');
                if (e.session == oSipSessionRegister) {
                    uiOnConnectionEvent(bConnected, !bConnected);
                    txtRegStatus.innerHTML = "<i>" + txtDisplayName.value + " " + e.description + " to [" + txtRealm.value + "]</i>";
                    if (isUserWebphoneNew()) {
                        setInterval(() => {
                            console.log('WEBPHONE_NEW connected: ', !!oSipSessionCall);
                            hideHangUp();
                            alwaysConnectAgent('call-audio');
                        }, 5000);
                    }

                    //setSecretToken(); //setting up the secret token
                } else if (e.session == oSipSessionCall) {
                    // ///btnHangUp.value = 'HangUp'; ///btnCall.disabled = true;
                    // ///btnHangUp.disabled = false; IF IN ARRAY THE HIDE HANGUP
                    // if(jQuery.inArray(getParamValue('empId'), empHideHangupArray) != -1) {
                    // hideHangUp(); } else { showHangUp(); }
                    btnTransfer.disabled = false;
                    if (window.btnBFCP)
                        window.btnBFCP.disabled = false;

                    if (bConnected) {
                        stopRingbackTone();
                        stopRingTone();

                        if (oNotifICall) {
                            oNotifICall.cancel();
                            oNotifICall = null;
                        }
                    }

                    if (e.description.toLowerCase() == 'in call') {
                        var sRemoteNumber = (oSipSessionCall.getRemoteFriendlyName() || 'unknown');
                        if (sRemoteNumber.indexOf('1111111111') > -1) {
                            sRemoteNumber = 'unknown';
                        }
                        try {
                            RemoteFriendlyName = sRemoteNumber == 'unknown' ? sRemoteNumber : JSON.parse(atob(e.session.getRemoteFriendlyName()))
                        } catch (e) {
                            RemoteFriendlyName = 'unknown';
                        }
                        muteMicrophone(true);
                        window
                            .localStorage
                            .setItem('showHideRingtone', "false");
                        // btnHangUp.disabled = false;
                        // btnHangUp.style.visibility = 'visible';
                        // window
                        //     .localStorage
                        //     .setItem('setCallStatus', "Hang Up");
                        restrictApiForIncoming = 1;


                        if (RemoteFriendlyName.calltype !== "VC_IB") {
                            if (RemoteFriendlyName && RemoteFriendlyName.calltype && RemoteFriendlyName.calltype.indexOf('IB') > -1) {
                                localStorage.setItem("opensvleadid", RemoteFriendlyName.leadid);
                            } else {
                                AgentAnswerd();
                            }
                        }

                    }

                    // ///txtCallStatus.innerHTML = "<i>" + e.description + "</i>";
                    // ///window.localStorage.setItem('callDisposition',e.description);
                    // setRealtimeDisposition(e.description);
                    setRealtimeDisposition(e.description, e.getSipResponseCode());
                    divCallOptions.style.opacity = bConnected ?
                        1 :
                        0;

                    if (SIPml.isWebRtc4AllSupported()) { // IE don't provide stream callback
                        uiVideoDisplayEvent(false, true);
                        uiVideoDisplayEvent(true, true);
                    }
                }
                break;
            } // 'connecting' | 'connected'
        case 'terminating':
        case 'terminated':
            {
                console.log("terminated", new Date());
                if (e.session == oSipSessionRegister) {
                    uiOnConnectionEvent(false, false);

                    oSipSessionCall = null;
                    oSipSessionRegister = null;

                    txtRegStatus.innerHTML = "<i>" + e.description + "</i>";

                    if (e.description.toLowerCase() == "forbidden") {
                        retryCount = retryCount + 1;
                    }
                    if (retryCount >= 5 || (AsteriskToken != null && secretToken != AsteriskToken)) {
                        alert("Your credentials are not correct, please contact your administrator.")
                        WindowLogout();
                        retryCount = 0;
                    }

                } else if (e.session == oSipSessionCall) {
                    localStorage.removeItem("IsInboundVC")
                    uiCallTerminated(e.description, e.getSipResponseCode());
                    localStorage.setItem('onCall', false);
                    if (RemoteFriendlyName && RemoteFriendlyName.calltype && RemoteFriendlyName.calltype.indexOf('IB') > -1) { } else {
                        AgentHangUp(e.description, "Answered", e.getSipResponseCode());
                    }
                }

                // if(e.getSipResponseCode() == 486){ AgentHangUp(e.description,"NotAnswered") }

                break;
            } // 'terminating' | 'terminated'

        case 'm_stream_video_local_added':
            {
                if (e.session == oSipSessionCall) {
                    uiVideoDisplayEvent(true, true);
                }
                break;
            }
        case 'm_stream_video_local_removed':
            {
                if (e.session == oSipSessionCall) {
                    uiVideoDisplayEvent(true, false);
                }
                break;
            }
        case 'm_stream_video_remote_added':
            {
                if (e.session == oSipSessionCall) {
                    uiVideoDisplayEvent(false, true);
                }
                break;
            }
        case 'm_stream_video_remote_removed':
            {
                if (e.session == oSipSessionCall) {
                    uiVideoDisplayEvent(false, false);
                }
                break;
            }

        case 'm_stream_audio_local_added':
        case 'm_stream_audio_local_removed':
        case 'm_stream_audio_remote_added':
        case 'm_stream_audio_remote_removed':
            {
                break;
            }

        case 'i_ect_new_call':
            {
                oSipSessionTransferCall = e.session;
                break;
            }

        case 'i_ao_request':
            {
                if (e.session == oSipSessionCall) {
                    var iSipResponseCode = e.getSipResponseCode();
                    if (iSipResponseCode == 180 || iSipResponseCode == 183) {
                        // startRingbackTone(); ///txtCallStatus.innerHTML = '<i>Remote ringing...</i>';
                        // ///window.localStorage.setItem('callDisposition',"Remote ringing...");
                        setRealtimeDisposition("Remote ringing...");
                    }
                }
                break;
            }

        case 'm_early_media':
            {
                if (e.session == oSipSessionCall) {
                    stopRingbackTone();
                    stopRingTone();
                    // ///txtCallStatus.innerHTML = '<i>Early media started</i>';
                    // ///window.localStorage.setItem('callDisposition',"Early media started");
                    setRealtimeDisposition("Early media started");
                }
                break;
            }

        case 'm_local_hold_ok':
            {
                if (e.session == oSipSessionCall) {
                    if (oSipSessionCall.bTransfering) {
                        oSipSessionCall.bTransfering = false;
                        // this.AVSession.TransferCall(this.transferUri);
                    }
                    btnHoldResume.value = 'Resume';
                    window
                        .localStorage
                        .setItem('setHoldText', "Resume");
                    btnHoldResume.disabled = false;
                    // ///txtCallStatus.innerHTML = '<i>Call placed on hold</i>';
                    // ///window.localStorage.setItem('callDisposition',"Call placed on hold");
                    setRealtimeDisposition("Call placed on hold");
                    oSipSessionCall.bHeld = true;
                }
                break;
            }
        case 'm_local_hold_nok':
            {
                if (e.session == oSipSessionCall) {
                    oSipSessionCall.bTransfering = false;
                    btnHoldResume.value = 'Hold';
                    window
                        .localStorage
                        .setItem('setHoldText', "Hold");
                    btnHoldResume.disabled = false;
                    // ///txtCallStatus.innerHTML = '<i>Failed to place remote party on hold</i>';
                    // ///window.localStorage.setItem('callDisposition',"Failed to place remote party
                    // on hold");
                    setRealtimeDisposition("Call taken off hold");
                }
                break;
            }
        case 'm_local_resume_ok':
            {
                if (e.session == oSipSessionCall) {
                    oSipSessionCall.bTransfering = false;
                    btnHoldResume.value = 'Hold';
                    window
                        .localStorage
                        .setItem('setHoldText', "Hold");
                    btnHoldResume.disabled = false;
                    // ///txtCallStatus.innerHTML = '<i>Call taken off hold</i>';
                    // ///window.localStorage.setItem('callDisposition',"Call taken off hold");
                    setRealtimeDisposition("Call taken off hold");
                    oSipSessionCall.bHeld = false;

                    if (SIPml.isWebRtc4AllSupported()) { // IE don't provide stream callback yet
                        uiVideoDisplayEvent(false, true);
                        uiVideoDisplayEvent(true, true);
                    }
                }
                break;
            }
        case 'm_local_resume_nok':
            {
                if (e.session == oSipSessionCall) {
                    oSipSessionCall.bTransfering = false;
                    btnHoldResume.disabled = false;
                    // ///txtCallStatus.innerHTML = '<i>Failed to unhold call</i>';
                    // ///window.localStorage.setItem('callDisposition',"Failed to unhold call");
                    setRealtimeDisposition("Failed to unhold call");
                }
                break;
            }
        case 'm_remote_hold':
            {
                if (e.session == oSipSessionCall) {
                    // ///txtCallStatus.innerHTML = '<i>Placed on hold by remote party</i>';
                    // ///window.localStorage.setItem('callDisposition',"Placed on hold by remote
                    // party");
                    setRealtimeDisposition("Placed on hold by remote party");
                }
                break;
            }
        case 'm_remote_resume':
            {
                if (e.session == oSipSessionCall) {
                    // ///txtCallStatus.innerHTML = '<i>Taken off hold by remote party</i>';
                    // ///window.localStorage.setItem('callDisposition',"Taken off hold by remote
                    // party");
                    setRealtimeDisposition("Taken off hold by remote party");
                }
                break;
            }
        case 'm_bfcp_info':
            {
                if (e.session == oSipSessionCall) {
                    // ///txtCallStatus.innerHTML = 'BFCP Info: <i>' + e.description + '</i>';
                    // ///window.localStorage.setItem('callDisposition',e.description);
                    setRealtimeDisposition(e.description, e.getSipResponseCode());
                }
                break;
            }

        case 'o_ect_trying':
            {
                if (e.session == oSipSessionCall) {
                    // ///txtCallStatus.innerHTML = '<i>Call transfer in progress...</i>';
                    // ///window.localStorage.setItem('callDisposition',"Call transfer in
                    // progress...");
                    setRealtimeDisposition("Call transfer in progress...");
                }
                break;
            }
        case 'o_ect_accepted':
            {
                if (e.session == oSipSessionCall) {
                    // ///txtCallStatus.innerHTML = '<i>Call transfer accepted</i>';
                    // ///window.localStorage.setItem('callDisposition',"Call transfer accepted");
                    setRealtimeDisposition("Call transfer accepted");
                }
                break;
            }
        case 'o_ect_completed':
        case 'i_ect_completed':
            {
                if (e.session == oSipSessionCall) {
                    // ///txtCallStatus.innerHTML = '<i>Call transfer completed</i>';
                    // ///window.localStorage.setItem('callDisposition',"Call transfer completed");
                    setRealtimeDisposition("Call transfer completed");
                    btnTransfer.disabled = false;
                    if (oSipSessionTransferCall) {
                        oSipSessionCall = oSipSessionTransferCall;
                    }
                    oSipSessionTransferCall = null;
                }
                break;
            }
        case 'o_ect_failed':
        case 'i_ect_failed':
            {
                if (e.session == oSipSessionCall) {
                    // ///txtCallStatus.innerHTML = '<i>Call transfer failed</i>';
                    // ///window.localStorage.setItem('callDisposition',"Call transfer failed");
                    setRealtimeDisposition("Call transfer failed");
                    if (window.localStorage.getItem('feedbackTimer') == "show") {
                        sipHangUp();
                    }
                    btnTransfer.disabled = false;
                }
                break;
            }
        case 'o_ect_notify':
        case 'i_ect_notify':
            {
                if (e.session == oSipSessionCall) {
                    // ///txtCallStatus.innerHTML = "<i>Call Transfer: <b>" + e.getSipResponseCode()
                    // + " " + e.description + "</b></i>";
                    // ///window.localStorage.setItem('callDisposition',"Call Transfer: " +
                    // e.getSipResponseCode() + " " + e.description);
                    setRealtimeDisposition("Call Transfered.");
                    console.log("Call Transfer: " + e.getSipResponseCode() + " " + e.description)
                    if (e.getSipResponseCode() >= 300) {
                        if (oSipSessionCall.bHeld) {
                            oSipSessionCall.resume();
                        }
                        btnTransfer.disabled = false;
                    }
                }
                break;
            }
        case 'i_ect_requested':
            {
                if (e.session == oSipSessionCall) {
                    var s_message = "Do you accept call transfer to [" + e.getTransferDestinationFriendlyName() + "]?"; //FIXME
                    if (confirm(s_message)) {
                        // ///txtCallStatus.innerHTML = "<i>Call transfer in progress...</i>";
                        // ///window.localStorage.setItem('callDisposition',"Call transfer in
                        // progress...");
                        setRealtimeDisposition("Call transfer in progress...");
                        oSipSessionCall.acceptTransfer();
                        break;
                    }
                    oSipSessionCall.rejectTransfer();
                }
                break;
            }
        default:
            break;
    }
}

// function added by amrish
function getParamValue(paramName) {
    var url = window
        .location
        .search
        .substring(1); //get rid of "?" in querystring
    var qArray = url.split('&'); //get key-value pairs
    for (var i = 0; i < qArray.length; i++) {
        var pArr = qArray[i].split('='); //split key and value
        if (pArr[0] == paramName)
            return pArr[1]; //return value
    }
}

// Export.html Settings Start Here
function settingsSave() {
    window
        .localStorage
        .setItem('org.doubango.expert.disable_video', cbVideoDisable.checked ?
            "true" :
            "false");
    window
        .localStorage
        .setItem('org.doubango.expert.enable_rtcweb_breaker', cbRTCWebBreaker.checked ?
            "true" :
            "false");
    if (!txtWebsocketServerUrl.disabled) {
        window
            .localStorage
            .setItem('org.doubango.expert.websocket_server_url', txtWebsocketServerUrl.value);
    }
    window
        .localStorage
        .setItem('org.doubango.expert.sip_outboundproxy_url', txtSIPOutboundProxyUrl.value);
    window
        .localStorage
        .setItem('org.doubango.expert.ice_servers', txtIceServers.value);
    window
        .localStorage
        .setItem('org.doubango.expert.bandwidth', txtBandwidth.value);
    window
        .localStorage
        .setItem('org.doubango.expert.video_size', txtSizeVideo.value);
    window
        .localStorage
        .setItem('org.doubango.expert.disable_early_ims', cbEarlyIMS.checked ?
            "true" :
            "false");
    window
        .localStorage
        .setItem('org.doubango.expert.disable_debug', cbDebugMessages.checked ?
            "true" :
            "false");
    window
        .localStorage
        .setItem('org.doubango.expert.enable_media_caching', cbCacheMediaStream.checked ?
            "true" :
            "false");
    window
        .localStorage
        .setItem('org.doubango.expert.disable_callbtn_options', cbCallButtonOptions.checked ?
            "true" :
            "false");

    txtInfo.innerHTML = '<i>Saved</i>';
}

function settingsRevert(bNotUserAction) {
    cbVideoDisable.checked = (window.localStorage.getItem('org.doubango.expert.disable_video') == "true");
    cbRTCWebBreaker.checked = (window.localStorage.getItem('org.doubango.expert.enable_rtcweb_breaker') == "true");
    txtWebsocketServerUrl.value = (window.localStorage.getItem('org.doubango.expert.websocket_server_url') || "");
    txtSIPOutboundProxyUrl.value = (window.localStorage.getItem('org.doubango.expert.sip_outboundproxy_url') || "");
    txtIceServers.value = (window.localStorage.getItem('org.doubango.expert.ice_servers') || "");
    txtBandwidth.value = (window.localStorage.getItem('org.doubango.expert.bandwidth') || "");
    txtSizeVideo.value = (window.localStorage.getItem('org.doubango.expert.video_size') || "");
    cbEarlyIMS.checked = (window.localStorage.getItem('org.doubango.expert.disable_early_ims') == "true");
    cbDebugMessages.checked = (window.localStorage.getItem('org.doubango.expert.disable_debug') == "true");
    cbCacheMediaStream.checked = (window.localStorage.getItem('org.doubango.expert.enable_media_caching') == "true");
    cbCallButtonOptions.checked = (window.localStorage.getItem('org.doubango.expert.disable_callbtn_options') == "true");

    if (!bNotUserAction) {
        txtInfo.innerHTML = '<i>Reverted</i>';
    }
}
// Export.html Settings End Here Clock Timer Settings
function myTimer() {
    //if(!isRunning){
    hangTimer = 5;
    isRunning = true;
    talkTime = talkTime + 1;
    callTimer.innerHTML = ' ( ' + formatSeconds(talkTime) + ' )';
    window
        .localStorage
        .setItem('setcallTimer', formatSeconds(talkTime));
    var data = window
        .localStorage
        .getItem('ConnectCallSF');
    var ProductID = window.localStorage.getItem('ConnectedLeadProductID');
    if (data != null && data != "") {
        data = JSON.parse(data);
        if (data.campaign == "ntermob" || data.campaign == "termob") {
            hangTimer = 15;
        }
        if (data.campaign && data.campaign.toLowerCase() == "healthrenewal") {
            hangTimer = 10;
        }
    }
    if (parseInt(ProductID) == 106) {
        hangTimer = 30;
    }
    else if (IsInboundCallType && [2, 130, 147, 106, 118].includes(parseInt(ProductID))) {
        hangTimer = 15;
    }
    if (talkTime >= hangTimer) {
        btnHangUp.disabled = false;
        btnHangUp.style.visibility = 'visible';
        window
            .localStorage
            .setItem('setCallStatus', "Hang Up");

        // IF IN ARRAY THEN HIDE HANGUP BUTTON.
        // if(jQuery.inArray(getParamValue('empId'), empHideHangupArray) != -1) {
        // hideHangUp(); } else { showHangUp(); }
    }
    var data = window.localStorage.getItem('ConnectCallSF');
    if (!data) {
        if (ConnectCallSFObj) {
            window.localStorage.setItem('ConnectCallSF', JSON.stringify(ConnectCallSFObj));
            data = window.localStorage.getItem('ConnectCallSF');
        }
    }


    if (data) {
        data = JSON.parse(data);
        ConnectCallSFObj = data;
        //if (data.CheckIVRFeedBack) {
        if (talkTime >= feedbackTimer) {
            try {

                // if (data.lead && $.inArray(parseInt(data.lead.ProductId), [2, 130, 147, 106, 118]) != -1) {
                //     showfeedbackbutton(data.LeadID);
                //     data.CheckIVRFeedBack = false;
                //     window.localStorage.setItem('ConnectCallSF', JSON.stringify(data));
                // }

                var user = window.localStorage.getItem("User");
                user = JSON.parse(window.atob(user));
                let InProduct = false;
                let InGroup = false;
                for (let index = 0; index < user.ProductList.length; index++) {
                    const element = user.ProductList[index];
                    if ($.inArray(parseInt(element.ProductId), [2, 130, 147, 106, 118, 7, 115, 117, 1000, 139]) > -1) {
                        InProduct = true;
                    }
                }

                for (let index = 0; index < user.GroupList.length; index++) {
                    const element = user.GroupList[index];
                    if ($.inArray(parseInt(element.GroupId), [1557]) > -1) {
                        InGroup = true;
                    }
                }
                if (InProduct && InGroup == false) {
                    showfeedbackbutton(data.LeadID);
                    //data.CheckIVRFeedBack = false;
                    //window.localStorage.setItem('ConnectCallSF', JSON.stringify(data));
                }

                // if (user) {
                //     user = JSON.parse(window.atob(user));
                //     for (var i = 0; i < user.ProductList.length; i++) {
                //         if ($.inArray(parseInt(user.ProductList[i].ProductId), [2, 130, 147, 106, 118]) != -1) {
                //             showfeedbackbutton(data.LeadID);
                //             data.CheckIVRFeedBack = false;
                //             window.localStorage.setItem('ConnectCallSF', JSON.stringify(data));
                //         }
                //     }
                // }
            } catch (e) { }
        } else {
            window
                .localStorage
                .setItem('feedbackTimer', "hide");
            //showHangUp();
        }
        //}
    }
}
var showfeedbackbuttonapicall = 0;
function showfeedbackbutton(leadid) {
    if (showfeedbackbuttonapicall == 0) {
        showfeedbackbuttonapicall = 1;
        try {
           
        }
        catch (e) { }
    }
}
function formatSeconds(seconds) {
    var date = new Date(1970, 0, 1);
    date.setSeconds(seconds);
    return date
        .toTimeString()
        .replace(/.*(\d{2}:\d{2}:\d{2}).*/, "$1");
}

// Custom Event to hangup call
function customHangupEventHandler() {
    isInCall = 0;
    var d = new Date();
    var obj = {
        message: 'HangUp'
    }
    window.localStorage.removeItem("calltype");
    window
        .parent
        .postMessage(obj, "*");
    callButtonTrId.style.visibility = 'hidden';
    clearInterval(myCallTimerVar);
    myCallTimerVar = null;
    isRunning = false;
    window
        .localStorage
        .setItem('setcallTimer', "");
    talkTime = 0; // CALL TIMER INITIAL VALUE
    callEndTime = d.toLocaleTimeString();
    //parentWindow().setCall();
    window
        .localStorage
        .setItem('setCallStatus', "Call");
    uid = 0;
    window
        .localStorage
        .setItem("CallDataId", uid);
    //AgentHangUp();
    showMuteRing();
}

// Custom Event to auto answer call
function customCallAcceptEventHandler(e, accept = true) {
    try {
        var d = new Date();
        callButtonTrId.style.visibility = 'visible';
        restrictApiForIncoming = 0;
        if (accept) {
            e.newSession.accept();
        }
        isInCall = 1;
        talkTime = 0;
        if (myCallTimerVar == null) {
            callStartTime = d.toLocaleTimeString();
            myCallTimerVar = setInterval(myTimer, 1000); // START CALL TIMER
            //parentWindow().setHangup();
        }
        // window
        //     .localStorage
        //     .setItem('setCallStatus', "Hang Up");
        hideMuteRing();
    }
    catch (e) { }
}

function runtimer() {
    try {
        var d = new Date();
        talkTime = 0;
        if (myCallTimerVar == null) {
            callStartTime = d.toLocaleTimeString();
            myCallTimerVar = setInterval(myTimer, 1000); // START CALL TIMER
            //parentWindow().setHangup();
        }
    }
    catch (e) {
        console.error(e)
    }
}

function setRealtimeDisposition(disposition, code) {
    console.log("code", code);
    console.log("disposition", disposition);
    if (code && code != undefined && code != '-1') {
        disposition = showDispositionByCode(code).WEB;
    }
    txtCallStatus.innerHTML = disposition;
    window
        .localStorage
        .setItem('callDisposition', disposition);
}

function showMuteRing() {
    jQuery('#btnMuteRemote').show();
    jQuery('#btnMuteRemote').prop('disabled', false);
    window
        .localStorage
        .setItem('ringMuteStatus', true);
    muteMicrophone(true);
    window
        .localStorage
        .setItem('showHideRingtone', "true");
}

function hideMuteRing() {
    jQuery('#btnMuteRemote').hide();
    jQuery('#btnMuteRemote').prop('disabled', true);
    window
        .localStorage
        .setItem('ringMuteStatus', false);
    muteMicrophone(false);
    window
        .localStorage
        .setItem('showHideRingtone', "false");
}

function showHangUp() {
    btnHangUp.value = 'HangUp';
    btnHangUp.disabled = false;
    btnHangUp.style.visibility = 'visible';
    window
        .localStorage
        .setItem('setCallStatus', "Hang Up");
}

function hideHangUp() {
    btnHangUp.disabled = true;
    btnHangUp.style.visibility = 'hidden';
    window
        .localStorage
        .setItem('setCallStatus', "Hang_Up_Pre");
}


function handleAuthFail(updateLogoutStatus = false, timeout = 3000, logoutTypeId = 0, reason = "") {
    // logout and show alert
    setTimeout(function () {
        try {
            if (updateLogoutStatus && SV_CONFIG && !SV_CONFIG.DISABLE_LOGOUT_STATUS_UPDATE) {
                let date = new Date();
                UpdateAgentStatus("LOGOUT", date.getTime(), userobj.EmployeeId);
                UpdateAgentLoginStatus("LOGOUT", date.getTime(), userobj.EmployeeId);
            }

        }
        catch (e) {
            console.error(e);
        }
        setTimeout(() => {
            if (logoutTypeId) {
                parentWindow().top.location = `/pgv/Leads/BlankLogout.aspx?logoutSV=1&logoutTypeId=${logoutTypeId}`;
            } else {
                parentWindow().top.location = "/pgv/LogIn.aspx";
            }
            WindowLogout();
        }, 1000);

    }, timeout);
    try {
        parentWindow().reloginAgent(reason);
    } catch (e) { }
}

function verifySecretToken() {
    if (!oSipSessionCall) {
        let isTokenVerified = readFromLSCache('isTokenVerified');
        checkInactiveTime = checkInactiveTime + 1;
        //if (checkInactiveTime > 120) {
        //    WindowLogout();
        //}

        if (isTokenVerified) {
            //token verified
            return;
        }


        if (isTokenVerified === false) {
            // Auth failed
            handleAuthFail();
        }
        else if (!isTokenVerified) {
            // either Auth Status not updated since 1 minute, or auth status not present in localstorage
            if (secretToken != null && txtDisplayName.value != null && txtDisplayName.value != undefined && txtDisplayName.value != "") {
                var sfGetSecretTokenURL = AppConfig.InternalCustomerNotificationURL + "verifytoken";
                var input = JSON.stringify({ "userid": UserID, "token": secretToken })

                $.ajax({
                    url: sfGetSecretTokenURL,
                    type: 'POST',
                    data: input,
                    dataType: 'json',
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        if (!data) {
                            handleAuthFail();
                        }
                    },
                    error: function (request, error) { }
                });
            }
        }
    }

}

function ConnectCall(phone, data, empId) {
    if (oSipSessionCall == null && data) {
        checkInactiveTime = 0;
        restrictApiForIncoming = 1;
        // data.CallInitTime = JSON.stringify(new Date()).replace(/"/g,''); data.CallId =
        // Math.random(); window.localStorage.setItem('ConnectCallSF',
        // JSON.stringify(data)); leadInfo.innerHTML = data.LeadID ;
        console.log('ConnectCall', data);
        ConnectCallSFObj = data;
        txtPhoneNumber.value = data.DialerCode + '1111111111';
        sipCall("call-audio");
        btnHangUp.disabled = true;
        btnHangUp.style.visibility = 'hidden';
        window
            .localStorage
            .setItem('ringMuteStatus', true);
        muteMicrophone(true);
        window
            .localStorage
            .setItem('showHideRingtone', "true");
        window
            .localStorage
            .setItem('setRingtoneText', 'Mute the ring...');
        var d = new Date();
        callButtonTrId.style.visibility = 'visible';
        isInCall = 1;
        talkTime = 0;
        if (myCallTimerVar == null) {
            callStartTime = d.toLocaleTimeString();
            myCallTimerVar = setInterval(myTimer, 1000); // START CALL TIMER
            //window.localStorage.setItem('setCallStatus', "Hang Up");
        }
        window
            .localStorage
            .setItem('setCallStatus', "Hang_Up_Pre");

        // IF IN ARRAY THEN SHOW MUTERING BUTTON.
        // if(jQuery.inArray(getParamValue('empId'), empHideHangupArray) != -1) {
        // showMuteRing(); } else { hideMuteRing(); }
        localStorage.setItem('onCall', true);
        var user = window.atob(window.localStorage.getItem('User'));
        user = jQuery.parseJSON(user);

        
        var updatecallstatusURL = SV_CONFIG["MatrixCoreAPI"][SV_CONFIG["environment"]] + "onelead/api/Communication/updatecallstatus?leadid=" + data.LeadID + "&status=CallInitiated&userid=" + user.UserId;
        if (data.LeadID == undefined || data.LeadID == null || user.UserId == undefined || user.UserId == null) return;
        $.ajax({
            url: updatecallstatusURL,
            type: 'GET',
            xhrFields: {
                withCredentials: true
            },
            dataType: 'json',
            success: function (data) { },
            error: function (request, error) {
                //alert("Request: " + JSON.stringify(request));
            }
        });

    }
}

function UpdateAgentStatus(cuurentStatus, lastUpdatedOn, agentCode) {

    var input = '{ "status" : "' + cuurentStatus + '", "UserID": "' + UserID + '"}'
    var updateagentstatusURL = AppConfig.InternalCustomerNotificationURL + "updateagentstatus?onCall=true";
    if (isUserWebphoneNew()) {
        return;
    }
    $.ajax({
        url: updateagentstatusURL,
        type: 'POST',
        data: input,
        dataType: 'json',
        contentType: "application/json; charset=utf-8",
        success: function (data) { },
        error: function (request, error) { }
    });

}
function UpdateAgentLoginStatus(cuurentStatus, lastUpdatedOn, agentCode) {

    var input = '{ "status" : "' + cuurentStatus + '", "UserID": "' + UserID + '"}'
    var UpdateAgentLoginStatusURL = AppConfig.InternalCustomerNotificationURL + "updateagentloginstatus";
    $.ajax({
        url: UpdateAgentLoginStatusURL,
        type: 'POST',
        data: input,
        dataType: 'json',
        contentType: "application/json; charset=utf-8",
        success: function (data) { },
        error: function (request, error) { }
    });

}


setInterval(function () {
    var date = new Date();
    var user = window
        .localStorage
        .getItem('User');
    if (user && user != "" && oSipStack && oSipSessionRegister) {
        localStorage.setItem('isRegistered', true);
        user = window.atob(window.localStorage.getItem('User'));
        user = jQuery.parseJSON(user);
        let isTokenVerified = readFromLSCache('isTokenVerified');
        if (isAswatIndiaUser()) {
            /**
             *  for ASWATINDIA status will be handled via webphoneControl.js file
             */
            return;
        }

        if (oSipSessionCall != null) {
            cuurentStatus = "BUSY";
            if (!isUserWebphoneNew()) {
                localStorage.setItem('onCall', true);
            }
        }
        else if (!IsMicrophoneEnable) {
            cuurentStatus = "MIC-OFF";
            localStorage.setItem('onCall', false);
        }
        else if (!isTokenVerified) {
            cuurentStatus = "UNAVAILABLE";
            localStorage.setItem('onCall', false);
        }
        else {
            cuurentStatus = "IDLE";
            localStorage.setItem('onCall', false);
        }
        if (!alwaysConnectCall) {
            UpdateAgentStatus(cuurentStatus, date.getTime(), user.EmployeeId);
        }
    }
    else {
        localStorage.setItem('isRegistered', false);
    }
}, 3000);

function AgentAnswerd() {
    if (restrictApiForIncoming == 0) {
        return false;
    }
    var data = window
        .localStorage
        .getItem('ConnectCallSF');

    if (!data) {
        if (ConnectCallSFObj) {
            window.localStorage.setItem('ConnectCallSF', JSON.stringify(ConnectCallSFObj));
            data = window.localStorage.getItem('ConnectCallSF');
        }
    }
    if (data != null && data != "") {
        data = JSON.parse(data);

        data.callDate = data.callDate ?
            data.callDate :
            JSON
                .stringify(new Date())
                .replace(/"/g, '');
        window
            .localStorage
            .setItem('ConnectCallSF', JSON.stringify(data));

    }
    if (alwaysConnectCall) { return; }
    if (!data) return;
    var url = SV_CONFIG["MatrixCoreAPI"][SV_CONFIG["environment"]];
    var ConnectCallSFURL = url + "onelead/api/LeadPrioritization/InsertCallData?CallID=" + data.CallId + "&LeadID=" + data.LeadID + "&AgentID=" + data.empId + "&callDate=" + data.callDate + "&callType=OB&uid=" + data.uid + "&Context=" + data.campaign + "&dst=" + data.SF + "&action=agentanswered";

    $.ajax({
        url: ConnectCallSFURL,
        type: 'GET',
        xhrFields: {
            withCredentials: true
        },
        dataType: 'json',
        success: function (data) { },
        error: function (request, error) {
            //alert("Request: " + JSON.stringify(request));
        }
    });
}

// Helper function for getting cookie
const getCookie = (name) => {
    if (document) {
        name = name || 'token';
        let nameEQ = name + "=";
        let ca = document.cookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) == ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }
}

const findLeadIdAndCallIdOfSS = () => {

    const currentSSCallId = getCookie('currentSSCallId');
    let last5VcLeadData = readFromLSCache('last5VcLead');
    if (currentSSCallId && Array.isArray(last5VcLeadData)) {
        const room = last5VcLeadData.find(roomData => roomData.roomId === currentSSCallId);
        if (room) {
            return { callId: currentSSCallId, leadId: room.leadId };
        }
    }
    return null;
}

function AgentHangUp(Disposition, status, code) {
    if (noCallRunningTimerId) {
        clearTimeout(noCallRunningTimerId);
    }
    noCallRunningTimerId = setTimeout(() => {

        const connectCallSF = getConnectCallSFFromLS();
        let onCall = localStorage.getItem('onCall');
        onCall = (onCall === "true" ? true : false)

        const callData = findLeadIdAndCallIdOfSS();

        if (connectCallSF && onCall && callData && connectCallSF.LeadID == callData.leadId) {
            return;
        }

        if (callData && callData.leadId && callData.callId) {
            const input = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    callId: callData.callId,
                    leadId: callData.leadId,
                    logoutReason: "Oops !! The screen sharing was disconnected because the voice call wasn't active"
                }),
                credentials: 'include'
            };
            const endPoint = 'api/salesview/RemoveModeratorFromRoomPbmeet';
            const url = SV_CONFIG["MatrixCoreAPI"][SV_CONFIG["environment"]] + endPoint;
            fetch(url, input)
                .then(response => {
                    if (response.error) {
                        console.error('Error Occured: ' + response.message);
                    } else if (response.status == 'success') {
                        console.log(response.message)
                    }
                })
        }
    }, 30000)

    return;
    if (restrictApiForIncoming == 0) {
        return false;
    }
    showfeedbackbuttonapicall = 0;
    window
        .localStorage
        .setItem('feedbackTimer', "hide");

    leadInfo.innerHTML = '';
    //alert(Disposition);
    var data = window
        .localStorage
        .getItem('ConnectCallSF');
    var HangUptalkTime = 0;
    var durationTime = 0;
    if (data != "null" && data != "" && data) {
        data = JSON.parse(data);
        if (!data) return;

        if (data.callDate) {
            HangUptalkTime = (new Date() - new Date(data.callDate)) / 1000;
            durationTime = (new Date() - new Date(data.callDate)) / 1000;
        }
        if (data.CallInitTime) {
            durationTime = (new Date() - new Date(data.CallInitTime)) / 1000;
        }

        if (HangUptalkTime > 0) {
            status = "16";
            Disposition = "Answered";
            window
                .localStorage
                .setItem('AnswerCallTime', new Date());
        } else {
            status = code;
            Disposition = showDispositionByCode(code).DB;
        }

        var lead = window
            .localStorage
            .getItem('ConnectCallSF');
        lead = JSON.parse(lead);

        /*if (lead && durationTime < 4 && CallTryCount < 1 && (Disposition == "Forbidden" || Disposition == "NotFound")) {
            ConnectCall(lead.Phone, lead, lead.empId);
            CallTryCount = CallTryCount + 1;
            return;
        }*/

        CallTryCount = 0;

        if (data.callDate) {
            var url = AppConfig.CommBox;
            // var ConnectCallSFURL = url + 
            // "Asteriskevent.svc/InsertCallData?CallID=" + data.CallId + "&LeadID=" + data.LeadID + "&AgentID=" + data.empId + "&callDate=" + data.callDate + "&callType=OB&talkTime=" + parseInt(HangUptalkTime) + "&Duration=" + parseInt(durationTime) + "&Disposition=" + Disposition + "&status=" + status + "&uid=" + data.uid + "&dst=" + data.SF + "&Context=" + data.campaign + "&action=hangup";

        } else {
            var url = AppConfig.CommBox;
            // var ConnectCallSFURL = url + 
            // "Asteriskevent.svc/InsertCallData?CallID=" + data.CallId + "&LeadID=" + data.LeadID + "&AgentID=" + data.empId + "&callType=OB&talkTime=" + parseInt(HangUptalkTime) + "&Duration=" + parseInt(durationTime) + "&Disposition=" + Disposition + "&status=" + status + "&uid=" + data.uid + "&dst=" + data.SF + "&Context=" + data.campaign + "&action=hangup";

        }

        var PredictiveDialLeads = localStorage.getItem('PredictiveDialLeads');
        if (PredictiveDialLeads != null) {
            PredictiveDialLeads = JSON.parse(PredictiveDialLeads);
            PredictiveDialLeads.CallStatus = "hangup";
            localStorage.setItem('PredictiveDialLeads', JSON.stringify(PredictiveDialLeads));
        }
        window.localStorage.setItem('onCall', false);
        window.localStorage.removeItem('ConnectCallSF');

        if (lead.IsThirdParty) return;

        //While hanup store dispositionCode
        window.localStorage.setItem('dispositionCode', code);

        // $.ajax({
        // url: ConnectCallSFURL,
        // type: 'GET',
        // dataType: 'json',
        // success: function (data) {

        // if (!data) {
        // window
        // .localStorage
        // .setItem('ConnectCallSFURL', ConnectCallSFURL);
        // }

        // var codes = [503, 403]
        // if (codes.indexOf(code) > -1) {
        // // sipUnRegister();
        // // sipRegister();
        // //window.onbeforeunload = null;
        // //sipUnRegister();
        // //setTimeout(function () {
        // //	window.reload();
        // //}, 500);
        // }
        // },
        // error: function (request, error) {
        // window
        // .localStorage
        // .setItem('ConnectCallSFURL', ConnectCallSFURL);

        // //var codes = [503, 403]
        // //if (codes.indexOf(code) > -1) {
        // // sipUnRegister();
        // // sipRegister();
        // //  window.onbeforeunload = null;
        // // sipUnRegister();
        // //setTimeout(function () {
        // //  window.reload();
        // //}, 500);
        // //}
        // }
        // });
    } else {
        //alert("data not found");
    }

}


function showDispositionByCode(code) {
    if (code) {
        var obj = {
            '100': {
                'DB': 'Trying',
                'WEB': 'Trying'
            },
            '180': {
                'DB': 'Ringing',
                'WEB': 'Ringing'
            },
            '182': {
                'DB': 'Queued',
                'WEB': 'Queued'
            },
            '183': {
                'DB': 'SessionInProgress',
                'WEB': 'Session In Progress'
            },
            '199': {
                'DB': 'EarlyDialogTerminated',
                'WEB': 'Early Dialog Terminated'
            },
            '200': {
                'DB': 'OK',
                'WEB': 'OK'
            },
            '202': {
                'DB': 'Accepted',
                'WEB': 'Accepted'
            },
            '204': {
                'DB': 'NoNotification',
                'WEB': 'No Notification'
            },
            '300': {
                'DB': 'MultipleChoices',
                'WEB': 'Multiple Choices'
            },
            '301': {
                'DB': 'MovedPermanently',
                'WEB': 'Moved Permanently'
            },
            '305': {
                'DB': 'UseProxy',
                'WEB': 'Use Proxy'
            },
            '380': {
                'DB': 'AlternativeService',
                'WEB': 'Alternative Service'
            },
            '400': {
                'DB': 'BadRequest',
                'WEB': 'Bad Request'
            },
            '401': {
                'DB': 'Unauthorized',
                'WEB': 'Unauthorized'
            },
            '402': {
                'DB': 'PaymentRequired',
                'WEB': 'Payment Required'
            },
            '403': {
                'DB': 'Forbidden',
                'WEB': 'Call Not Reachable'
            },
            '404': {
                'DB': 'NotFound',
                'WEB': 'Not Found'
            },
            '405': {
                'DB': 'MethodNotAllowed',
                'WEB': 'Method Not Allowed'
            },
            '406': {
                'DB': 'NotAcceptable',
                'WEB': 'Not Acceptable'
            },
            '407': {
                'DB': 'ProxyAuthenticationRequired',
                'WEB': 'Proxy Authentication Required'
            },
            '408': {
                'DB': 'RequestTimeOut',
                'WEB': 'Request Timeout'
            },
            '409': {
                'DB': 'Conflict',
                'WEB': 'Conflict'
            },
            '410': {
                'DB': 'Gone',
                'WEB': 'Gone'
            },
            '411': {
                'DB': 'LengthRequired',
                'WEB': 'Length Required'
            },
            '412': {
                'DB': 'ConditionalRequestFailed',
                'WEB': 'Conditional Request Failed'
            },
            '413': {
                'DB': 'RequestEntityTooLarge',
                'WEB': 'Request Entity Too Large'
            },
            '414': {
                'DB': 'RequestURITooLong',
                'WEB': 'Request URI Too Large'
            },
            '415': {
                'DB': 'UnsupportedMediaType',
                'WEB': 'Unsupported Media Type'
            },
            '416': {
                'DB': 'UnsupportedMediaScheme',
                'WEB': 'Unsupported Media Scheme'
            },
            '417': {
                'DB': 'UnknownResourcePriority',
                'WEB': 'Unknown Resource Priority'
            },
            '420': {
                'DB': 'BadExtension',
                'WEB': 'Bad Extension'
            },
            '421': {
                'DB': 'ExtensionRequired',
                'WEB': 'Extension Required'
            },
            '422': {
                'DB': 'SessionIntervalTooSmall',
                'WEB': 'Session Interval Too Small'
            },
            '423': {
                'DB': 'IntervalTooBrief',
                'WEB': 'Interval Too Brief'
            },
            '424': {
                'DB': 'BadLocationInformation',
                'WEB': 'Bad Location Information'
            },
            '428': {
                'DB': 'UseIdentityHeader',
                'WEB': 'Use Identity Header'
            },
            '429': {
                'DB': 'ProvideReferrerIdentity',
                'WEB': 'Provide Referrer Identity'
            },
            '430': {
                'DB': 'FlowFailed',
                'WEB': 'Flow Failed'
            },
            '433': {
                'DB': 'AnonymityDisallowed',
                'WEB': 'Anonymity Disallowed'
            },
            '436': {
                'DB': 'BadIdentityInfo',
                'WEB': 'Bad Identity Info'
            },
            '437': {
                'DB': 'UnsupportedCertificate',
                'WEB': 'Unsupported Certificate'
            },
            '438': {
                'DB': 'InvalidIdentityHeader',
                'WEB': 'Invalid Identity Header'
            },
            '439': {
                'DB': 'FirstHopLacksOutboundSupport',
                'WEB': 'First Hop Lacks Outbound Support'
            },
            '440': {
                'DB': 'MaxBreadthExceeded',
                'WEB': 'Max Breadth Exceeded'
            },
            '469': {
                'DB': 'BadInfoPackage',
                'WEB': 'BadInfoPackage'
            },
            '470': {
                'DB': 'ConsentNeeded',
                'WEB': 'Consent Needed'
            },
            '480': {
                'DB': 'TemporarilyUnavailable',
                'WEB': 'Call Not Reachable'
            },
            '481': {
                'DB': 'CallOrTransactionDoesNotExist',
                'WEB': 'Call Or Transaction Does Not Exist'
            },
            '482': {
                'DB': 'LoopDetected',
                'WEB': 'Loop Detected'
            },
            '483': {
                'DB': 'TooManyHops',
                'WEB': 'Too Many Hops'
            },
            '484': {
                'DB': 'AddressIncomplete',
                'WEB': 'Address Incomplete'
            },
            '485': {
                'DB': 'Ambiguous',
                'WEB': 'Ambiguous'
            },
            '486': {
                'DB': 'Busy',
                'WEB': 'Busy'
            },
            '487': {
                'DB': 'RequestTerminated',
                'WEB': 'Request Terminated'
            },
            '488': {
                'DB': 'NotAcceptable',
                'WEB': 'Not Acceptable'
            },
            '489': {
                'DB': 'BadEvent',
                'WEB': 'Bad Event'
            },
            '491': {
                'DB': 'RequestPending',
                'WEB': 'Request Pending'
            },
            '493': {
                'DB': 'Undecipherable',
                'WEB': 'Undecipherable'
            },
            '494': {
                'DB': 'SecurityAgreementRequired',
                'WEB': 'Security Agreement Required'
            },
            '500': {
                'DB': 'InternalServerError',
                'WEB': 'Internal Server Error'
            },
            '501': {
                'DB': 'NotImplemented',
                'WEB': 'Not Implemented'
            },
            '502': {
                'DB': 'BadGateway',
                'WEB': 'Bad Gateway'
            },
            '503': {
                'DB': 'ServiceUnavailable',
                'WEB': 'Call Not Reachable'
            },
            '504': {
                'DB': 'ServerTimeOut',
                'WEB': 'Server Timeout'
            },
            '505': {
                'DB': 'VersionNotSupported',
                'WEB': 'Version Not Supported'
            },
            '513': {
                'DB': 'MessageTooLarge',
                'WEB': 'Message Too Large'
            },
            '580': {
                'DB': 'PreconditionFailure',
                'WEB': 'Precondition Failure'
            },
            '600': {
                'DB': 'BusyEverywhere',
                'WEB': 'Busy Everywhere'
            },
            '603': {
                'DB': 'Decline',
                'WEB': 'Decline'
            },
            '604': {
                'DB': 'DoesNotExistAnywhere',
                'WEB': 'Does Not Exist Anywhere'
            },
            '606': {
                'DB': 'NotAcceptable',
                'WEB': 'Not Acceptable'
            },
            '607': {
                'DB': 'Unwanted',
                'WEB': 'Unwanted'
            }

        };
        if (obj[code]) {
            return obj[code];
        } else {
            //alert(code);
            return { 'DB': '', 'WEB': '' };
        }
    } else {
        return { 'DB': 'NotAnswered', 'WEB': 'Not Answered' };
    }
}

function ErrorCaseConnectCall(url, index) {
    //var url = window.localStorage.getItem('ConnectCallSFURL');
    if (url != "" && url != null && url != undefined) {
        $.ajax({
            url: url,
            type: 'GET',
            dataType: 'json',
            success: function (data) {
                if (!data) {
                    window
                        .localStorage
                        .setItem('ConnectCallSFURL', ConnectCallSFURL);
                } else {
                    window
                        .localStorage
                        .removeItem('ConnectCallSFURL', ConnectCallSFURL);
                }
            },
            error: function (request, error) {
                //alert("Request: " + JSON.stringify(request));
                window
                    .localStorage
                    .setItem('ConnectCallSFURL', ConnectCallSFURL);

            }
        });
    }
}

setTimeout(function () {
    ErrorCaseConnectCall();

}, 3000)

function toggleMicrophone() {
    if (window.localStorage.getItem('ringMuteStatus') == "true") {
        muteMicrophone(false);
    } else if (window.localStorage.getItem('ringMuteStatus') == "false") {
        muteMicrophone(true);
    }
}

function muteMicrophone(bEnabled) {
    if (oSipSessionCall != null) {
        if (oSipSessionCall.o_session != null) {
            if (oSipSessionCall.o_session.o_stream_remote != null) {
                if (oSipSessionCall.o_session.o_stream_remote.getAudioTracks().length > 0) {
                    for (var nTrack = 0; nTrack < oSipSessionCall.o_session.o_stream_remote.getAudioTracks().length; nTrack++) {
                        oSipSessionCall
                            .o_session
                            .o_stream_remote
                            .getAudioTracks()[nTrack]
                            .enabled = bEnabled;
                    }
                    window
                        .localStorage
                        .setItem('ringMuteStatus', bEnabled);

                    // ///txtCallStatus.innerHTML = bEnabled ? '<i>Mute the Ring...</i>' : '<i>Unmute
                    // the Ring...</i>'; ///window.localStorage.setItem('callDisposition',bEnabled ?
                    // 'Mute the ring...' : 'Unmute the ring...');
                    setRealtimeDisposition(bEnabled ?
                        'Mute the ring...' :
                        'Unmute the ring...');
                    window
                        .localStorage
                        .setItem('setRingtoneText', bEnabled ?
                            'Mute the ring...' :
                            'Unmute the ring...');
                } else {
                    // console.log("-->>>> muteMicrophone->
                    // oSipSessionCall.o_session.o_stream_local-> NO AUDIO TRACKS");
                }
            } else {
                // console.log("-->>>> muteMicrophone-> oSipSessionCall.o_session.o_stream_local
                // is NULL");
            }
        } else {
            //console.log("-->>>> muteMicrophone-> oSipSessionCall.o_session is NULL");
        }
    } else {
        //console.log("-->>>> muteMicrophone-> oSipSessionCall  is NULL");
    }
}

function sipAccept(toOpenSVLead = true) {
    if (oSipSessionCall) {
        if (toOpenSVLead) {
            localStorage.setItem("opensvleadid", ibleadid);
        }
        oSipSessionCall.accept(oConfigCall);
        ibleadid = 0;
    }
}


function MicrophoneEnable() {
    navigator.mediaDevices.getUserMedia({ audio: true })
        .then(function (stream) {
            IsMicrophoneEnable = true;
        })
        .then(() => navigator.mediaDevices.enumerateDevices())
        .catch(function (err) {
            IsMicrophoneEnable = false;

            console.warn('No mic for you!  ', err)
        });
}

function CheckMicroPhone() {
    return IsMicrophoneEnable;
}


function isUserWebphoneNew() {
    let User = window.localStorage.getItem('User')
    if (User) {
        User = JSON.parse(window.atob(User));
        if (User.CallingCompany && User.CallingCompany && (['WEBPHONE_NEW'].includes(User.CallingCompany))) {
            return true;
        }
    }
}
function readFromLSCache(key) {
    var data = null;
    try {
        data = JSON.parse(localStorage.getItem(key));
    } catch {
        data = null;
    }
    if (data !== null) {
        if (
            (data.expires_at !== null && data.expires_at < new Date().getTime()) // flush expired data
            || !((typeof data === 'object') && ("value" in data) && ("expires_at" in data)) // condition to handle previous data stored, in other format
        ) {
            localStorage.removeItem(key);
        } else {
            return data.value;
        }
    }
    return null;
};
function writeToLSCache(key, value, ttl_ms) {
    let data = { value: value };
    try {
        data = { value: value, expires_at: new Date().getTime() + ttl_ms / 1 };
    } catch { }
    try {
        localStorage.setItem(key, JSON.stringify(data));
    }
    catch { }
};
function getConnectCallSFFromLS() {
    let ConnectCallSF = window.localStorage.getItem('ConnectCallSF');
    // let onCall = window.localStorage.getItem("onCall") === "true" ? true : false;

    try {
        ConnectCallSF = JSON.parse(ConnectCallSF);
    }
    catch { ConnectCallSF = null; }
    return ConnectCallSF;

}

function checkUserGroup(checkGrpsArray) {
    let newAPIGroups = [];
    let isUserGrp = false;
    if (Array.isArray(checkGrpsArray)) {
        newAPIGroups = checkGrpsArray
    } else { return false; }
    let usergrp = userobj.UserGroupList || [];
    usergrp.forEach(function (item) {
        if (Array.isArray(newAPIGroups) && (newAPIGroups.indexOf(item.GroupId) > -1)) {
            isUserGrp = true;
        }
    });
    return isUserGrp;
}

const gaEventTracker = (eventAction, eventLabel, leadId, eventCategory = "SalesView", eventValue = 1) => {
    try {
        const data = {
            eventCategory: eventCategory,
            eventAction: eventAction,
            eventLabel: eventLabel,
            eventValue: eventValue,
            leadid: leadId,
            //customerid: ""  //todo
        };

        window.dataLayer = window.dataLayer || [];
        if (!window.gtag) {
            // eslint-disable-next-line no-undef
            window.gtag = () => { window.dataLayer.push(arguments); }
        }
        window.gtag("event", 'eventTracking', data);
    }
    catch (e) {
        // eslint-disable-next-line no-console
        console.error('gaError error: ', e);
    }
}

const isAswatIndiaUser = () => {

    var user = window.atob(window.localStorage.getItem('User'));
    user = jQuery.parseJSON(user);

    if (user.CallingCompany === "ASWATINDIA") {
        return true;
    }
    return false;
}


const IsVCActive = (lastActiveWithinSec = 15) => {

    try {

        if (SV_CONFIG.disableIsVCActiveChk) {
            return false;
        }
        if (SV_CONFIG.lastActiveWithinSec) {
            lastActiveWithinSec = SV_CONFIG.lastActiveWithinSec;
        }

        const lastActiveVC = getCookie('lastActiveVC');

        if (lastActiveVC) {

            const currentTime = new Date().getTime();
            const lastActiveTime = parseInt(lastActiveVC);
            const timeDifference = (currentTime - lastActiveTime) / 1000;

            if (timeDifference < lastActiveWithinSec) {
                return true;
            }
        }
    } catch {

    }
    return false;
}