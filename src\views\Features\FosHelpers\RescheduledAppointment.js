import React, { useEffect, useState } from "react";
import RoomIcon from '@mui/icons-material/Room';
import '../assets/styles/rescheduled.css'
import { AddressNew } from "./fosMasters";
import { useSnackbar } from 'notistack';
import { But<PERSON>, Grid } from "@mui/material";
import { CONFIG, SV_CONFIG } from "../../../appconfig";
import { useQuery } from "../../../hooks/useQuery";
import withStyles from '@mui/styles/withStyles';
import MuiDialogTitle from '@mui/material/DialogTitle';
import MuiDialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import Typography from '@mui/material/Typography';
import SearchMap from "./SearchMap";
import AppointmentPositionMap from "./AppointmentPositionMap";
import { useLoadScript } from "@react-google-maps/api";
import { GetAppointmentDataService, UpdateAppointmentStatusService } from "./fosServices";
import { getProductNamebyId } from "../../../services/Common";
import { LinearProgress } from "@mui/material";
import dayjs from "dayjs";
import { TextInput } from "../../../components";
import { useSelector } from "react-redux";
import { IsSourceCustomerWhatsapp } from "./fosServices";
import { bypassGenderInput } from "../../Features/FosHelpers/fosCommonHelper";
import { SetAppointmentDataService } from "./fosServices";
import { AskCustomerLocation, GetCustomerLocationData } from "./fosServices";



const styles = (theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(2),
  },
  closeButton: {
    position: 'absolute',
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
});
const libraries = ["places"];
const DialogTitle = withStyles(styles)((props) => {
  const { children, classes, onClose, ...other } = props;
  return (
    <MuiDialogTitle disableTypography className={classes.root} {...other}>
      <Typography variant="h6">{children}</Typography>
      {onClose ? (
        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={onClose}
          size="large">
          <CloseIcon />
        </IconButton>
      ) : null}
    </MuiDialogTitle>
  );
});

const DialogContent = withStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
}))(MuiDialogContent);


const RescheduledAppointment = (props) => {
  const { children, classes, onClose, ...other } = props;
  const [AppointmentPosition, setAppointmentPosition] = useState(null);
  const [UserSelectedPosition, setUserSelectedPosition] = useState(null);
  const [Map, setMap] = useState(null);
  // const classes = useStyles();
  const [person, setPerson] = useState({});
  const [productId, setProductId] = useState(0);
  const [CustomerId, setCustomerId] = useState(0);
  const [parentLeadId, setparentLeadId] = useState(0);
  const [token, setToken] = useState(0);
  const [subStatusId, setSubStatusId] = useState(0);
  const [showConfirmbtn, setShowConfirmbtn] = useState(false);
  const [Address, setAddress] = useState("");
  const [Loading, setLoading] = useState(true);
  const [StatusId, setStatusId] = useState(0);
  const { enqueueSnackbar } = useSnackbar();
  const [IsAppointment, setIsAppointment] = useState(false);
  const [IsSomethingwentwrong, setIsSomethingwentwrong] = useState(false);
  const [AppointmentMessage, setAppointmentMessage] = useState("");
  const [IsUpcoming, setIsUpcoming] = useState(false);
  const [CustomerSource, setCustomerSource] = useState("");
  const [appointmentDateTime, setAppointmentDateTime] = useState();
  const [NewAddress, setNewAddress] = useState(AddressNew);
  const [HouseNo, setHouseNo] = useState(null);
  const [IsCustLocationConfirmed, setIsCustLocationConfirmed] = useState(true);
  const [IsNewTrigger, setIsNewTrigger] = useState(false);
  const [IsCustomerLocationData, setIsCustomerLocationData] = useState(false);
  const [AppointmentCity, setAppointmentCity] = useState(null);
  let [IsShowSaveBtn, setIsShowSaveBtn] = useState(true);
  let query = useQuery();
  const [UserSelectedAddress, IsSearchBarCall] = useSelector(state => {
    let { UserSelectedAddress, IsSearchBarCall } = state.salesview;
    return [UserSelectedAddress, IsSearchBarCall]
  });
  let IsSrcCustomerWhatsapp = IsSourceCustomerWhatsapp();
  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey:SV_CONFIG && SV_CONFIG.googleMapsApiKey,//process.env.REACT_APP_GOOGLE_MAPS_API_KEY,
    libraries

  });


  const getPersonAppointmentDetails = () => {
    let routeParams = window.location.pathname.toLowerCase().split('/');
    if (!routeParams) {
      return 0;
    }
    if (routeParams.includes('rescheduleappointment')) {
      let _LeadId = query.get("l");
      _LeadId = _LeadId.replace(/ /g, '+');
      let src = query.get("src");
      setCustomerSource(src);
      let productId = parseInt(query.get("p"));
      let token = parseInt(query.get("t"));
      let customerID = query.get("c");
      customerID = customerID.replace(/ /g, '+');

      setProductId(productId)
      setparentLeadId(_LeadId);
      setToken(token);
      setCustomerId(customerID);
      window.localStorage.setItem('CustomerToken', token)
      window.localStorage.setItem('parentLeadId', _LeadId)
      window.localStorage.setItem('EncryptedLeadId', _LeadId)
      window.localStorage.setItem('EncryptedCustomerId', customerID)
      window.localStorage.setItem("CustomerWhatsappURL", window.location.href);

      GetAppointmentDataService(customerID, _LeadId).then(function (response) {

        if (response && response.CustomerId > 0) {

          setSubStatusId(response.subStatusId);
          setStatusId(response.StatusId)
          if (response.subStatusId < 2088 && response.subStatusId != 2004 && response.subStatusId != 2003 && response.subStatusId != 2124) {
            setShowConfirmbtn(true);
          }
          if (response.subStatusId == 2004) {
            setAppointmentMessage("This appointment is cancelled")
          }
          if (response.subStatusId == 2003) {
            setAppointmentMessage("This appointment is already completed")
          }
          if (response.subStatusId == 2124) {

            setAppointmentMessage("This appointment is currently in progress")
          }
          if ((new Date(response.AppointmentDateTime)) > new Date()) {
            setIsUpcoming(true);
          }

          var _person = {
            "CustomerName": response.CustomerName,
            "AppointmentDateTime": response.AppointmentDateTime,
            "ProductName": getProductNamebyId(productId),
            "Landmark": response.Landmark

          }
          setPerson(_person)

          let _address = response.Address ? response.Address + ", " + response.Landmark : "";
          let _address1 = response.Address1 ? response.Address1 + ", " : "";
          let _city = response.City ? response.City + ", " : "";
          let _pincode = response.Pincode ? response.Pincode : "";

          let _Address = _address + _address1 + _city + _pincode
          setAddress(_Address);
          setAppointmentDateTime(response.AppointmentDateTime);
          setIsCustLocationConfirmed(response.IsCustLocationConfirmed)
          //  setStatusId(response.StatusId);
          setHouseNo(response.Address);
          setNewAddress(prevState => ({ ...prevState, ...response, place_id: response.location && response.location.place_id }));
          setAppointmentPosition({ lat: response.location.Lat, lng: response.location.Long })
          setUserSelectedPosition({ lat: response.location.Lat, lng: response.location.Long })
          setLoading(false);
          setIsAppointment(true);
          setIsNewTrigger(response.IsNewTrigger)
          setAppointmentCity(response.City);
        }
        else {
          GetCustomerLocationData(customerID, _LeadId).then((response) => {
            if (response.status = true && !!response.Data) {
              console.log('555555', response)
              // setAppointmentPosition({	"lat":28.457523,
              // "lng":77.026344})
              setIsNewTrigger(true)
              setIsCustomerLocationData(true);
              setAppointmentCity(response.Data.City);
              setHouseNo(response.Data.Address);
              setNewAddress(prevState => ({ ...prevState, OfflineCityId: response.Data.CityId, CountryId: response.Data.CountryId, OfflineCity: response.Data.City, place_id: response.Data.PlaceId }));
              setAppointmentPosition({ lat: response.Data.Lat, lng: response.Data.Long })
              setUserSelectedPosition({ lat: response.Data.Lat, lng: response.Data.Long })

            }

            setLoading(false);
            setIsAppointment(false);

          })

        }
      }

      ).catch((error) => {
        if (error.status == 401) {
          console.log("Error in Reschduling appoitment")
          enqueueSnackbar("Not Authorised or Invalid Token ", {
            variant: 'error',
            autoHideDuration: 3000,
          });
        } else {
          enqueueSnackbar("Something went wrong, Please try refreshing again ", {
            variant: 'error',
            autoHideDuration: 3000,
          });
        }
        setLoading(false);
        setIsSomethingwentwrong(true);
        //setIsAppointment(false);
      });

    }
  }

  const ConfirmAppointment = () => {
    let reqData = {

      "LeadID": 0,
      "UserID": 4020,
      "SubStatusId": 2088,
      "StatusId": StatusId || 4,
      "EncryptedCustomerId": window.localStorage.getItem("EncryptedCustomerId"),
      "EncryptLeadId": window.localStorage.getItem("EncryptedLeadId"),
      "Source": CustomerSource
    }
    UpdateAppointmentStatusService(reqData).then(() => {
      setShowConfirmbtn(false);
      getPersonAppointmentDetails();
    });


  }

  useEffect(() => {
  if(JSON.stringify(SV_CONFIG)!='{}')
    getPersonAppointmentDetails();
  }, [])
  const OpenFOSPanel = () => {

    window.open('./fosAppointment?src=' + CustomerSource + '&l=' + parentLeadId + '&c=' + CustomerId + '&p=' + productId + '&t=' + token, "_self")
  }


  const DetectMyLocation = () => {
    if ("geolocation" in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setUserSelectedPosition({ lat: position.coords.latitude, lng: position.coords.longitude })
          // const bounds = new window.google.maps.LatLngBounds({ lat: position.coords.latitude, lng: position.coords.longitude });
          // if (Map)
          //   Map.fitBounds(bounds);

        },
        (error) => {
          console.error("Error getting location:", error);
        }
      );
    } else {
      console.error("Geolocation is not available");
    }
  }


  const validateAppointmentData = () => {

    if (!UserSelectedAddress || (UserSelectedAddress && !UserSelectedAddress.formatted_address)) {
      enqueueSnackbar("Please map your address correctly!", { variant: 'error', autoHideDuration: 3000 });
      return false;
    }
    else if (NewAddress && UserSelectedAddress && Array.isArray(UserSelectedAddress.CityList) && UserSelectedAddress.CityList.length>0 &&UserSelectedAddress.CityList.indexOf(NewAddress.OfflineCityId)==-1) {
      enqueueSnackbar('Oops!! Please reach out to us at 1800-208-8787 if you want to change the city.',
        { variant: 'error', autoHideDuration: 5000, })

      return false;
    }
    else if (!HouseNo) {
      enqueueSnackbar("Please Put your Building No/Flat No.", { variant: 'error', autoHideDuration: 3000 });
      return false;
    }
    else if (HouseNo && HouseNo.trim().length < 10) {
      enqueueSnackbar("Minimum 10 characters are required for Building No/Flat No.", { variant: 'error', autoHideDuration: 3000 });
      return false;
    }
    return true;
  }


  const ConfirmLocation = () => {

    let newAddress = NewAddress;

    const isValid = validateAppointmentData();

    if (!isValid) {
      return;
    }

    let reqData = {
      "CustomerId": 0,
      "ParentId": 0,
      "UserId": 4020,
      "OfflineCityId": newAddress.OfflineCityId || 0,
      "Pincode": UserSelectedAddress && UserSelectedAddress.Pincode,//newAddress.Pincode || 0,
      "AppointmentType": newAddress.AppointmentType || 0,
      "AssignmentId": newAddress.AssignmentId,
      "ZoneId": newAddress.ZoneId || 0,
      "AppointmentDateTime": newAddress.AppointmentDateTime && newAddress.AppointmentDateTime.replace("T", " "),//"2023-10-04 18:00:00.000",// _appointmentDateTime,
      "SlotId": newAddress.SlotId,//newAddress.AppointmentTimeSlot.SlotId,
      "Address": HouseNo,
      "Address1": (newAddress.Address1 && newAddress.Address1.trim()),
      "Landmark": UserSelectedAddress && UserSelectedAddress.formatted_address,//UserSelectedAddress || "",
      "NearBy": (newAddress.NearBy && newAddress.NearBy.trim()) || "",
      "Comments": newAddress.Comments || "",
      "PlanList": newAddress.PlanList || [],
      "subStatusId": newAddress.subStatusId,//IsSrcCustomerWhatsapp ? 2005 : undefined,
      "IncomeId": newAddress.IncomeId,
      "IncomeDocsId": newAddress.IncomeDocsId,
      "EducationId": newAddress.EducationId,
      "Source": CustomerSource,
      "place_id": UserSelectedAddress && UserSelectedAddress.place_id,//newAddress.place_id,
      "Gender": bypassGenderInput(productId) ? 0 : newAddress.Gender,
      "EncryptedLeadId": window.localStorage.getItem('EncryptedLeadId'),
      "EncryptedCustomerId": window.localStorage.getItem('EncryptedCustomerId'),
      "CancelReasonId": 0,
      "IsDropLocationConfirm": 1

    };
    if ([7, 1000].indexOf(productId) > -1) {
      reqData = { ...reqData, productId: productId }
    }

    setIsShowSaveBtn(false);
    // setBookedAppointmentId(null);
    SetAppointmentDataService(reqData).then((response) => {
      if (response != null) {

        if (response && parseInt(response.StatusCode) === 200) {
          setIsShowSaveBtn(true);
          GetAppointmentData(window.localStorage.getItem('EncryptedCustomerId'), window.localStorage.getItem('EncryptedLeadId'));

          window.parent.postMessage({ "action": "SetAppointmentBooked" }, '*');
          window.parent.postMessage({ "action": response.Message }, '*');
          enqueueSnackbar("Appointment Details Saved Successfully", { variant: 'Success', autoHideDuration: 1500, });
          ConfirmAppointment();

          if (IsSrcCustomerWhatsapp === true) {

            if (window.localStorage.getItem('CustomerWhatsappURL')) {
              setTimeout(() => {
                window.open(window.localStorage.getItem('CustomerWhatsappURL'), "_self")
              }, 1500);
            }

          }

        }
        else {
          setIsShowSaveBtn(true);
          enqueueSnackbar(response.Message, { variant: 'error', autoHideDuration: 3000, });
        }
      }
      else {
        setIsShowSaveBtn(true);
        enqueueSnackbar("Something went wrong, Please Refresh page and try again", { variant: 'error', autoHideDuration: 3000, });
      }
    }).catch((err) => {
      setIsShowSaveBtn(true);
      enqueueSnackbar("Something went wrong, please resubmit after few second", { variant: 'error', autoHideDuration: 3000, });

    });

  };

  const SendLocation = () => {
    const isValid = validateAppointmentData();

    if (!isValid) {
      return;
    }
    let reqData =
    {
      "LeadId": 0,
      "EncryptedLeadId": window.localStorage.getItem('EncryptedLeadId'),
      "EncryptedCustomerId": window.localStorage.getItem('EncryptedCustomerId'),
      "CityId": NewAddress.OfflineCityId,
      "City": NewAddress.OfflineCity,
      "CountryId": NewAddress.CountryId,
      "Pincode": UserSelectedAddress.Pincode,
      "PlaceId": UserSelectedAddress.place_id,
      "LandMark": UserSelectedAddress.formatted_address,
      "Address": HouseNo,
      "Source": CustomerSource,
      "ResponseSource":CustomerSource,
      "CreatedBy": 4020
    }
    AskCustomerLocation(reqData).then((response) => {
      enqueueSnackbar("Appointment Details Saved Successfully", { variant: 'Success', autoHideDuration: 1500, });
      console.log("88888", response)
    }).catch((error) => {
      console.log("88888", error)
    })
  }
  const handleChange = (event) => {
    const regx = /^[0-9\b]+$/;
    let val = event.target.value;
    switch (event.target.name) {
      case "HouseNo":
        setHouseNo(val)
        break;

      default: break;
    }
  }


  const GetAppointmentData = (CustomerId, parentLeadId) => {
    // setGetAppointmentDataAPIStatus(API_STATUS.LOADING);
    GetAppointmentDataService(CustomerId, parentLeadId).then(function (response) {
      // setGetAppointmentDataAPIStatus(API_STATUS.SUCCESS);
      if (response && response.CustomerId > 0) {
        setAppointmentDateTime(response.AppointmentDateTime);
        setStatusId(response.StatusId);

        setNewAddress(prevState => ({ ...prevState, ...response, place_id: response.location && response.location.place_id }));
        // setNewAddress(prevState => ({ ...prevState, ...response, OfflineCity: SelectedOfflineCity, place_id: response.location && response.location.place_id }));
      }
    }).catch((err) => {
      console.log(err)
    
    });
  }
  const fn_RescheduledAppointment = () => {
    if (IsCustLocationConfirmed) {
      OpenFOSPanel()
    }
    else {
      enqueueSnackbar("Confirm your location to proceed.", { variant: 'error', autoHideDuration: 3000, });
    }
  }


  return (
    <div>
      {/* {open && <ModalDialogue />} */}
      {IsNewTrigger && !IsSomethingwentwrong && AppointmentMessage === "" ? (<div className="appointment-reschedule">
        <div className="header">
          <span>
            <a href="https://www.policybazaar.com"><img src={CONFIG.PUBLIC_URL + "/images/rescheduled/pb-logo.svg"} alt="PB" /></a>
           
          </span>
        </div>

        <div className="DropLocation-inner_box">
          <div className="details-box DropLocation-details-box ">
            <Grid container spacing={2}>
              <Grid item md={8} xs={12} className="text-center">
                <div className="desktopView">
                  <h1>Hi, </h1>
                  <p>Please help us locate you better 
                  (provide your location or confirm if already provided)</p>
                </div>
                <div className="mobileview">
                  {/* <div className="heading"> <span>LOCATION DETAILS</span>
                </div> */}
                  <h1>Hi, </h1>
                  <p>Please help us locate you better 
                (provide your location or confirm if already provided)</p>
                </div>
                {/* <img src={CONFIG.PUBLIC_URL + "/images/rescheduled/map.jpg"} className="mapImage" /> */}
                {
                  isLoaded && AppointmentPosition && <AppointmentPositionMap
                    UserSelectedPosition={UserSelectedPosition ? UserSelectedPosition : AppointmentPosition}
                    setUserSelectedPosition={setUserSelectedPosition}
                    Map={Map}
                    setMap={setMap}
                    isLoaded={isLoaded}
                    NewAddress={NewAddress}

                  ></AppointmentPositionMap>
                }

              </Grid>
              <Grid item md={4} xs={12} >
                <div className="grayBg">
                  {AppointmentCity && <p className="CityName">Your City : <b>{AppointmentCity}</b></p>}
                  <div className="SearchLocation">
                    {isLoaded && <SearchMap
                      UserSelectedPosition={UserSelectedPosition}
                      setUserSelectedPosition={setUserSelectedPosition}
                      isLoaded={isLoaded}
                    >
                    </SearchMap>
                    }

                    {/* <img src={CONFIG.PUBLIC_URL + "/images/rescheduled/search-interface.svg"} /> */}
                    <Button className="DetectLocationBtn" color="secondary" variant="contained" onClick={() => { DetectMyLocation() }}> <img src={CONFIG.PUBLIC_URL + "/images/rescheduled/gps.svg"} /> Detect My Location</Button>
                    <div>

                      <TextInput
                        name="HouseNo"
                        label='House/Flat No, Building No, Block/Society'
                        handleChange={handleChange}
                        value={HouseNo}
                        sm={12} md={12} xs={12}
                 
                      />

                    </div>

                    {IsAppointment ?
                      <Button className="sendLocationBtn" color="secondary" variant="contained" onClick={() => { ConfirmLocation() }}>Confirm Location</Button>
                      : <Button className="sendLocationBtn" color="secondary" variant="contained" onClick={() => { SendLocation() }}>Send Location</Button>
                    }

                  </div>
                </div>
              </Grid>
            </Grid>
          </div>
        </div>

        <div className="DropLocation-inner_box">
          {!Loading && IsSomethingwentwrong && <h4 className="details-boxnoappointment">Something went wrong. Please try again later</h4>}
          {!Loading && !IsAppointment && !IsCustomerLocationData && !IsSomethingwentwrong && <h4 className="details-boxnoappointment">This appointment has either been Cancelled or Completed please call on <a href="tel:1800-208-8787">1800-208-8787</a> to learn more</h4>}
          {!Loading && AppointmentMessage !== "" && !IsSomethingwentwrong && <h4 className="details-boxnoappointment">{AppointmentMessage}</h4>}

          {IsAppointment && !IsSomethingwentwrong && AppointmentMessage === "" &&
            <div className="details-box DropLocation-details-box">
              {/* {(subStatusId === 2088) && <span className="confrimedMsg">Confirmed</span>} */}
              <h2>Your Appointment Details</h2>
              <Grid container spacing={2}>
                <Grid item md={8} xs={12}>
                  <ul>
                    <li>
                      <img src={CONFIG.PUBLIC_URL + "/images/rescheduled/icon1.svg"} alt="clock" />
                      <span>{person.AppointmentDateTime && dayjs(person.AppointmentDateTime).format("DD-MMM-YYYY HH:mm A")} </span>
                    </li>
                    <li>
                      <img src={CONFIG.PUBLIC_URL + "/images/rescheduled/icon2.svg"} alt="home" />
                      <span>{Address}</span>

                    </li>
                  
                  </ul>
                </Grid>
                <Grid item md={4} xs={12}>
                  <div className="button_box DropLocationButton" id="myButton">
                    {!Loading && IsAppointment && !IsSomethingwentwrong && AppointmentMessage === "" && <button onClick={() => { fn_RescheduledAppointment() }}>Reschedule</button>}
                  </div>
                </Grid>
              </Grid>


            </div>
          }

        </div>
      </div>) :
        (<div >
          {Loading && <LinearProgress variant="indeterminate" color="secondary" />}
          <div className="appointment-reschedule">
            <div className="header">
              <span>
                <a href="https://www.policybazaar.com"><img src={CONFIG.PUBLIC_URL + "/images/rescheduled/pb-logo.svg"} className="desktoplogo" alt="PB" /></a>
                <a href="https://www.policybazaar.com"><img src={CONFIG.PUBLIC_URL + "/images/rescheduled/pb_logo_mobile.svg"} className="mobilelogo" alt="PB" />
                  <em>My Appointment</em>
                </a>
              </span>
            </div>
            <div className="inner_box">
              <div className="main-view">
                <div className="main-img"><img src={CONFIG.PUBLIC_URL + "/images/rescheduled/main.png"} alt="banner" /></div>
                {!Loading && IsSomethingwentwrong && <h4 className="details-boxnoappointment">Something went wrong. Please try again later</h4>}
                {!Loading && !IsAppointment && !IsSomethingwentwrong && <h4 className="details-boxnoappointment">This appointment has either been Cancelled or Completed please call on <a href="tel:1800-208-8787">1800-208-8787</a> to learn more</h4>}
                {!Loading && AppointmentMessage !== "" && !IsSomethingwentwrong && <h4 className="details-boxnoappointment">{AppointmentMessage}</h4>}
                {IsAppointment && !IsSomethingwentwrong && AppointmentMessage === "" &&
                  <div className="details-box">
                    {(subStatusId === 2088) && <span className="confrimedMsg">Confirmed</span>}
                    <h1>Hi</h1>
                    <p>Your {IsUpcoming ? "Upcoming " : ""}{person.ProductName} Insurance Appointment details</p>

                    <ul>
                      <li>
                        <img src={CONFIG.PUBLIC_URL + "/images/rescheduled/icon1.svg"} alt="clock" />
                        <span>{person.AppointmentDateTime && dayjs(person.AppointmentDateTime).format("DD-MMM-YYYY HH:mm A")} </span>
                      </li>
                      <li>
                        <img src={CONFIG.PUBLIC_URL + "/images/rescheduled/icon2.svg"} alt="home" />
                        <span>{Address}</span>

                      </li>
                      <li>
                        <RoomIcon />
                        <span>{person.Landmark}</span>
                      </li>
                    </ul>
                  </div>
                }
                <div className="button_box" id="myButton">
                  {!Loading && IsAppointment && !IsSomethingwentwrong && AppointmentMessage === "" && <button onClick={() => { OpenFOSPanel() }}>Reschedule</button>}
                  {!Loading && IsUpcoming && IsAppointment && !IsSomethingwentwrong && showConfirmbtn && AppointmentMessage === "" && <button className="submitbtn" onClick={() => { ConfirmAppointment() }} >confirm</button>}
                </div>
              </div>


            </div>

          </div>

        </div>)
      }
    </div>

  );
};

export default RescheduledAppointment;
