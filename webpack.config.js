const path = require('path');
const hwp = require('html-webpack-plugin');
const webpack = require('webpack');
require('dotenv').config();
require('dotenv').config({path: '.env.deploy'});
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');

const PUBLIC_URL = process.env.BUILD_ENV.toLowerCase() === "merge" ? '/merge/newsv' : '/pgv/newsv';

module.exports = {
  mode: 'production',
  entry: path.join(__dirname, '/src/index.js'),
  output: {
    filename: 'js/build.[name].[contenthash].js',
    path: path.join(__dirname, '/dist'),
    publicPath: PUBLIC_URL + "/"
  },
  optimization: {
    // We no not want to minimize our code.
    minimize: true,
    splitChunks: {
      // include all types of chunks
      chunks: 'all'
    }
  },
  devtool: 'source-map',
  module: {
    rules: [
      {
        test: /\.(svg)$/i,
        type: "asset/resource",
        generator: {
          filename: "images/[name][ext]"
        },
      },
      {
        exclude: /node_modules/,
        test: /\.js$/,
        loader: 'babel-loader'
      },
      {
        test: /\.(sa|sc|c)ss$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
          },
          'css-loader',
          'postcss-loader',
          {
            loader: 'sass-loader'
          },
        ],
      },
      {
        test: /.(ttf|otf|eot|woff(2)?)(\?[a-z0-9]+)?$/,
        use: [{
          loader: 'file-loader',
          options: {
            name: '[name].[ext]',
            outputPath: 'fonts/'
          }
        }]
      }]
  },

  plugins: [
    new webpack.DefinePlugin({
      "API_BASE_URL": JSON.stringify(process.env.API_BASE_URL),
      "TOKEN_KEY": JSON.stringify(process.env.TOKEN_KEY),
      "COOKIE_LOGIN": JSON.stringify(process.env.COOKIE_LOGIN),
      "PUBLIC_URL": JSON.stringify(PUBLIC_URL),
      "BUILD_ENV": JSON.stringify(process.env.DEPLOY_ENV) || JSON.stringify(process.env.BUILD_ENV),
      "VERSION": JSON.stringify(new Date().getTime())
    }),
    new CleanWebpackPlugin(),
    new MiniCssExtractPlugin({
      // Options similar to the same options in webpackOptions.output
      // both options are optional
      // path: path.join(__dirname, '/dist'),

      filename: 'css/[name].[contenthash].css',
      chunkFilename: 'css/build.[name].[contenthash].css',
    }),
    new hwp({ template: path.join(__dirname, '/public/index.html') }),
    new CopyWebpackPlugin({
      patterns: [
        { from: 'public/images', to: 'images' },
        { from: 'public/libs', to: 'libs' },
        { from: 'public/sound', to: 'sound' },
        { from: 'public/docs', to: 'docs' }
      ]
    })
  ]
}