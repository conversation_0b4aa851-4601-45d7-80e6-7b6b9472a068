import React, { useEffect } from "react";
// import RightBlock from "./RightBlock";
import { Checkbox, FormControlLabel, Grid, Radio, TextField } from "@mui/material";
import { connect, useSelector } from "react-redux";
import { setRefreshLead, setRefreshCustomerId } from "../../../store/actions/SalesView/SalesView";
import rootScopeService from "../../../services/rootScopeService";
import { useState } from "react";
import { SelectDropdown, TextInput } from "../../../components";
import { CONFIG, SV_CONFIG } from "../../../appconfig";
import { GendersMaster, CreateLeadParamsMaster } from "../CreateLeadHelpers/CreateLeadMasters";
import { CreateReferralLeadService } from "../../../layouts/SV/components/Sidebar/helper/sidebarHelper";
import './referralLead.scss'
import { useSnackbar } from "notistack";
import { Autocomplete } from "@mui/material";
import masterService from "../../../services/masterService";
import LoaderComponent from "../../../components/Loader";
import CreateReferralMotorHealth, { LeadSourceOptions } from "./CreateReferralMotorHealth";
import CreateAppointmentPopup from "../../SV/RightBar/Modals/CreateAppointmentPopup";
import ErrorBoundary from "../../../hoc/ErrorBoundary/ErrorBoundary";
import CreateReferralSme, { ValidateSmeData, SetSmeCreateLeadParams, UpdateExitPointUrl } from "./CreateReferralSme";
import User from "../../../services/user.service";

function CreateLead(props) {

    const [CreateLeadParams, setCreateLeadParams] = useState(CreateLeadParamsMaster);
    const [showGender] = useState([7, 1000].includes(rootScopeService.getProductId()) ? true : false)
    const [showAge] = useState([7, 1000].includes(rootScopeService.getProductId()) ? true : false)
    const [parentLeadId, setparentLeadId] = useState(0);
    const [CustomerId, setCustomerId] = useState(0);
    const [productId, setProductId] = useState(0);
    const [NewLeadId, setNewLeadId] = useState(0);
    const [IsRenewal, setIsRenewal] = useState(0);
    const [IsActive, setIsActive] = useState(0);
    const [LeadSource, setLeadSource] = useState('');
    const [userId, setUserId] = useState(0);
    const [inputValue, setInputValue] = useState('');
    const [Remarks, setRemarks] = useState('');
    let [InputCountry, setInputCountry] = useState("");
    const [Countries, setCountries] = useState([""]);
    const [SubProducts, setSubProducts] = useState([""]);
    const [isLoading, setIsLoading] = useState(false);
    const [isSuccess, setIsSuccess] = useState(false);
    const [currentPopup, setCurrentPopup] = useState(null);
    const [SmeLeadProcessed, setSmeLeadProcessed] = useState(0);
    const { enqueueSnackbar } = useSnackbar();
    const [UserGroups, setUserGroups] = useState([]);
    const [UserList, setUserList] = useState([]);
    const [FilteredUserList, setFilteredUserList] = useState([]);
    const [CountryValidationsList, setCountryValidationsList] = useState([""]);
    const [LeadAssignedUser, setLeadAssignedUser] = useState(0);
    const [IsRecentLead, setIsRecentLead] = useState(false);

    useEffect(() => {
        const leadData = getDetailsFromURL();
        if (!leadData) return;
        initStates(leadData);
        GetCountryList();
        GetUserGroups();
        GetUserList();
    }, []);

    useEffect(() => {
        if (productId && productId == 115) {
            GetSubCategories(productId);
        }
        if (productId) {
            GetCountryValidations(productId);
        }
    }, [productId]);

    const GetSubCategories = (productId) => {
        masterService.getSubProductByProductID(productId).then(res => {
            res = res || [];
            setSubProducts(res);
        });
    }

    const GetCountryValidations = (productId) => {
        masterService.getCountriesValidations(productId).then(res => {
            res = res || [];
            setCountryValidationsList(res);
        });
    }

    const GetCountryList = (DefaultCountry = true) => {
        masterService.GetCountryList().then((result) => {
            if (result && Array.isArray(result)) {
                setCountries(result);
                if (DefaultCountry) {
                    setInputCountry(
                        result.filter((x) => x.CountryID === 392)[0]
                    );
                }
            }
        });
    };

    const initStates = (leadData) => {
        const { _parentLeadId, _ProductId, _userId, _CustomerId, _LeadSource, _LeadAssignedUser, _IsRecentLead, _IsRenewal, _IsActive } = leadData;
        setCustomerId(_CustomerId);
        setProductId(_ProductId)
        setparentLeadId(_parentLeadId);
        setUserId(_userId);
        setLeadSource(_LeadSource);
        setLeadAssignedUser(_LeadAssignedUser);
        setIsRenewal(_IsRenewal);
        setIsActive(_IsActive);
        setIsRecentLead(_IsRecentLead);
    }

    const getDetailsFromURL = () => {
        let routeParams = window.location.pathname.split('/');
        if (!routeParams) {
            return null;
        }
        let _userId, _parentLeadId, _ProductId, _CustomerId, _LeadSource, _LeadAssignedUser, _IsRecentLead, _IsRenewal, _IsActive;
        let reqIndex = CONFIG.PUBLIC_URL ? 4 : 2;
        _parentLeadId = parseInt(atob(routeParams[reqIndex]).split('/')[2]);
        _ProductId = parseInt(atob(routeParams[reqIndex]).split('/')[1]);
        _CustomerId = parseInt(atob(routeParams[reqIndex]).split('/')[0]);
        _userId = parseInt(atob(routeParams[reqIndex]).split('/')[3]);
        _LeadSource = atob(routeParams[reqIndex]).split('/')[4];
        _LeadAssignedUser = atob(routeParams[reqIndex]).split('/')[5];
        _IsRecentLead = atob(routeParams[reqIndex]).split('/')[6];

        if (_ProductId == 2 || _ProductId == 130 || _ProductId == 106) {
            _IsRenewal = parseInt(atob(routeParams[reqIndex]).split('/')[7]);
            _IsActive = parseInt(atob(routeParams[reqIndex]).split('/')[8]);
        }
        return {
            _parentLeadId,
            _ProductId,
            _userId,
            _CustomerId,
            _LeadSource,
            _LeadAssignedUser,
            _IsRecentLead,
            _IsRenewal,
            _IsActive
        }
    }

    const CreateReferralLead = () => {
        if (rootScopeService.getProductId() == 131) {
            CreateSmeReferralLead();
        }
        else {
            const isValid = validateCreateReferralLead();
            if (isValid) {
                try {
                    setIsLoading(true)
                    let APIParams = setParametersForCreateLeadAPI();
                    createLeadApi(APIParams)

                } catch (e) {
                    setIsLoading(false)
                    enqueueSnackbar('Some error occurred', { variant: 'error', autoHideDuration: 3000 });
                }
            }
        }
    }

    const CreateSmeReferralLead = () => {
        let message = ValidateSmeData(CreateLeadParams);
        if (message) {
            enqueueSnackbar(message, { variant: 'error', autoHideDuration: 4000, style: { whiteSpace: 'pre-line' } });
        }
        else {
            try {
                setIsLoading(true);
                setIsSuccess(true);
                let params = SetSmeCreateLeadParams(CreateLeadParams, CustomerId);
                createLeadApi(params, true);
            }
            catch (e) {
                enqueueSnackbar('Some error occurred', { variant: 'error', autoHideDuration: 3000 });
                setIsLoading(false);
                setIsSuccess(false);
            }
        }
    }

    const setParametersForCreateLeadAPI = () => {
        var APIParams = {}
        APIParams.Name = CreateLeadParams.Name.trim()
        APIParams.ProductId = productId
        if (LeadSource && LeadSource.toLowerCase() == 'fos_referral') {
            APIParams.LeadSource = 'Referral'
            APIParams.Source = 'FOS_referral'
            APIParams.UtmSource = 'FOS_referral'
        }
        else {
            APIParams.LeadSource = 'Referral'
            APIParams.UtmSource = 'Self Referral'
        }
        APIParams.ReferralLead = parentLeadId

        if ([7, 1000].includes(productId)) {
            APIParams.Age = CreateLeadParams.Age
            APIParams.Gender = CreateLeadParams.Gender
        }
        if ([115].includes(productId)) {
            APIParams.SubProductId = CreateLeadParams.SubProductId
        }
        if ([117, 2, 106, 130].includes(productId)) {
            APIParams.CityID = CreateLeadParams.CityId
        }
        if (CreateLeadParams.IsExistMobileNo) {
            APIParams.MobileNo = 0
            APIParams.CountryId = 0
            APIParams.CustomerId = CustomerId
        } else {
            APIParams.MobileNo = CreateLeadParams.MobileNo
            APIParams.CountryId = InputCountry.CountryID
            APIParams.CustomerId = 0
        }
        if ([2, 106, 130].includes(productId) && IsRenewal == 1 && [13].includes(User?.RoleId)) {
            APIParams.UtmMedium = 'Health_Renewal'
            APIParams.Remarks = Remarks
        }
        if (CreateLeadParams.UtmSource && (CreateLeadParams.UtmSource).toLowerCase() === "healthoncorp") {
            APIParams.LeadSource = LeadSourceOptions.filter((e) => e.Id == CreateLeadParams.LeadSourceId)[0].Name;
            APIParams.UtmSource = CreateLeadParams.UtmSource

        }
        APIParams.AssignedGroupId = CreateLeadParams.AssignedGroupId ? CreateLeadParams.AssignedGroupId : 0;
        APIParams.AssignedUser = CreateLeadParams.AssignedUser ? CreateLeadParams.AssignedUser : 0;
        if ([7, 1000, 2, 106, 130, 115, 117].includes(productId)) {
            APIParams.MaxStatusID = 4;
        }

        return APIParams;
    }

    const createLeadApi = (APIParams, isSme = false) => {
        CreateReferralLeadService(APIParams).then((result) => {
            setIsLoading(false)
            if (result && result.IsLeadCreated) {
                setIsSuccess(true)
                setNewLeadId(result.LeadId)
                if (LeadSource && LeadSource.toLowerCase() == 'fos_referral') {
                    setCurrentPopup('CreateAppointmentPopup');
                }
                else {
                    setCreateLeadParams(CreateLeadParamsMaster);
                    enqueueSnackbar(result.Message, { variant: 'success', autoHideDuration: 3000 });
                }
            } else if (result && result.IsLeadCreated == false) {
                enqueueSnackbar(result.Message, { variant: 'error', autoHideDuration: 3000 });
            } else if (result && result.errors) {
                enqueueSnackbar('Some error occurred', { variant: 'error', autoHideDuration: 3000 });
            }

            if (isSme) {
                UpdateExitPointUrl(result.LeadId);
                setIsLoading(false);
                setIsSuccess(false);
                setSmeLeadProcessed(SmeLeadProcessed + 1);
            }
        }).catch((err) => {
            setIsLoading(false)
            if (isSme) {
                setIsSuccess(false);
            }
            enqueueSnackbar('Some error occurred', { variant: 'error', autoHideDuration: 3000 });
        })
    }

    const validateMobileNumber = (CreateLeadParams, InputCountry) => {
        if(CreateLeadParams.IsExistMobileNo) {
            return true;
        }
        if (!CreateLeadParams.MobileNo) {
            enqueueSnackbar("Mobile number is required.", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return false;
        }
    
        const mobileNoStr = CreateLeadParams.MobileNo.toString(); // Convert to string for validation
        const selectedCountry = CountryValidationsList.find(country => country.countryId === InputCountry?.CountryID);
    
        if (selectedCountry && selectedCountry.validation) {
            const { startsWith, length } = selectedCountry.validation;
    
            // Check if mobile number starts with allowed digits
            if (startsWith && !startsWith.some(prefix => mobileNoStr.startsWith(prefix.toString()))) {
                enqueueSnackbar(`Mobile number should start with ${startsWith.join(", ")}.`, {
                    variant: 'error',
                    autoHideDuration: 3000,
                });
                return false;
            }
    
            // Check if mobile number length falls within the allowed range
            if (length && !(parseInt(length.min) === parseInt(length.max)) && (mobileNoStr.length < parseInt(length.min) || mobileNoStr.length > parseInt(length.max))) {
                enqueueSnackbar(`Mobile number should be between ${length.min} to ${length.max} digits.`, {
                    variant: 'error',
                    autoHideDuration: 3000,
                });
                return false;
            }else if(length && (parseInt(length.min) === parseInt(length.max)) && (mobileNoStr.length < parseInt(length.min) || mobileNoStr.length > parseInt(length.max))){
                enqueueSnackbar(`Mobile number should be of ${length.min} digits numeric.`, {
                    variant: 'error',
                    autoHideDuration: 3000,
                });
                return false;
            }
        } else {
            // Default validation for countries without specific rules (NRI case)
            const minLength = productId && parseInt(productId) === 2 ? 8 : 5;
            const maxLength = productId && parseInt(productId) === 2 ? 11 : 13;
            
            if (mobileNoStr.length < minLength || mobileNoStr.length > maxLength) {
                enqueueSnackbar(`Mobile number should be between ${minLength} to ${maxLength} digits.`, {
                    variant: 'error',
                    autoHideDuration: 3000,
                });
                return false;
            }
        }
    
        return true; // If all validations pass
    };
    

    const validateCreateReferralLead = () => {
        const isValid = validateMobileNumber(CreateLeadParams, InputCountry);
       
        if ((CreateLeadParams.Gender == "" || CreateLeadParams.Gender == undefined) && [1000, 7].includes(productId)) {
            enqueueSnackbar("Please select Gender", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return false;
        }
        else if (CreateLeadParams.Name == "" || CreateLeadParams.Name == undefined) {
            enqueueSnackbar("Please enter Name", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return false;
        }
        else if ((CreateLeadParams.CityId == "" || CreateLeadParams.CityId == undefined) && [2, 117, 130, 106].includes(productId)) {
            enqueueSnackbar("Please enter City", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return false;
        }
        else if ((CreateLeadParams.Age == "" || CreateLeadParams.Age == undefined) && [1000, 7].includes(productId)) {
            enqueueSnackbar("Please enter Age", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return false;
        }
        else if ((CreateLeadParams.Age <= 0 || CreateLeadParams.Age > 100) && [1000, 7].includes(productId)) {
            enqueueSnackbar("Age can not be less than 1 and greater than 100", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return false;
        }
        // else if ((CreateLeadParams.MobileNo == "" || CreateLeadParams.MobileNo == undefined) && !(CreateLeadParams.IsExistMobileNo)) {
        //     enqueueSnackbar("Please enter Mobile Number", {
        //         variant: 'error',
        //         autoHideDuration: 3000,
        //     });
        //     return false;
        // }
        else if (!(InputCountry) && !(CreateLeadParams.IsExistMobileNo)) {
            enqueueSnackbar("Please select Country Code", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return false;
        }
        // else if (!(CreateLeadParams.IsExistMobileNo) && (InputCountry && InputCountry.CountryID == 392) && !((CreateLeadParams.MobileNo.toString()).length == 10)) {
        //     enqueueSnackbar("MobileNo should be 10 digit numeric.", {
        //         variant: 'error',
        //         autoHideDuration: 3000,
        //     });
        //     return false;
        // }
        // else if (!(CreateLeadParams.IsExistMobileNo) && !(InputCountry && InputCountry.CountryID == 392) && !((CreateLeadParams.MobileNo.toString()).length >= 5 && (CreateLeadParams.MobileNo.toString()).length <= 13)) {
        //     enqueueSnackbar("MobileNo should be 5 to 13 digit numeric.", {
        //         variant: 'error',
        //         autoHideDuration: 3000,
        //     });
        //     return false;
        // }
        else if(!isValid) {
            return false;
        }
        else if ((CreateLeadParams.SubProductId == "" || CreateLeadParams.SubProductId == undefined) && [115].includes(productId)) {
            enqueueSnackbar("Please select Category", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return false;
        }
        else if ((CreateLeadParams.AssignedGroupId == "" || CreateLeadParams.AssignedGroupId == undefined) && [12, 19, 2, 11].includes(User?.RoleId)) {
            enqueueSnackbar("Please select Group", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return false;
        }
        else if ((CreateLeadParams.AssignedUser == "" || CreateLeadParams.AssignedUser == undefined) && [12, 19, 2, 11].includes(User?.RoleId)) {
            enqueueSnackbar("Please select Assign To User", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return false;
        }
        else if (IsRenewal == 1 && IsActive == 1 && CreateLeadParams.IsExistMobileNo && [13].includes(User?.RoleId)) {
            enqueueSnackbar("Unable to create Lead! Please book/ reject the entire set first, else use alternative number to create referral.", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return false;
        }
        else if (IsRenewal == 1 && Remarks == "" && [13].includes(User?.RoleId)) {
            enqueueSnackbar("Add Remarks/Context for sales agent", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return false;
        }
        return true;
    }

    const filterUsersByGroup = (users, groupId) => {
        if (users?.UserListResult?.Data && Array.isArray(users.UserListResult.Data)) {
            return users.UserListResult.Data.filter((user) =>
                user.Products.some((product) =>
                    product.Groups.some((group) => group.GroupId === groupId)
                )
            )
        } else {
            return [];
        }
    };

    const GetUserGroups = () => {
        masterService.GetUserGroups(rootScopeService.getProductId(), 'CreateReferralLead').then((response) => {
            if (response && Array.isArray(response)) {
                let sortedGroups = getSortedList(response[0],'GroupName');
                setUserGroups(sortedGroups);
            }
        });
    }

    const GetUserList = () => {
        masterService.GetUserList(rootScopeService.getProductId(), 13).then((response) => {
            setUserList(response);
        });
    }

    const handleChange = (event) => {
        const regx = /^[0-9\b]+$/;
        const regexalphabets = /^[A-Za-z\s]*$/;
        let val = event.target.value;
        let e_name = event.target.name;

        if (["LeadSourceId", "SmeSelectedLead", "ContactPersonName", "EmailId", "CompanyName", "UtmSource", "Occupation"].includes(e_name)) {
            setCreateLeadParams({ ...CreateLeadParams, [e_name]: val })
        }

        switch (e_name) {
            case 'Name':
                if (event.target.value !== "" && !regexalphabets.test(event.target.value)) {
                    enqueueSnackbar("Only alphabets allowed", {
                        variant: "error",
                        autoHideDuration: 3000,
                    });
                } else if (!val.replace(/\s/g, '').length) {
                    setCreateLeadParams({ ...CreateLeadParams, Name: '' })
                }
                else {
                    setCreateLeadParams({ ...CreateLeadParams, Name: val })
                }
                break;
            case 'Age':
                if (event.target.value !== "" && !regx.test(event.target.value)) {
                    enqueueSnackbar("Only Numbers allowed", {
                        variant: "error",
                        autoHideDuration: 3000,
                    });
                } else {
                    setCreateLeadParams({ ...CreateLeadParams, Age: val })
                }
                break;
            case 'MobileNo':
                if (event.target.value !== "" && !regx.test(event.target.value)) {
                    enqueueSnackbar("Only Numbers allowed", {
                        variant: "error",
                        autoHideDuration: 3000,
                    });
                } else {
                    setCreateLeadParams({ ...CreateLeadParams, MobileNo: (val) ? parseInt(val, 10) : val })
                }
                break;
            case 'Gender':
                if (event.target.value) {
                    setCreateLeadParams({ ...CreateLeadParams, Gender: parseInt(val) })
                }
                break;
            case 'SubProductId':
                setCreateLeadParams({ ...CreateLeadParams, SubProductId: val })
                break;
            case 'IsExistMobileNo':
                val = event.target.checked;
                setCreateLeadParams({ ...CreateLeadParams, IsExistMobileNo: val }, () => {
                    setMobileNoAndCountryCode(val);
                })
                break;
            case 'PostalCode':
                if (val && !regx.test(val)) {
                    enqueueSnackbar("Only Numbers allowed", {
                        variant: "error",
                        autoHideDuration: 3000,
                    });
                }
                else {
                    setCreateLeadParams({ ...CreateLeadParams, PostalCode: val })
                }
                break;
            case "GroupId":
                setCreateLeadParams((prevState) => ({ ...prevState, AssignedGroupId: event.target.value, AssignedUser: '' }));
                let FilteredUserList = filterUsersByGroup(UserList, event.target.value);
                const sortedUsers = getSortedList(FilteredUserList,'UserName');
                setFilteredUserList(sortedUsers);
                break;
            case "AssignedUser":
                setCreateLeadParams((prevState) => ({ ...prevState, AssignedUser: event.target.value }));
                break;
            case "Remarks":
                setRemarks(event.target.value);
                break;
            default:
                break;
        }
    }

    const getSortedList = (Options,key) => {
        return [...Options].sort((a, b) => {
        const nameA = a[key]?.toLowerCase() || '';
        const nameB = b[key]?.toLowerCase() || '';
        return nameA.localeCompare(nameB);
    }); 
    }

    const setMobileNoAndCountryCode = (val) => {
        if (val) {
            setCreateLeadParams({ ...CreateLeadParams, MobileNo: 0 })
            setCreateLeadParams({ ...CreateLeadParams, CountryId: 0 })
        }
    }

    const setMotorParameters = (val) => {
        setCreateLeadParams(CreateLeadParams => ({ ...CreateLeadParams, ...val }));
    }

    const setInputCountryParameters = (val) => {
        setInputCountry(val)
    }

    return (
        <>
            {([7, 1000].includes(productId)) && <div className="CreateTermReferralLead">
                {(User?.RoleId === 12) && <div className="ReferralLeadNotAllowed">
                    <p>Referral leads for Life BU not allowed at Supervisor role</p>
                </div>}
                {(!(User?.RoleId === 13) && String(IsRecentLead).toLowerCase() === "true" && parseInt(LeadAssignedUser) === 0) && (
                    <div className="ReferralLeadNotAllowed">
                        <p>Note: Referral lead cannot be created on unassigned leads</p>
                    </div>
                )}
                <Grid container spacing={2}>
                    {showGender && <Grid item sm={6} md={3} xs={12}>
                        {/* <Grid item {...GenderGridProps}> */}
                        {(GendersMaster.map((Gender) => (
                            <FormControlLabel
                                key={Gender.value}
                                value={Gender.value}
                                control={<Radio className="RadioBtn" />}
                                label={Gender.label}
                                onChange={handleChange}
                                checked={CreateLeadParams.Gender == Gender.value}
                                name="Gender"
                                className="GenderDetails"
                            />
                        )))}
                    </Grid>}
                    {/* } */}

                    <TextInput
                        name="Name"
                        label="Customer Name"
                        handleChange={handleChange}
                        value={CreateLeadParams.Name}
                        sm={12} md={12} xs={12}
                        required={true}
                        maxLength={50}
                    />

                    {showAge && <TextInput
                        name="Age"
                        label="Age"
                        handleChange={handleChange}
                        //inputProps={{ type: 'number'}}
                        value={CreateLeadParams.Age}
                        sm={12} md={12} xs={12}
                        required={true}
                        maxLength="3"
                    />}

                    {!(CreateLeadParams.IsExistMobileNo) &&
                        <>
                            <Grid item sm={4} md={4} xs={5} className="pdRight-0">
                                <Autocomplete
                                    onChange={(event, value) => setInputCountry(value)}
                                    className={"createLeadAutocomplete"}
                                    options={Countries}
                                    value={InputCountry || null}
                                    inputValue={inputValue}
                                    //style={{ width: 300 }}
                                    getOptionLabel={(option) => option.Country || ""}
                                    onInputChange={(event, newValue) => {
                                        // Trim the input value to your desired length
                                        const trimmedValue = (newValue.slice(0, 5)).toUpperCase(); // Trim to 5 characters
                                        setInputValue(trimmedValue);
                                    }}
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            variant="outlined"
                                        />
                                    )}
                                />
                            </Grid>
                            <Grid item sm={8} md={8} xs={7} className="pdLeft-0">
                                <TextInput
                                    name="MobileNo"
                                    maxLength="13"
                                    label="Mobile Number"
                                    value={CreateLeadParams.MobileNo}
                                    handleChange={handleChange}
                                    sm={12} md={12} xs={12}
                                    className="mobileNumber"
                                    required={true}
                                />
                            </Grid>
                        </>
                    }

                    <Grid item sm={12} md={12} xs={12} className="pdtop5">
                        <FormControlLabel className="useExitMobileNo"
                            name="IsExistMobileNo"
                            control={<Checkbox color="primary" />}
                            label="Use existing mobile number"
                            value={CreateLeadParams.IsExistMobileNo}
                            onChange={handleChange}
                        />

                    </Grid>
                    {User && [12,19,2,11].includes(User.RoleId) &&
                    <>
                        <Grid item sm={12} md={12} xs={12} className="pdtop5">
                            <SelectDropdown
                                name="GroupId"
                                label="Groups *"
                                value={CreateLeadParams.AssignedGroupId}
                                handleChange={handleChange}
                                options={UserGroups}
                                labelKeyInOptions="GroupName"
                                valueKeyInOptions="GroupId"
                                maxLength={100}
                                required
                            />
                        </Grid>
                        <SelectDropdown
                            name="AssignedUser"
                            label="Assign To User *"
                            value={CreateLeadParams.AssignedUser}
                            handleChange={handleChange}
                            options={FilteredUserList}
                            labelKeyInOptions="UserName"
                            labelKeyInSecondOpt="EmployeeId"
                            valueKeyInOptions="UserId"
                            sm={12} md={12} xs={12}
                            maxLength={100}
                            required
                        /></>
                        }
                </Grid>
                {!(CreateLeadParams.IsExistMobileNo) && <div className="differentCustomer">
                    <p> <img alt="" src={CONFIG.PUBLIC_URL + "/images/salesview/diffrentCus.svg"} />Creating a referral lead for different customer</p>
                </div>}
                {(CreateLeadParams.IsExistMobileNo) && <div className="SameCustomer">
                    <p> <img alt="" src={CONFIG.PUBLIC_URL + "/images/salesview/sameCus.svg"} />Creating a referral lead for the same customer</p>
                </div>}
                {!(User?.RoleId === 12) && <div className="text-center">
                    <button className={(isSuccess) ? "CreateLeadBtn disableBtn" : "CreateLeadBtn"} disabled={(isSuccess) ? true : false} onClick={() => CreateReferralLead(CreateLeadParams)} >create referral</button>
                    {isLoading ? <LoaderComponent open={true} /> : null}

                </div>}
            </div>}

            {([115].includes(productId)) && <div className="SavingReferralLead">
                {(User?.RoleId === 12) && <div className="ReferralLeadNotAllowed">
                    <p>Referral leads for Life BU not allowed at Supervisor role</p>
                </div>}
                {(!(User?.RoleId === 13) && String(IsRecentLead).toLowerCase() === "true" && parseInt(LeadAssignedUser) === 0) && (
                    <div className="ReferralLeadNotAllowed">
                        <p>Note: Referral lead cannot be created on unassigned leads</p>
                    </div>
                )}
                <Grid container spacing={3}>
                    <Grid item sm={6} md={7} xs={12}>
                        <Grid container spacing={2}>
                            <TextInput
                                name="Name"
                                label="Customer Name"
                                handleChange={handleChange}
                                value={CreateLeadParams.Name}
                                sm={12} md={12} xs={12}
                                required={true}
                                maxLength={50}
                            />
                            {!(CreateLeadParams.IsExistMobileNo) &&
                                <>
                                    <Grid item sm={4} md={4} xs={5} className="pdRight-0">
                                        <Autocomplete
                                            onChange={(event, value) => setInputCountry(value)}
                                            className={"createLeadAutocomplete"}
                                            options={Countries}
                                            value={InputCountry || null}
                                            inputValue={inputValue}
                                            //style={{ width: 300 }}
                                            getOptionLabel={(option) => option.Country || ""}
                                            onInputChange={(event, newValue) => {
                                                // Trim the input value to your desired length
                                                const trimmedValue = (newValue.slice(0, 5)).toUpperCase(); // Trim to 5 characters
                                                setInputValue(trimmedValue);
                                            }}
                                            renderInput={(params) => (
                                                <TextField
                                                    {...params}
                                                    variant="outlined"
                                                />
                                            )}
                                        />
                                    </Grid>
                                    <Grid item sm={8} md={8} xs={7} className="pdLeft-0">
                                        <TextInput
                                            name="MobileNo"
                                            label="Mobile Number"
                                            value={CreateLeadParams.MobileNo}
                                            handleChange={handleChange}
                                            className="savingmobileNumber"
                                            sm={12} md={12} xs={12}
                                            //inputProps={{ type: 'number' }}
                                            required={true}
                                            maxLength="13"
                                        />
                                    </Grid>
                                </>
                            }
                            <Grid item sm={12} md={12} xs={12} className="pdtop5">
                                <FormControlLabel className="useExitMobileNo"
                                    name="IsExistMobileNo"
                                    control={<Checkbox color="primary" />}
                                    label="Use existing mobile number"
                                    value={CreateLeadParams.IsExistMobileNo}
                                    onChange={handleChange}
                                />

                            </Grid>

                            {User && [12,19,2,11].includes(User.RoleId) &&
                            <>
                                <Grid item sm={12} md={12} xs={12} className="pdtop5">
                                    <SelectDropdown
                                        name="GroupId"
                                        label="Groups *"
                                        value={CreateLeadParams.AssignedGroupId}
                                        handleChange={handleChange}
                                        options={UserGroups}
                                        labelKeyInOptions="GroupName"
                                        sm={12} md={12} xs={12}
                                        valueKeyInOptions="GroupId"
                                        maxLength={100}
                                        required
                                    />
                                </Grid>
                                <SelectDropdown
                                    name="AssignedUser"
                                    label="Assign To User *"
                                    value={CreateLeadParams.AssignedUser}
                                    handleChange={handleChange}
                                    options={FilteredUserList}
                                    labelKeyInOptions="UserName"
                                    labelKeyInSecondOpt="EmployeeId"
                                    valueKeyInOptions="UserId"
                                    sm={12} md={12} xs={12}
                                    maxLength={100}
                                    required
                                /></>}
                            <Grid item sm={12} md={12} xs={12} className="pdtop5">
                                {!(CreateLeadParams.IsExistMobileNo) && <div className="differentCustomer">
                                    <p> <img alt="" src={CONFIG.PUBLIC_URL + "/images/salesview/diffrentCus.svg"} />Creating a referral lead for different customer</p>
                                </div>}
                                {(CreateLeadParams.IsExistMobileNo) && <div className="SameCustomer">
                                    <p> <img alt="" src={CONFIG.PUBLIC_URL + "/images/salesview/sameCus.svg"} />Creating a referral lead for the same customer</p>
                                </div>}
                            </Grid>
                        </Grid>

                    </Grid>
                    <Grid item sm={6} md={5} xs={12}>
                        <div className="investmentCategories">
                            <h3>Categories</h3>
                            {(SubProducts.map((subProduct) => (
                                <FormControlLabel
                                    key={subProduct.ID}
                                    value={subProduct.ID}
                                    control={<Radio className="RadioBtn" />}
                                    label={subProduct.Name}
                                    onChange={handleChange}
                                    checked={CreateLeadParams.SubProductId == subProduct.ID}
                                    name="SubProductId"
                                    className="GenderDetails"
                                />
                            )))}
                        </div>
                    </Grid>
                    {!(User?.RoleId === 12)  && <div className="text-center btn">
                        <button className={(isSuccess) ? "CreateLeadBtn disableBtn" : "CreateLeadBtn"} disabled={(isSuccess) ? true : false} onClick={() => CreateReferralLead(CreateLeadParams)}>create referral
                        </button>
                        {isLoading ? <LoaderComponent open={true} /> : null}
                    </div>}
                </Grid>
            </div>}
            {([117, 2, 106, 130].includes(productId)) && <div className="SavingReferralLead">
                {(!(User?.RoleId === 13) && String(IsRecentLead).toLowerCase() === "true" && parseInt(LeadAssignedUser) === 0) && (
                    <div className="ReferralLeadNotAllowed">
                        <p>Note: Referral lead cannot be created on unassigned leads</p>
                    </div>
                )}

                <Grid container spacing={3}>
                    <CreateReferralMotorHealth Countries={Countries} FilteredUserList={FilteredUserList} UserGroups={UserGroups}
                        CreateLeadParams={CreateLeadParams} setMotorParams={setMotorParameters}
                        setInputCountryParams={setInputCountryParameters}
                        setCreateLeadParams={setCreateLeadParams}
                        handleChange={handleChange} />
                    {[2, 106, 130].includes(productId) && IsRenewal == 1 && [13].includes(User?.RoleId) &&
                        <>
                            <Grid item sm={12} md={12} xs={12}>
                                <TextInput
                                    name="Remarks"
                                    label="Remarks/Context for sales agent"
                                    maxLength={15}
                                    handleChange={handleChange}
                                    value={Remarks}
                                    sm={12} md={12} xs={12}
                                />
                            </Grid>
                        </>
                    }

                    <div className="text-center btn">
                        <button className={(isSuccess) ? "CreateLeadBtn disableBtn" : "CreateLeadBtn"} disabled={(isSuccess) ? true : false} onClick={() => CreateReferralLead(CreateLeadParams)}>create referral
                        </button>
                        {isLoading ? <LoaderComponent open={true} /> : null}
                    </div>
                </Grid>
            </div>
            }
            {([131].includes(productId)) &&
                <div className="SavingReferralLead">
                    <Grid container spacing={3}>
                        <CreateReferralSme
                            Countries={Countries}
                            CreateLeadParams={CreateLeadParams}
                            setCreateLeadParams={setCreateLeadParams}
                            handleChange={handleChange}
                            SmeLeadProcessed={SmeLeadProcessed}
                            parentLeadId={parentLeadId}
                        />
                        <div className="text-center btn">
                            <button
                                className={isSuccess ? "CreateLeadBtn disableBtn" : "CreateLeadBtn"}
                                disabled={isSuccess}
                                onClick={() => CreateReferralLead()}>
                                create referral
                            </button>
                            {isLoading ? <LoaderComponent open={true} /> : null}
                        </div>
                    </Grid>
                </div>
            }
            <ErrorBoundary name="CreateAppointmentPopup">
                <CreateAppointmentPopup open={currentPopup === "CreateAppointmentPopup"} CreateLeadParams={CreateLeadParams}
                    NewLeadId={NewLeadId} ProductId={productId} parentLeadId={parentLeadId} />
            </ErrorBoundary>
        </>
    );
}
const mapDispatchToProps = (dispatch) => {
    return {
        setRefreshLeadToRedux: (value) =>
            dispatch(setRefreshLead({ RefreshLead: value })),
        setRefreshCustomerIdToRedux: (value) => dispatch(setRefreshCustomerId({ RefreshCustomerId: value })),
    };
};
export default connect(() => ({}), mapDispatchToProps)(CreateLead);