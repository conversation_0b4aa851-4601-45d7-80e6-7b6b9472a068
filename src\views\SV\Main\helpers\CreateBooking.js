import dayjs from "dayjs";
import { default as siValidation } from "../../../../assets/json/SumInsuredValidation";
import User from "../../../../services/user.service";
import { SV_CONFIG } from "../../../../../src/appconfig/app.config";
import { IsValidUserGroup } from "../../../../services/Common";

let PolicyStartDate = "", PolicyEndDate = "";
let invalids = [undefined, null, 0, "", "0"];
let invalidNumeric = [undefined, null, ""];
let SubProductCheckIds = [7, 115, 131];
let intloanPrds = [158, 159, 160, 164, 171];
let alphanumericRegex = /^([a-zA-Z0-9]+)$/;
let panNoRegex = /[a-zA-Z]{5}[0-9]{4}[a-zA-Z]{1}$/;
let gstNoRegex = /^[0-9]{2}[a-zA-Z]{5}[0-9]{4}[a-zA-Z]{1}[1-9a-zA-Z]{1}[zZ][0-9a-zA-Z]{1}$/;

export const IsSmePropertyProduct = function (productId, item) {
    let isSmeProperty = false;
    try {
        isSmeProperty = productId === 131 && item.SubProduct && ([5, 7, 8].indexOf(item.SubProduct.ID) > -1)
    }
    catch {
        isSmeProperty = false;
    }
    return !!isSmeProperty;
};

export const validateEmail = function (email) {
    // eslint-disable-next-line no-useless-escape
    var emailReg = /^([\w-\.]+@([\w-]+\.)+[\w-]{2,4})?$/; if (!emailReg.test(email)) { return false; } else {
        return true;
    }
}

export const validateEmailForSME = function (email) {
    var emailReg = /^([\w-\.]+@([\w-]+\.)+[\w-]{2,4})?$/;
    const excludedTestPattern = /^(test|test\d*)@gmail\.com$/; //exclude <NAME_EMAIL> or <EMAIL>....
    const consecutivePattern = /^(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)@gmail\.com$/;
    const excludePolicyBazaar = /@policybazaar\.com$/;
    const excludeCare = /@care\.com$/;
    const excludeTestDomain = /@test\.com$/;
    const excludedEmails = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>/.in"]

    if (!(emailReg.test(email)) || (excludedTestPattern.test(email) || consecutivePattern.test(email) || excludePolicyBazaar.test(email) || excludeCare.test(email) ||
        excludeTestDomain.test(email) || excludedEmails.includes(email))) {
        return false;
    }
    else {
        return true;
    }
}

export const isValidApplicationNumber = function (ApplicationNumber, Rule) {
    var regex = new RegExp(Rule);
    return regex.test(ApplicationNumber);
};

export const IsSmeHouseHold = (item) => {
    let result = false;
    try {
        //For PI and Marine (where occupancy is Household item)
        if (item.SubProduct.ID === 14 || (item.SubProduct.ID === 13 && item.Occupation.ID == 28)) {
            result = true;
        }
    }
    catch {

    }
    return result;
}

export const IsFosAgentForBrokerage = function () {
    let result = false;
    try {
        if (User.RoleId !== 13 && User.RoleId !== 12) {
            result = true;
        }
        else {
            // Check if FOS 1, FOS 2, FOS 3, FOS 4, Online Hybrid
            let groupList = User.UserGroupList;
            let smeFosGroupsForBrokerage =  SV_CONFIG["SMEFOSGroups"];
            if (Array.isArray(smeFosGroupsForBrokerage) && Array.isArray(groupList)) {
                for (var i = 0, len = groupList.length; i < len; i++) {
                    if (smeFosGroupsForBrokerage.indexOf(groupList[i].GroupId) > -1) {
                        result = true;
                        break;
                    }
                }
            }
        }
    }
    catch {
        result = false;
    }
    return result;
};

export const IsValidBrokerage = function (val, oldPlanId, smePlanDeal) {
    let maxBrokerage = 0;
    let errorMessage = "Error occured for Brokerage";
    try {
        if (oldPlanId > 0) {
            if (!isNaN(Number(val))) {
                for (var i = 0, len = smePlanDeal.length; i < len; i++) {
                    if (oldPlanId == smePlanDeal[i].OldPlanId) {
                        maxBrokerage = smePlanDeal[i].Brokerage;
                        break;
                    }
                }

                if (!val || (parseFloat(val) <= maxBrokerage)) {
                    if (!val.includes('.') || ((val.split('.')[1]).length <= 2)) {
                        if (val && val < 0) {
                            errorMessage = "Invalid Brokerage number"
                        }
                        else {
                            errorMessage = null;
                        }
                    }
                    else {
                        errorMessage = "Brokerage cannot exceed more than two decimal";
                    }
                }
                else {
                    errorMessage = "Brokerage should not be more than " + maxBrokerage + " percent";
                }
            }
            else {
                errorMessage = "Invalid Brokerage number";
            }
        }
        else {
            errorMessage = "Plan is mandatory for Brokerage percentage";
        }
    }
    catch {
        // Do nothing
    }
    return errorMessage;
};

const GetPanFromGst = function (GstNo) {
    let panNo = '';
    if (GstNo && GstNo.length === 15) {
        panNo = GstNo.substring(2, 12);
    }
    return panNo;
}

export const IsValidItem = function (item, validationType, itemType = 0, fileName = "", SupplierBySubProduct = null, OptimzedPlansListforBookedLeads = null, InvoiceFileName = null, isSmeDocsUploadValid = false, isSmeFosAgent = false) {
    // returns Boolean and Message
    let policyCopyDocTypeId = 56;
    let invoiceDocTypeId = 67;
    let gstCopyDocTypeId = 774;
    let panCopyDocTypeId = 3;
    let paymentProofDocTypeId = 853;
    let reqDocsErrorExists = false;
    const regxMobileNo = /^[0-9\b]+$/;
    const regexName = /^[a-zA-Z ]+$/;

    let IsValid = true, Message = "";
    let checkInsuredName = true;
    let checkPayFreq = true;
    let checkBookingType = true;
    if (SubProductCheckIds.indexOf(item.ProductId) != -1) {
        if (invalids.indexOf(item.SubProduct) !== -1) {
            IsValid = false;
            Message += "SubProduct is not selected.\n";
        } else if (invalids.indexOf(item.SubProduct.ID) !== -1) {
            IsValid = false;
            Message += "SubProduct is not selected.\n";
        }
    }

    let isValidSupChecked = false;
    let isValidPlanChecked = false;
    if (itemType == 1 && item && item.ProductId == 131) {
        let isValidSup = false;
        if (SupplierBySubProduct && Array.isArray(SupplierBySubProduct)) {
            SupplierBySubProduct.forEach((sup) => {
                if (item.Supplier && item.ProductId == sup.ProductId && item.Supplier.OldSupplierId == sup.OldSupplierId) {
                    isValidSup = true;
                }
            });
        }
        if (!isValidSup) {
            isValidSupChecked = true;
            IsValid = false;
            Message += "Supplier is not selected.\n";
        }

        let isValidPlan = false;
        if (OptimzedPlansListforBookedLeads && Array.isArray(OptimzedPlansListforBookedLeads)) {
            OptimzedPlansListforBookedLeads.forEach((plan) => {
                if (item.Plan && plan.SubProductId == item.SubProduct.ID && item.Plan.PlanId == plan.PlanId) {
                    isValidPlan = true;
                }
            });
        }
        if (!isValidPlan) {
            isValidPlanChecked = true;
            IsValid = false;
            Message += "Plan is not selected.\n";
        }
    }

    if (item && item.ProductId === 131 && item.SubProduct && item.SubProduct.ID === 19) {
        if (invalids.indexOf(item.ConstitutionOfBusiness) !== -1) {
            IsValid = false;
            Message += "Please select Constitution Of Business.\n";
        }
        if (invalidNumeric.indexOf(item.MedicalExtension) !== -1) {
            IsValid = false;
            Message += "Please select Medical Extension.\n";
        }
        else {
            if (item.MedicalExtension === "Other" && (isNaN(Number(item.OtherMedicalExtension)) || item.OtherMedicalExtension <= 0)) {
                IsValid = false;
                Message += "Please enter valid Other Medical Extension value.\n";
            }
        }

        if (item.WorkerType && item.WorkerType.length > 0) {
            for (var i = 0, len = item.WorkerType.length; i < len; i++) {
                if ((item.WorkerType[i].Name === "Skilled" && (invalids.indexOf(item.NoOfWorkerSkilled) !== -1 || isNaN(Number(item.NoOfWorkerSkilled)) || item.NoOfWorkerSkilled <= 0)) ||
                    (item.WorkerType[i].Name === "Semi-Skilled" && (invalids.indexOf(item.NoOfWorkerSemiSkilled) !== -1 || isNaN(Number(item.NoOfWorkerSemiSkilled)) || item.NoOfWorkerSemiSkilled <= 0)) ||
                    (item.WorkerType[i].Name === "Un-Skilled" && (invalids.indexOf(item.NoOfWorkerUnSkilled) !== -1 || isNaN(Number(item.NoOfWorkerUnSkilled)) || item.NoOfWorkerUnSkilled <= 0)) ||
                    (item.WorkerType[i].Name === "Other" && (invalids.indexOf(item.NoOfWorkerOther) !== -1 || isNaN(Number(item.NoOfWorkerOther)) || item.NoOfWorkerOther <= 0))) {
                    IsValid = false;
                    Message += "Please enter valid No Of Workers.\n";
                }

                if ((item.WorkerType[i].Name === "Skilled" && (invalids.indexOf(item.SalaryOfWorkerSkilled) !== -1 || isNaN(Number(item.SalaryOfWorkerSkilled)) || item.SalaryOfWorkerSkilled <= 0)) ||
                    (item.WorkerType[i].Name === "Semi-Skilled" && (invalids.indexOf(item.SalaryOfWorkerSemiSkilled) !== -1 || isNaN(Number(item.SalaryOfWorkerSemiSkilled)) || item.SalaryOfWorkerSemiSkilled <= 0)) ||
                    (item.WorkerType[i].Name === "Un-Skilled" && (invalids.indexOf(item.SalaryOfWorkerUnSkilled) !== -1 || isNaN(Number(item.SalaryOfWorkerUnSkilled)) || item.SalaryOfWorkerUnSkilled <= 0)) ||
                    (item.WorkerType[i].Name === "Other" && (invalids.indexOf(item.SalaryOfWorkerOther) !== -1 || isNaN(Number(item.SalaryOfWorkerOther)) || item.SalaryOfWorkerOther <= 0))) {
                    IsValid = false;
                    Message += "Please enter valid Salary Of Workers.\n";
                }

                if (!IsValid) {
                    break;
                }
            }
        }
        else {
            IsValid = false;
            Message += "Please select Worker Type.\n";
        }
    }
    else {
        item.MedicalExtension = null;
        item.OtherMedicalExtension = null;
        item.WorkerType = null;
        item.NoOfWorkerSkilled = null;
        item.NoOfWorkerSemiSkilled = null;
        item.NoOfWorkerUnSkilled = null;
        item.SalaryOfWorkerSkilled = null;
        item.SalaryOfWorkerSemiSkilled = null;
        item.SalaryOfWorkerUnSkilled = null;
        item.NoOfWorkerOther = null;
        item.SalaryOfWorkerOther = null;
    }

    if (item && item.ProductId === 131 && item.SubProduct && item.TransitType && item.SubProduct.ID === 13) {
        // Validate Marine Anual open for SME
        // if (invalids.indexOf(item.TransitFrom) !== -1 || invalids.indexOf(item.TransitFrom.CityStateName) !== -1) {
        //     IsValid = false;
        //     Message += "Please select Transit From.\n";
        // }
        // if (invalids.indexOf(item.TransitTo) !== -1 || invalids.indexOf(item.TransitTo.CityStateName) !== -1) {
        //     IsValid = false;
        //     Message += "Please select Transit To.\n";
        // }

        if (item.Inclusion && item.Inclusion.length > 0) {
            // Do nothing
        }
        else {
            IsValid = false;
            Message += "Please select Inclusions.\n";
        }

        if (invalids.indexOf(item.OccupationType) !== -1) {
            IsValid = false;
            Message += "Please select Occupation Type.\n";
        }
        else {
            if (item.OccupationType === "Manufacturer" || item.OccupationType === "Trader") {
                let validateName = (item.OccupationType === "Trader" && invalidNumeric.indexOf(item.ManufacturerTraderName) === -1) ||
                    item.OccupationType === "Manufacturer"

                if (item.ManufacturerTraderName && item.ManufacturerTraderName.trim() === '') {
                    IsValid = false;
                    Message += item.OccupationType === "Manufacturer" ? "Please enter valid Manufacturer Name.\n" : "Please enter valid Trader Name.\n";
                }
                else {
                    if (validateName &&
                        (invalids.indexOf(item.ManufacturerTraderName) !== -1 || !regexName.test(item.ManufacturerTraderName))) {
                        IsValid = false;
                        Message += item.OccupationType === "Manufacturer" ? "Please enter valid Manufacturer Name.\n" : "Please enter valid Trader Name.\n";
                    }
                }

                let validateMobileNumber = (item.OccupationType === "Trader" && invalidNumeric.indexOf(item.ManufacturerTraderContactNo) === -1) ||
                    item.OccupationType === "Manufacturer"

                if (validateMobileNumber &&
                    (invalids.indexOf(item.ManufacturerTraderContactNo) !== -1 ||
                        !regxMobileNo.test(item.ManufacturerTraderContactNo) ||
                        item.ManufacturerTraderContactNo.length < 5 ||
                        item.ManufacturerTraderContactNo.length > 12)) {
                    IsValid = false;
                    Message += item.OccupationType === "Manufacturer" ? "Please enter valid Manufacturer Contact No.\n" : "Please enter valid Trader Contact No.\n";
                }
            }
            else {
                item.ManufacturerTraderName = null;
                item.ManufacturerTraderContactNo = null;
            }
        }

        if (invalids.indexOf(item.ConstitutionOfBusiness) !== -1) {
            IsValid = false;
            Message += "Please select Constitution Of Business.\n";
        }
    }
    else {
        item.TransitFrom = null;
        item.TransitTo = null;
        item.OccupationType = null;
        item.ManufacturerTraderName = null;
        item.ManufacturerTraderContactNo = null;
        item.Inclusion = null;

        if (item && item.ProductId === 131 && item.SubProduct && item.SubProduct.ID !== 19)
            item.ConstitutionOfBusiness = null;
    }

    if (item && item.ProductId == 131 && item.PayTerm && item.PayTerm > 1) {
        let isValidIns = true;
        let totalInstallmentValue = 0;

        if (item.InstallmentsData && item.InstallmentsData.length > 0) {
            if (item.InstallmentsData.length > item.PayTerm) {
                IsValid = false;
                Message += "Upto " + item.PayTerm + " Installments can be added.\n";
            }
            else {
                item.InstallmentsData.forEach(ins => {
                    if (ins.Date && ins.Amount && ins.Amount > 0) {
                        totalInstallmentValue = totalInstallmentValue + parseFloat(ins.Amount);
                    }
                    else {
                        isValidIns = false;
                    }

                    if (isValidIns && ((new Date(ins.Date) < new Date(item.PolicyStartDate)) || (new Date(ins.Date) > new Date(item.PolicyEndDate)))) {
                        IsValid = false;
                        Message += "Installment dates should be between policy start date and end date.\n";
                    }
                });

                if (item && item.InstallmentsData.length < item.PayTerm && item.ProductId === 131) {
                    IsValid = false;
                    Message += "Please fill the data of " + item.PayTerm + " Installment.\n";
                }

                if (isValidIns) {
                    if (totalInstallmentValue != item.TotalPremium) {
                        IsValid = false;
                        Message += "Total amount of all the Installments should be equal to Premium.\n";
                    }

                }
                else {
                    IsValid = false;
                    Message += "Date and Amount for all installments should be filled.\n";
                }
            }
        }
        else {
            IsValid = false;
            Message += "Please enter installment details.\n";
        }
    }
    else {
        item.InstallmentsData = null;
    }

    if (item && item.ProductId == 131 && (item.SubProduct.ID == 1 || item.SubProduct.ID == 103)) {
        if (invalids.indexOf(item.FamilyType) !== -1 || invalids.indexOf(item.FamilyType.Id) !== -1) {
            IsValid = false;
            Message += "Please fill the Family Type.\n";
        }

        if (item.SumInsuredType && item.SumInsuredType.Id > 0) {
            if (item.SumInsuredType.Id == 2) {
                if (!item.Grades || item.Grades.length <= 0) {
                    IsValid = false;
                    Message += "Please Add Grades.\n";
                }
                else {
                    let isInvalid = false;

                    item.Grades.forEach(grade => {
                        if (!grade.SumInsured || !(grade.SumInsured > 0)) {
                            isInvalid = true;
                        }
                    });

                    if (isInvalid) {
                        IsValid = false;
                        Message += "Please enter valid Sum Insured for Grades.\n";
                    }
                }
            }
            else {
                item.Grades = null;
            }
        }
        else {
            IsValid = false;
            Message += "Please fill the Sum Insured Type.\n";
        }
    }

    if (item && item.SalesPartners && Array.isArray(item.SalesPartners) && item.SalesPartners.length > 0) {
        let isInvalid = false;
        item.SalesPartners.forEach(salesPartner => {
            if (salesPartner == null || salesPartner.UserId == "0" || salesPartner.UserId == "") {
                isInvalid = true;
            }
        });

        if (isInvalid) {
            IsValid = false;
            Message += "Please fill the Secondary Sales Agent details.\n";
        }

        if (item.SalesPartners.length > 1) {
            IsValid = false;
            Message += "Only one Secondary agent can be added\n";
        }
    }
    if (validationType > 0) {
        if (item && item.ProductId === 131 && item.SalesSpecialists && item.SalesSpecialists.length === 0 && item.SubProduct && ([19, 15, 12, 21, 14, 20, 196, 22].indexOf(item.SubProduct.ID)) > -1) {
            const userGroupIds = [1617, 1618, 1619, 2684, 2983]
            const isValidGroupId = IsValidUserGroup(userGroupIds, [13])
            if (isValidGroupId) {
                IsValid = false;
                Message += "Please fill the Sales Specialist details.\n";
            }
        }
    }
    if (item && item.SalesSpecialists && Array.isArray(item.SalesSpecialists) && item.SalesSpecialists.length > 0) {
        let isInvalid = false;
        item.SalesSpecialists.forEach(salesSpecialist => {
            if (salesSpecialist == null || salesSpecialist.UserId == "0" || salesSpecialist.UserId == "") {
                isInvalid = true;
            }
        });

        if (isInvalid) {
            IsValid = false;
            Message += "Please fill the Sales Specialist details.\n";
        }

        if (item.SalesSpecialists.length > 1) {
            IsValid = false;
            Message += "Only one Sales specialist can be added\n";
        }
    }

    let isSmePos = (item.ProductId === 131 && item.payment &&
        item.payment.PaymentStatus && item.payment.PaymentStatus === 4001 &&
        item.payment.PaymentSubStatus && item.payment.PaymentSubStatus === 50);

    if (isSmeFosAgent && item.payment && item.payment.PaymentStatus && item.ProductId == 131 && itemType == 1) {
        if (isSmeDocsUploadValid ||
            (item && item.LeadDocuments &&
                (item.LeadDocuments.findIndex(item => item.DocTypeId === paymentProofDocTypeId) > -1))) {
            // POS documents are valid
        }
        else {
            reqDocsErrorExists = true;
            IsValid = false;
            Message += "Please upload required documents.\n";
        }
    }

    //Validate file uploaded/PAN/GST/CIN
    if (isSmePos) {
        // if(fileName && fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase() !== "pdf")
        // {
        //     IsValid = false;
        //     Message += "Invalid file format. Please upload pdf file.\n";
        // }

        if ((itemType == 1) && !isSmeFosAgent) {
            if (isSmeDocsUploadValid ||
                (item && item.LeadDocuments &&
                    (item.LeadDocuments.findIndex(item => item.DocTypeId === policyCopyDocTypeId) > -1))) {
                // POS documents are valid
            }
            else {
                reqDocsErrorExists = true;
                IsValid = false;
                Message += "Please upload required documents.\n";
            }
        }

        if (item.PAN || item.GST) {
            if (item.PAN && (!isValidApplicationNumber(item.PAN, alphanumericRegex) || (item.PAN.length !== 10))) {
                IsValid = false;
                Message += "PAN should be 10 digit alphanumeric.\n";
            }
            if (item.GST && (!isValidApplicationNumber(item.GST, alphanumericRegex) || (item.GST.length !== 15))) {
                IsValid = false;
                Message += "GSTIN should be 15 digit alphanumeric.\n";
            }
            if (IsValid && item.PAN && !isValidApplicationNumber(item.PAN, panNoRegex)) {
                IsValid = false;
                Message += "PAN should be in valid format.\n";
            }
            // if (IsValid && item.GST && !isValidApplicationNumber(item.GST, gstNoRegex)) {
            //     IsValid = false;
            //     Message += "GSTIN should be in valid format.\n";
            // }

            let panFromGst = GetPanFromGst(item.GST);
            if (IsValid && item.GST && item.PAN && item.PAN.toLowerCase() !== panFromGst.toLowerCase()) {
                IsValid = false;
                Message += "PAN is not valid as per GSTIN.\n";
            }

            if (IsValid && item.GST && !item.PAN) {
                item.PAN = panFromGst;
            }
        }
        else {
            IsValid = false;
            Message += "PAN or GST one of the fields is required for POS.\n";
        }

        if (item.CIN && (!isValidApplicationNumber(item.CIN, alphanumericRegex) || (item.CIN.length !== 21))) {
            IsValid = false;
            Message += "CIN should be 21 digit alphanumeric.\n";
        }
    }
    else {
        item.PAN = '';
        item.GST = ''
        item.CIN = '';
    }

    // validate supplier
    if (!isValidSupChecked) {
        if (invalids.indexOf(item.Supplier) !== -1) {
            IsValid = false;
            Message += "Supplier is not selected.\n";
        } else {
            if (item.ProductId == 131)
                item.Supplier.OldSupplierId = item.Supplier.SupplierId;
            if (invalids.indexOf(item.Supplier.OldSupplierId) !== -1) {
                IsValid = false;
                Message += "Invalid Supplier is selected.\n";
            }
        };
    }

    // validate plan
    if (!isValidPlanChecked) {
        if (invalids.indexOf(item.Plan) !== -1) {
            IsValid = false;
            Message += "Plan is not selected.\n";
        }
        else {
            if ((item.Plan).length < 1) {
                IsValid = false;
                Message += "Plan is not selected.\n";
            }
            else {
                // if (item.ProductId == 131)
                //     item.Plan.OldPlanId = item.Plan.PlanId;
                if (invalids.indexOf(item.Plan.OldPlanId) !== -1) {
                    IsValid = false;
                    Message += "Invalid plan is selected.\n";
                };
            }
        };
    }

    // validate Product
    if (invalids.indexOf(item.ProductId) !== -1) {
        IsValid = false;
        Message += "Product not found.\n";
    } else {
        if (invalids.indexOf(item.ProductId) !== -1) {
            IsValid = false;
            Message += "Invalid Product.\n";
        };
    };


    // Policy type is mandatory
    if (item.ProductId != 154) {
        if (invalids.indexOf(item.PolicyType) !== -1
            || (invalids.indexOf(item.PolicyType.PolicyTypeId) !== -1 && item.PolicyType.PolicyTypeId < 0)) {
            IsValid = false;
            Message += "Policy type is mandatory.\n";
        };
    }

    if (item.ProductId == 131) {
        if (invalids.indexOf(item.Brokerage) == -1) {
            if (parseFloat(item.Brokerage) < parseFloat(0.1) || parseFloat(item.Brokerage) >= parseFloat(100) || isNaN(Number(item.Brokerage))) {
                IsValid = false;
                Message += "Brokerage value should be decimal and lie in range 0.1 to 99.99 .\n";
            }
            else
                item.Brokerage = Math.floor(item.Brokerage * 100) / 100;
        }
        else
            item.Brokerage = parseFloat(0);

        if (item.PolicyType.PolicyTypeId == 1 && item.PrevPolicyNo == "" && item.PreviousBookingNo == "") {
            IsValid = false;
            Message += "One of the fields 'Last Year Policy Number' or 'Last Year Booking Id' should be filled\n";
        }
        if (isNaN(Number(item.PreviousBookingNo))) {
            IsValid = false;
            Message += "'Last Year Booking Id' should be numeric\n";
        }
    }
    else
        item.Brokerage = parseFloat(0);
    // For Investment
    if (!(invalids.indexOf(item.ProductId) !== -1) && (item.ProductId == 115)) {
        // Validate Pay term
        if (invalids.indexOf(item.PayTerm) !== -1) {
            IsValid = false;
            Message += "Pay Term is mandatory.\n";
        }
        else if (isNaN(Number(item.PayTerm))
            || Number(item.PayTerm) <= 0) {
            IsValid = false;
            Message += "Pay Term should be numeric and greater than 0.\n";
        };
        // Validate Policy term
        if (invalids.indexOf(item.PolicyTerm) !== -1) {
            IsValid = false;
            Message += "Policy Term is mandatory.\n";
        }
        else if (isNaN(Number(item.PolicyTerm))
            || Number(item.PolicyTerm) <= 0) {
            IsValid = false;
            Message += "Policy Term should be numeric and greater than 0.\n";
        };

    }

    // validate booking details
    if (invalids.indexOf(item) !== -1) {
        IsValid = false;
        Message += "Booking details are not entered.\n";
    } else {

        // SumInsured is mandatory and should be more than premium amount

        var checkSumInsured = true;
        if (invalids.indexOf(siValidation[String(item.ProductId)]) == -1 && item.Plan != undefined) {
            if (siValidation[String(item.ProductId)].indexOf(item.Plan.OldPlanId) != -1) {
                checkSumInsured = false;
            }
        }


        if (intloanPrds.indexOf(item.ProductId) != -1) {
            checkSumInsured = false;
            checkInsuredName = false;
            checkPayFreq = false;
            checkBookingType = false;

        }

        if (checkSumInsured) {
            if (item.IsTP !== "1" && (invalids.indexOf(item.SumInsured) !== -1
                || isNaN(Number(item.SumInsured))
                || parseFloat(item.SumInsured) <= parseFloat(item.TotalPremium || 0))) {
                IsValid = false;
                if ((item.ProductId == 117)) {
                    Message += "IDV is mandatory and should be more than premium amount.\n";
                }
                else {
                    Message += "SumInsured is mandatory and should be more than premium amount.\n";
                }
            }
            else if (item.ProductId === 117 && item.LeadSource === "Renewal" && item.SumInsured > 7000000) {
                IsValid = false;
                Message += "IDV should not be more than 7000000.\n";
            }
            else if (item.ProductId === 117 && item.LeadSource === "Renewal" && item.TotalPremium > 40000) {
                IsValid = false;
                Message += "Premium amount should not be more than 40000.\n";
            }
        }

        if ((invalids.indexOf(item.TotalPremium) !== -1
            || isNaN(Number(item.TotalPremium))
            || Number(item.TotalPremium) <= 0)) {
            IsValid = false;
            Message += "Premium should be numeric and greater than 0.\n";
        };

        if ([139].indexOf(item.ProductId) != -1) {

            if (invalidNumeric.indexOf(item.ODPremium) !== -1
                || isNaN(Number(item.ODPremium))) {
                IsValid = false;
                Message += "ODPremium should be numeric.\n";
            }

            // if (invalids.indexOf(item.TPPremium) !== -1
            //     || isNaN(Number(item.TPPremium)) || (Number(item.TPPremium)) <= 0
            // ) {
            //     IsValid = false;
            //     Message += "TPPremium should be numeric greater than 0.\n";
            // };

            // if (invalidNumeric.indexOf(item.ServiceTax) !== -1
            //     || isNaN(parseInt(item.ServiceTax))
            // ) {
            //     IsValid = false;
            //     Message += "ServiceTax should be numeric.\n";
            // };

            // if (invalidNumeric.indexOf(item.AddonPremium) !== -1
            //     || isNaN(parseInt(item.AddonPremium))
            // ) {
            //     IsValid = false;
            //     Message += "AddonPremium should be numeric.\n";
            // };
        }
        if ([151, 148, 155, 150, 163, 153, 176].indexOf(item.ProductId) != -1) {
            if (invalidNumeric.indexOf(item.PaidPremium) !== -1
                || isNaN(Number(item.PaidPremium))
            ) {
                IsValid = false;
                Message += "Paid Premium should be numeric only.\n";
            };
            if (invalidNumeric.indexOf(item.PBDiscount) !== -1
                || isNaN(Number(item.PBDiscount))
            ) {
                IsValid = false;
                Message += "PB Discount should be numeric only.\n";
            };

            if (invalidNumeric.indexOf(item.InsurerFee) !== -1
                || isNaN(Number(item.InsurerFee))
            ) {
                IsValid = false;
                Message += "Insurer Fee should be numeric only.\n";
            };
        }

        if ([117, 154, 131].indexOf(item.ProductId) === -1 && checkInsuredName) {
            if (invalids.indexOf(item.InsuredName) !== -1) {
                IsValid = false;
                Message += "Insured Name not entered.\n";
            }
            else if (invalids.indexOf(item.InsuredName.trim()) !== -1) {
                IsValid = false;
                Message += "Insured Name not entered.\n";
            }

            if (item.ProductId == 2 && item.PolicyType.PolicyTypeId == 1) {
                IsValid = true;
            }
        }

        if (item.ProductId == 2 && item.PolicyType.PolicyTypeId == 0 && checkInsuredName && item.InsuredName && !isValidApplicationNumber(item.InsuredName, /^[a-zA-Z ]*$/)) {
            IsValid = false;
            Message += "Insured Name should contains Alphabets only.\n";
        }

        // validate Sales Agent
        if (invalids.indexOf(item.SalesAgent) !== -1
            || invalids.indexOf(item.SalesAgent.UserId) !== -1
            || !(item.SalesAgent.UserId > 0)) {
            //IsValid = false, Message += "Please select a sales agent.\n";
            item.SalesAgent = { UserId: null }; //If no agent is selected then by default sales agent will be null
        };

        // validate Booking type
        if ((invalids.indexOf(item.BookingType) !== -1
            || !(parseInt(item.BookingType) > 0)) && item.ProductId != 115 && item.ProductId != 131 && checkBookingType) {
            IsValid = false;
            Message += "Please select a Booking type.\n";
        };

        if ([114, 117].indexOf(item.ProductId) != -1) {
            if (item.ODPremium != "") {
                if (isNaN(Number(item.ODPremium))
                    || Number(item.ODPremium) < 0) {
                    IsValid = false;
                    Message += "ODPremium should be numeric.\n";
                }
                else if (invalids.indexOf(item.TotalPremium) !== -1
                    || isNaN(Number(item.TotalPremium))
                    || parseFloat(item.TotalPremium) < parseFloat(item.ODPremium || 0)) {
                    IsValid = false;
                    Message += "ODPremium should be less than Premium.\n";
                };
            }
            if (item.ProductId == 117 && item.PolicyType.PolicyTypeId == 1) {
                //Validate OD Premium
                if (!item.ODPremium || item.ODPremium == "") {
                    if (item.IsTP !== "1") {
                        IsValid = false;
                        Message += "Please enter ODPremium.\n";
                    }
                }
                //Validate Proposal No 
                if (itemType === 1 && (item.Supplier && item.Supplier.OldSupplierId && item.Supplier.OldSupplierId == 35)) {
                    if (!item.ProposalNo || item.ProposalNo == "") {
                        IsValid = false;
                        Message += "Please Enter valid Proposal Number.\n";
                    }
                }
                //Validate Policy No 
                if (itemType === 1 && (!item.PolicyNo || item.PolicyNo == "")) {
                    IsValid = false;
                    Message += "Please Enter Policy No.\n";
                }
                else {
                    //Allow only " - " and " / " special character for Policy No. and Atleast 1 number or Alphabet should be there.
                    var PolNoRegex = /^.*[a-zA-Z0-9]+[a-zA-Z0-9-/]*$/;
                    if (itemType === 1 && (item.PolicyNo) && (item.PolicyNo != "")) {
                        if ((item.PolicyNo.trim().length < 4) || (item.PolicyNo.trim().length > 90)) {
                            IsValid = false;
                            Message += "Policy number should be of 4 to 100 characters\n";
                        }
                        if (!isValidApplicationNumber(item.PolicyNo, PolNoRegex)) {
                            IsValid = false;
                            Message += "Policy number should contain atleast one Alphanumeric(Special characters not allowed except ' - ' and ' / ').\n";
                        }
                    }
                }
            }
        }
        // else if (item.ODPremium != "") {
        //     item.ODPremium = 0;
        // }
        if (item.ProductId == 131 && item.SubProduct.ID == 8 && !item.ShopTypeId) {
            IsValid = false;
            Message += "Please select Shop Type.\n";
        };
        if (item.ProductId == 131 && item.SubProduct.ID == 13) {
            if (item.TransitType == "" || item.TransitType == null || item.TransitType == undefined) {
                IsValid = false;
                Message += "Please select Transit Type.\n";
            }
        };
        if (item.ProductId == 131 && item.SubProduct.ID == 13) {
            if (item.ShipmentType == "" || item.ShipmentType == null || item.ShipmentType == undefined) {
                IsValid = false;
                Message += "Please select Shipment Type.\n";
            }
            if (item.MarineCoverType == "" || item.MarineCoverType == null || item.MarineCoverType == undefined) {
                IsValid = false;
                Message += "Please select Cover Type.\n";
            }

            // if(item.TransitType === "Single Transit" && InvoiceFileName && ["pdf","docx","doc"].indexOf(InvoiceFileName.substring(InvoiceFileName.lastIndexOf('.') + 1).toLowerCase()) === -1)
            // {
            //     IsValid = false;
            //     Message += "Invoice file should be in pdf/word format.\n";
            // }

            if (!reqDocsErrorExists && itemType == 1 && item.TransitType === "Single Transit") {
                if (isSmeDocsUploadValid ||
                    (item && item.LeadDocuments &&
                        (item.LeadDocuments.findIndex(item => item.DocTypeId === invoiceDocTypeId) > -1))) {
                    // Marine Invoice is valid
                }
                else {
                    IsValid = false;
                    Message += "Please upload required documents.\n";
                }
            }
        };
        if (item.ProductId == 2 && item.Plan.PlanId == 3846) {
            if (item.TermTenure == 0 || item.TermTenure == null || item.TermTenure == undefined) {
                IsValid = false;
                Message += "Please enter TermTenure.\n";
            }
            if (item.TermTenure > 99 || item.TermTenure < 0) {
                IsValid = false;
                Message += "TermTenure range  is 0-99.\n";
            }
        }
        else {
            item.TermTenure = 0;
        }
        if (item.ProductId == 2 && item.Plan.PlanId == 3846) {
            if (item.TermSI == 0 || item.TermSI == null || item.TermSI == undefined) {
                IsValid = false;
                Message += "Please enter TermSI.\n";
            }
            if (item.TermSI > 100000000 || item.TermSI < 0) {
                IsValid = false;
                Message += "TermSI range  is 0-10Cr.\n";
            }
        }
        else {
            item.TermSI = 0;
        }
        //For New  Car-117 , Registration No is mandatory
        if (item.ProductId == 117) {
            if (!item.RegistrationNo) {
                IsValid = false;
                Message += "Please enter Registration Number.\n";
            }
            var RegNoRegex = /^([a-zA-Z0-9]+)$/;
            if ((item.RegistrationNo) && (item.RegistrationNo != "")) {
                if ((item.RegistrationNo.trim().length < 6) || (item.RegistrationNo.trim().length > 11)) {
                    IsValid = false;
                    Message += "Registration number should be of 6 to 11 characters\n";
                }
                if (!isValidApplicationNumber(item.RegistrationNo, RegNoRegex)) {
                    IsValid = false;
                    Message += "Registration number should be alphanumeric.\n";
                    Message += "Please enter the Regn No in correct format. Eg. HRXXABXXXX, DLXXABCXXXX.\n";
                }
            };

        };
    };

    // Rider SI
    var intRegex = /^\d+(?:\.\d\d?)?$/;
    if ((item.InstallmentPaid != undefined) && (item.InstallmentPaid != "")) {
        if (!isValidApplicationNumber(item.InstallmentPaid, intRegex)) {
            IsValid = false;
            Message += "Installment should be numeric.\n";
        }
    };

    if ((item.RiderSI != undefined) && (item.RiderSI != "")) {
        if (!isValidApplicationNumber(item.RiderSI, intRegex)) {
            IsValid = false;
            Message += "Rider SI should be numeric.\n";
        }
    };

    var dateOfInspection = null;
    if (item.ProductId == 117 && item.PolicyType.PolicyTypeId != 1) {
        if (invalids.indexOf(item.Medical_or_InspectionRequired) !== -1) {
            IsValid = false;
            Message += "Inspection Required is mandatory.\n";
        }
        else if (item.Medical_or_InspectionRequired == 1) {

            if (invalids.indexOf(item.ReferenceNo.trim()) !== -1) {
                IsValid = false;
                Message += "Reference Number not entered.\n";
            }
            if (invalids.indexOf(item.InspectionStatus) !== -1
                || (invalids.indexOf(item.InspectionStatus.ID) !== -1 && item.InspectionStatus.ID < 0)
            ) {
                IsValid = false;
                Message += " Inspection Status not selected.\n";
            };

            try {

                dateOfInspection = new Date(item.DateOfInspection);
                if (dateOfInspection == "Invalid Date") {
                    IsValid = false;
                    Message += " Inspection Date is not valid.\n";
                }
            }
            catch (error) {
                IsValid = false;
                Message += " Inspection Date is not valid.\n";
            }
        }
    }
    else if (item.ProductId == 131) {
        if (invalids.indexOf(item.CityState) !== -1) {
            IsValid = false;
            Message += "Enter City Name.\n";
        }
        else if (invalids.indexOf(item.CityState.CityStateName) !== -1) {
            IsValid = false;
            Message += "Enter City Name.\n";
        }

        if (item.OtherOccupanyMandatory != undefined) {
            if (item.OtherOccupanyMandatory == true) {
                if (!item.Occupation) {
                    IsValid = false;
                    Message += "Parent risk category is mandatory.\n";
                }
                else if (item.Occupation.ID <= 0) {
                    IsValid = false;
                    Message += "Parent risk category is mandatory.\n";
                }
            }
            else {
                item.Occupation = null;
            }
        }



        // Child risk category mandatory validation for product ID 131 and specific sub-product IDs
        // Only mandatory if ChildOccupancyList has values (indicated by IsValidChildOccupancy)
        if (item.ProductId === 131 && item.SubProduct && item.SubProduct.ID &&
            [12, 15, 20, 21, 22, 23].indexOf(item.SubProduct.ID) !== -1 &&
            item.IsValidChildOccupancy === true) {
            if (!item.ChildOccupation || !item.ChildOccupation.ID || item.ChildOccupation.ID <= 0) {
                IsValid = false;
                Message += "Child risk category is mandatory for this sub-product.\n";
            }
        }

        if (item.IsValidChildOccupancy) {
            if (item.ChildOccupation && item.ChildOccupation.ID > 0) {
                // Do nothing
            }
            else {
                // IsValid = false;
                // Message += "Please select Child risk category.\n";
            }
        }
        else {
            if (item.SubProduct && item.SubProduct.ID && item.SubProduct.ID == 19) {
                item.ChildOccupancies = null;
            }
            else {
                item.ChildOccupation = null;
            }

        }

        if (item.SubProduct != undefined) {
            //var brokerage = SMEJson.Brokerage[item.SubProduct.ID];
            //if (invalids.indexOf(brokerage) == -1) {
            //    if (parseFloat(brokerage) < item.Brokerage) {
            //        IsValid = false;
            //        Message += " Maximum allowed brokerage is " + brokerage;
            //    }
            //}

            if ([1, 2, 3, 4, 11, 19, 103, 104].indexOf(item.SubProduct.ID) !== -1) {
                if (item.NoOfLives === "" || item.NoOfLives === undefined) {
                    IsValid = false;
                    Message += "No Of Lives should be numeric.\n";
                }
                if (item.NoOfEmployees === "" || item.NoOfEmployees === undefined) {
                    IsValid = false;
                    Message += "No Of Employees should be numeric.\n";
                }
                if (IsValid) {
                    if (Number(item.NoOfLives) < Number(item.NoOfEmployees)) {
                        IsValid = false;
                        Message += " No. Of Lives cannot be less than No. Of Employees.\n";
                    }
                }
            }
        }
        if (item.SubProduct.ID != 8) {
            item.ShopTypeId = null;
        }
        if (item.SubProduct.ID != 13) {
            item.TransitType = null;
        }
        if (item.SubProduct.ID != 13) {
            item.ShipmentType = null;
            item.MarineCoverType = 0;
        }

        if (item.CompanyName && item.CompanyName.trim() !== "") {
            // Do nothing
        }
        else {
            IsValid = false;
            Message += "Enter Company Name \n";
        }

        if (item.InsuredName && item.InsuredName.trim() !== "") {
            // Do nothing
        }
        else {
            IsValid = false;
            Message += "Enter Insured Name \n";
        }

        if (invalids.indexOf(item.Name) !== -1) {
            IsValid = false;
            Message += "Enter Contact Person's Name \n";
        }

        if (invalids.indexOf(item.PayTerm) !== -1) {
            IsValid = false;
            Message += "Installments are mandatory.\n";
        }
        else if (isNaN(Number(item.PayTerm))
            || Number(item.PayTerm) <= 0) {
            IsValid = false;
            Message += "Installments should be numeric and greater than 0.\n";
        }
        else if (item.PayTerm > 15) {
            IsValid = false;
            Message += "Installments can not be more than 15.\n";
        }

        if (invalids.indexOf(item.PolicyTerm) !== -1) {
            IsValid = false;
            Message += "PolicyTerm is mandatory.\n";
        }
        else if (isNaN(Number(item.PolicyTerm))
            || Number(item.PolicyTerm) <= 0) {
            IsValid = false;
            Message += "PolicyTerm should be numeric and greater than 0.\n";
        };

        if (item.NoOfLives != "" && item.NoOfLives != undefined) {
            if (isNaN(Number(item.NoOfLives))
                || Number(item.NoOfLives) < 0) {
                IsValid = false;
                Message += "No Of Lives should be numeric.\n";
            };
        }

        if (item.NoOfEmployees != "" && item.NoOfEmployees != undefined) {
            if (isNaN(Number(item.NoOfEmployees))
                || Number(item.NoOfEmployees) < 0) {
                IsValid = false;
                Message += "No Of Employees should be numeric.\n";
            };
        }

        if (item.LoadingAmount != "" && item.LoadingAmount != undefined) {
            if (isNaN(Number(item.LoadingAmount))
                || Number(item.LoadingAmount) < 0) {
                IsValid = false;
                Message += "Loading Amount should be numeric.\n";
            };
        }

        var PaymentDate = item.PaymentDate;
        if (PaymentDate != "" && item.PaymentDate != undefined) {
            if (PaymentDate.toString().split("-").length < 3) {
                IsValid = false;
                Message += "Invalid Payment Date.\n";
            }
            else {
                try {
                    PaymentDate = Date(item.PaymentDate.replace(/(\d{2})-(\d{2})-(\d{4})/, "$2/$1/$3"));
                    if (PaymentDate == "Invalid Date") {
                        if (PaymentDate != "") {
                            IsValid = false;
                            Message += "Invalid Payment Date.\n";
                        }
                    }
                }
                catch (error) {

                    IsValid = false;
                    Message += "Invalid Payment Date.\n";

                }
            }
        }

        if (item.CoInsurance && item.CoInsurance == 1) {
            if (!item.LeadersPercentage || (item.LeadersPercentage && item.LeadersPercentage <= 0)) {
                IsValid = false;
                Message += "Leader Supplier's Percentage is invalid.\n";
            }
            else {
                if (!item.FollowerSuppliers || (item.FollowerSuppliers && item.FollowerSuppliers.length == 0)) {
                    IsValid = false;
                    Message += "Please Add Follower Supplier, when Co-Insurance is Yes.\n";
                }
                if (item.FollowerSuppliers && item.FollowerSuppliers.length > 0) {
                    let isInvalidFollowerSup = false;
                    let isInvalidFollowerPer = false;
                    let totalPercentage = parseInt(item.LeadersPercentage);
                    item.FollowerSuppliers.forEach((sup) => {
                        if (sup.FollowerPercentage && sup.FollowerPercentage > 0) {
                            totalPercentage += parseInt(sup.FollowerPercentage);
                        }
                        else {
                            isInvalidFollowerPer = true;
                        }

                        if (!sup.FollowerSupplier || !sup.FollowerSupplier.OldSupplierId || (sup.FollowerSupplier.OldSupplierId <= 0)) {
                            isInvalidFollowerSup = true;
                        }
                    });

                    if (isInvalidFollowerSup) {
                        IsValid = false;
                        Message += "All Follower Suppliers should be selected.\n";
                    }
                    else if (isInvalidFollowerPer) {
                        IsValid = false;
                        Message += "All Follower Supplier's Percentage should be entered.\n";
                    }
                    else if (totalPercentage != 100) {
                        IsValid = false;
                        Message += "Total of Leader Supplier's Percentage and Follower's Percentages should be 100 percent.\n";
                    }
                }
            }
        }
        else {
            item.CoInsurance = null;
        }

        if (IsSmePropertyProduct(131, item)) {
            if (invalids.indexOf(item.PropertyType) !== -1) {
                IsValid = false;
                Message += "Select Property Type.\n";
            }

            if (item.InsuredScope && item.InsuredScope.length > 0) {
                for (var inseredIndex = 0, insuredLen = item.InsuredScope.length; inseredIndex < insuredLen; inseredIndex++) {

                    if (item.InsuredScope[inseredIndex].Name === "Building" && (invalids.indexOf(item.BuildingValue) !== -1 || isNaN(Number(item.BuildingValue)) || item.BuildingValue <= 0)) {
                        IsValid = false;
                        Message += "Please enter valid Building Value.\n";
                    }
                    if (item.InsuredScope[inseredIndex].Name === "Content" && (invalids.indexOf(item.ContentValue) !== -1 || isNaN(Number(item.ContentValue)) || item.ContentValue <= 0)) {
                        IsValid = false;
                        Message += "Please enter valid Content Value.\n";
                    }
                    if (item.InsuredScope[inseredIndex].Name === "Stock" && (invalids.indexOf(item.StockValue) !== -1 || isNaN(Number(item.StockValue)) || item.StockValue <= 0)) {
                        IsValid = false;
                        Message += "Please enter valid Stock Value.\n";
                    }
                }
            }
            else {
                IsValid = false;
                Message += "Please select Insured Scope.\n";
            }
        }
        else {
            item.PropertyType = null;
            item.InsuredScope = null;
        }

        if (IsSmePropertyProduct(131, item) || (item.SubProduct && item.SubProduct.ID === 19)) {
            if (item.RiskLocations && item.RiskLocations.length > 0) {
                for (var j = 0, _len = item.RiskLocations.length; j < _len; j++) {
                    let location = item.RiskLocations[j];

                    if (invalids.indexOf(location.RiskAddress) !== -1 || location.RiskAddress.trim() === '') {
                        IsValid = false;
                        Message += "Please enter a valid Risk Location Address.\n";
                    }
                    // if (invalids.indexOf(location.City) !== -1) {
                    //     IsValid = false;
                    //     Message += "Please enter a valid Risk Location City.\n";
                    // }
                    if ((invalids.indexOf(location.PinCode) !== -1) || isNaN(Number(location.PinCode)) || (location.PinCode <= 0) || (location.PinCode.length < 6)) {
                        IsValid = false;
                        Message += "Please enter a valid Six digit Risk Location Pincode.\n";
                    }

                    if (!IsValid) {
                        break;
                    }
                }
            }
            else {
                IsValid = false;
                Message += "Please Add Risk Locations.\n";
            }
        }
        else {
            item.RiskLocations = null;
        }
    }
    else if (item.ProductId == 101) {

        if (invalids.indexOf(item.CoverType) !== -1) {
            IsValid = false;
            Message += "Select Cover Type.\n";
        }
        else if (item.CoverType.ID == 0) {
            IsValid = false;
            Message += "Select Cover Type.\n";
        }
        else {
            if (item.CoverType.ID == 1 && (invalids.indexOf(item.BuildingSI) !== -1 || isNaN(Number(item.BuildingSI)) || Number(item.BuildingSI) < 0)) {
                IsValid = false;
                Message += "Structure SI should be numeric.\n";
            }

            if (item.CoverType.ID == 2 && (invalids.indexOf(item.ContentSI) !== -1 || isNaN(Number(item.ContentSI)) || Number(item.ContentSI) < 0)) {
                IsValid = false;
                Message += "Content SI should be numeric.\n";
            }

            if (item.CoverType.ID == 3 &&
                (invalids.indexOf(item.ContentSI) !== -1 || isNaN(Number(item.ContentSI)) || Number(item.ContentSI) < 0 ||
                    invalids.indexOf(item.BuildingSI) !== -1 || isNaN(Number(item.BuildingSI)) || Number(item.BuildingSI) < 0)) {
                IsValid = false;
                Message += "Content SI and Structure SI is required.\n";
            }
        }


        if (invalids.indexOf(item.PropertyType) !== -1) {
            IsValid = false;
            Message += "Select Property Type.\n";
        }
        else if (item.PropertyType.ID == 0) {
            IsValid = false;
            Message += "Select Property Type.\n";
        }
        if (invalids.indexOf(item.PropertyPurpose) !== -1) {
            IsValid = false;
            Message += "Select Purpose.\n";
        }
        else if (item.PropertyPurpose.ID == 0) {
            IsValid = false;
            Message += "Select Purpose Type.\n";
        }

        if (invalids.indexOf(item.PolicyStartDate) !== -1) {
            IsValid = false;
            Message += "Policy StartDate Missing.\n";
        }

        if (invalids.indexOf(item.PolicyEndDate) !== -1) {
            IsValid = false;
            Message += "Policy EndDate Missing.\n";
        }

        if (invalids.indexOf(item.CityState) !== -1 || invalids.indexOf(item.CityState.CityStateName) !== -1) {
            IsValid = false;
            Message += "Enter City Name.\n";
        }
    }

    if ([2, 106, 118, 130, 131, 139, 117, 114, 101].indexOf(item.ProductId) != -1) {
        PolicyStartDate = dayjs(item.PolicyStartDate).format('DD-MM-YYYY');
        if (PolicyStartDate != "" && PolicyStartDate != undefined) {
            if (PolicyStartDate.split("-").length < 3) {
                IsValid = false;
                Message += "Invalid PolicyStartDate.\n";
            }
            else {
                try {
                    PolicyStartDate = new Date(PolicyStartDate.replace(/(\d{2})-(\d{2})-(\d{4})/, "$2/$1/$3"));
                    if (PolicyStartDate == "Invalid Date") {
                        PolicyStartDate = "";
                        IsValid = false;
                        Message += "Invalid Policy StartDate.\n";

                    }
                }
                catch (error) {
                    IsValid = false;
                    Message += "Invalid Policy StartDate.\n";

                }
            }
        }

        PolicyEndDate = dayjs(item.PolicyEndDate).format('DD-MM-YYYY');
        if (PolicyEndDate != "" && PolicyEndDate != undefined) {
            if (PolicyEndDate.split("-").length < 3) {
                IsValid = false;
                Message += "Invalid Policy EndDate.\n";
            }
            else {
                try {
                    PolicyEndDate = new Date(PolicyEndDate.replace(/(\d{2})-(\d{2})-(\d{4})/, "$2/$1/$3"));
                    if (PolicyEndDate == "Invalid Date") {
                        PolicyEndDate = "";
                        IsValid = false;
                        Message += "Invalid Policy EndDate.\n";
                    }
                }
                catch (error) {
                    PolicyEndDate = "";
                    IsValid = false;
                    Message += "Invalid Policy EndDate.\n";

                }
            }
        }



        if ([2, 106, 118, 130, 139, 117, 114].indexOf(item.ProductId) != -1) {
            if (PolicyEndDate != "" && PolicyEndDate != undefined && (PolicyStartDate == "" || PolicyStartDate == undefined)) {
                IsValid = false;
                Message += " Policy StartDate Missing.\n";
            }

            if (PolicyStartDate != "" && PolicyStartDate != undefined && (PolicyEndDate == "" || PolicyEndDate == undefined)) {
                IsValid = false;
                Message += " Policy EndDate Missing.\n";
            }

            if (PolicyEndDate != "" && PolicyEndDate != undefined
                && (PolicyStartDate != "" && PolicyStartDate != undefined) && PolicyStartDate >= PolicyEndDate) {
                IsValid = false;
                Message += "Policy EndDate cann't be less than Policy StartDate.\n";
            }
        }
        if ([131, 101].indexOf(item.ProductId) != -1) {
            if (itemType == 1) {
                if (item.ProductId == 131) {
                    if (item.EmailID && item.EmailID.length != (item.EmailID.trim()).length) {
                        IsValid = false; Message += "In Email Id, Whitespaces are not allowed\n";
                    }
                    else if (invalids.indexOf(item.EmailID) !== -1 || !validateEmailForSME(item.EmailID)) {
                        IsValid = false; Message += "Enter Valid EmailID.\n";
                    }

                }
                else {
                    if (invalids.indexOf(item.EmailID) !== -1 || !validateEmail(item.EmailID)) {
                        IsValid = false; Message += "Enter Valid EmailID.\n";
                    }
                }
            }

            if (PolicyEndDate != undefined && PolicyEndDate == 0) {
                PolicyEndDate = "";
            }
            if (PolicyStartDate != undefined && PolicyStartDate == 0) {
                PolicyStartDate = "";
            }
            if (PolicyStartDate == "" || PolicyStartDate == undefined) {
                IsValid = false;
                Message += " Policy StartDate Missing.\n";
            }

            if (PolicyEndDate == "" || PolicyEndDate == undefined) {
                IsValid = false;
                Message += " Policy EndDate Missing.\n";
            }

            if (PolicyEndDate != "" && PolicyEndDate != undefined
                && (PolicyStartDate != "" && PolicyStartDate != undefined) && PolicyStartDate >= PolicyEndDate) {
                IsValid = false;
                Message += "Policy EndDate cann't be less than Policy StartDate.\n";
            }
        }
    }

    var IssuanceDate = null;
    // validate payment details
    if (validationType > 0) {
        // validate payment details
        if (invalids.indexOf(item.payment) !== -1) {
            IsValid = false;
            Message += "Payment details are not entered.\n";
        } else {

            // Payment Mode is mandatory
            if (invalids.indexOf(item.payment.PaymentStatus) !== -1) {
                IsValid = false;
                Message += "Please select payment mode.\n";
            }

            if (([2].indexOf(item.ProductId) != -1) && ([4001, 1001, 1].indexOf(item.payment.PaymentStatus) != -1) && invalids.indexOf(item.payment.PaymentSubStatus) != -1) {
                IsValid = false;
                Message += "Please select sub payment mode.\n";
            }
            else if ((item.ProductId != 2 && item.ProductId != 131) || ([4001, 1001, 1].indexOf(item.payment.PaymentStatus) == -1))
                item.payment.PaymentSubStatus = 0;

            if (item.ProductId == 115) {

                if (invalids.indexOf(item.payment.PaymentPeriodicity) !== -1
                    || !(parseInt(item.payment.PaymentPeriodicity) > 0)) {
                    IsValid = false;
                    Message += "Please select a Payment Frequency.\n";
                }

                if (invalids.indexOf(item.payment.PaymentStatus) === -1) {

                    if ([5001].indexOf(parseInt(item.payment.PaymentStatus)) != -1) {
                        if (invalids.indexOf(item.payment.ChequeNo) !== -1) {
                            IsValid = false;
                            Message += "Please enter Check No. having minimum  6 characters\n";
                        }
                        else if (item.payment.ChequeNo.trim().length < 6) {
                            IsValid = false;
                            Message += "Check No. must have minimum  6 characters\n";
                        }
                    };
                }
            }
            else if (item.ProductId == 117) {

                if (invalids.indexOf(item.payment.PaymentPeriodicity) !== -1
                    || !(parseInt(item.payment.PaymentPeriodicity) > 0)) {
                    if (item.PolicyType.PolicyTypeId != 1) {
                        IsValid = false;
                        Message += "Please select a Payment Frequency.\n";
                    }
                }

                if (invalids.indexOf(item.payment.PaymentStatus) === -1) {

                    if ([3001].indexOf(parseInt(item.payment.PaymentStatus)) != -1) {
                        if (invalids.indexOf(item.payment.TransRefNo) !== -1) {
                            IsValid = false;
                            Message += "Please enter Ref No. having minimum  6 characters\n";
                        }
                        else if (item.payment.TransRefNo.trim().length < 6) {
                            IsValid = false;
                            Message += "Ref No. must have minimum  6 characters\n";
                        }
                    };

                    if ([5001].indexOf(parseInt(item.payment.PaymentStatus)) != -1) {
                        if (invalids.indexOf(item.payment.ChequeNo) !== -1) {
                            IsValid = false;
                            Message += "Please enter Check No. having minimum  6 characters\n";
                        }
                        else if (item.payment.ChequeNo.trim().length < 6) {
                            IsValid = false;
                            Message += "Check No. must have minimum  6 characters\n";
                        }
                    };

                    if ([3001, 5001].indexOf(parseInt(item.payment.PaymentStatus)) != -1) {
                        if (invalids.indexOf(item.payment.BankNameBranch) !== -1) {
                            IsValid = false;
                            Message += "Please enter Bank name.\n";
                        };
                    };
                }
            }
            else if (item.ProductId == 2) {
                if ([5001].indexOf(parseInt(item.payment.PaymentStatus)) != -1) {
                    if (invalids.indexOf(item.payment.ChequeNo) !== -1) {
                        IsValid = false;
                        Message += "Please enter Check No.\n";
                    }
                    else {
                        item.payment.TransRefNo = item.payment.ChequeNo;
                    }
                }
                else {
                    if (invalids.indexOf(item.payment.TransRefNo) !== -1) {
                        IsValid = false;
                        Message += "Please enter Payment Transaction no . \n";
                    }
                    else {
                        item.payment.ChequeNo = item.payment.TransRefNo;
                    }
                };
            }
            else if (item.ProductId == 7) {

                if (invalids.indexOf(item.payment.PaymentPeriodicity) !== -1
                    || !(parseInt(item.payment.PaymentPeriodicity) > 0)) {
                    IsValid = false;
                    Message += "Please select a Payment Frequency.\n";
                }

                if (invalids.indexOf(item.payment.PaymentStatus) === -1) {
                    if ([1, 6001, 4001].indexOf(parseInt(item.payment.PaymentStatus)) === -1) {
                        if (invalids.indexOf(item.payment.BankNameBranch) !== -1) {
                            IsValid = false;
                            Message += "Please enter Bank name.\n";
                        };
                    };


                    // Validate Payment Source for ICICI Priu
                    if (item.Supplier != undefined) {
                        if (item.Supplier.OldSupplierId == "5") {
                            if (invalids.indexOf(item.PaymentSource) !== -1) {
                                IsValid = false;
                                Message += "Payment Source is mandatory.\n";
                            };
                        }
                    }


                    if ([4001].indexOf(parseInt(item.payment.PaymentStatus)) != -1) {
                        if (item.ApplicationNo != item.payment.TransRefNo) {
                            IsValid = false;
                            Message += "Enter Same application No.  and Trans No. \n";
                        }
                    };

                    if ([5001].indexOf(parseInt(item.payment.PaymentStatus)) != -1) {
                        if (invalids.indexOf(item.payment.ChequeNo) !== -1) {
                            IsValid = false;
                            Message += "Please enter Check No. having minimum  6 characters\n";
                        }
                        else if (item.payment.ChequeNo.trim().length < 6) {
                            IsValid = false;
                            Message += "Check No. must have minimum  6 characters\n";
                        }
                    };
                    if ([3001].indexOf(parseInt(item.payment.PaymentStatus)) != -1) {
                        if (invalids.indexOf(item.payment.TransRefNo) !== -1) {
                            IsValid = false;
                            Message += "Please enter Ref No. having minimum  6 characters\n";
                        }
                        else if (item.payment.TransRefNo.trim().length < 6) {
                            IsValid = false;
                            Message += "Ref No. must have minimum  6 characters\n";
                        }
                    };
                }
            }
            else if (item.ProductId == 131) {
                if ([1001, 1].indexOf(parseInt(item.payment.PaymentStatus)) !== -1) {
                    IsValid = false;
                    Message += "Please select payment mode.\n";
                };

                if ([5001].indexOf(parseInt(item.payment.PaymentStatus)) != -1) {
                    if (invalids.indexOf(item.payment.ChequeNo) !== -1) {
                        IsValid = false;
                        Message += "Please enter Check No. having minimum  6 characters\n";
                    }
                    else if (item.payment.ChequeNo.trim().length < 6) {
                        IsValid = false;
                        Message += "Check No. must have minimum  6 characters\n";
                    }
                };
                //if ([3001].indexOf(parseInt(item.payment.PaymentStatus)) != -1) {
                //    if (invalids.indexOf(item.payment.TransRefNo) !== -1) {
                //        IsValid = false, Message += "Please enter Ref No. having minimum  6 characters\n";
                //    }
                //    else if (item.payment.TransRefNo.trim().length < 6) {
                //        IsValid = false, Message += "Ref No. must have minimum  6 characters\n";
                //    }
                //};

                if ([4001].indexOf(parseInt(item.payment.PaymentStatus)) != -1) {
                    if (invalids.indexOf(item.payment.PaymentSubStatus) !== -1) {
                        IsValid = false;
                        Message += "Please Select Payment Sub Status\n";
                    }
                }

                if (item.BookingFrom && item.BookingFrom.Name) {
                    //do nothing
                }
                else {
                    const userGroupIds = [2684, 1617, 2983, 1897, 1619, 1618]
                    const isValidGroupId = IsValidUserGroup(userGroupIds, [13]) //fos group true + agent;
                    if (!isValidGroupId) // not mandatory for fos group
                    {
                        IsValid = false;
                        Message += "Booking From is required\n"
                    }
                }
                if (item.QuoteId) {
                    //do nothing
                }
                else {
                    if (item.SubProduct && item.SubProduct.ID && item.SubProduct.ID == 1 && item.BookingFrom && item.BookingFrom.Name && item.BookingFrom.Name == "CJ") {
                        IsValid = false;
                        Message += "Quote Id is required\n"
                    }
                }
                //if (item.Supplier != undefined) {
                //    if (invalids.indexOf(item.PaymentSource) !== -1) {
                //        IsValid = false, Message += "Payment Source is mandatory.\n";
                //    };
                //};
            }
            else /*if ([106, 118, 130, 3, 114].indexOf(item.ProductId) != -1)*/ {

                if (checkPayFreq) {
                    if (invalids.indexOf(item.payment.PaymentPeriodicity) !== -1
                        || !(parseInt(item.payment.PaymentPeriodicity) > 0)) {
                        IsValid = false;
                        Message += "Please select a Payment Frequency.\n";
                    };
                }


                if ([5001].indexOf(parseInt(item.payment.PaymentStatus)) != -1) {
                    if (invalids.indexOf(item.payment.ChequeNo) !== -1) {
                        IsValid = false;
                        Message += "Please enter Check No. having minimum  6 characters\n";
                    }
                    else if (item.payment.ChequeNo.trim().length < 6) {
                        IsValid = false;
                        Message += "Check No. must have minimum  6 characters\n";
                    }
                };
                if ([3001].indexOf(parseInt(item.payment.PaymentStatus)) != -1) {
                    if (invalids.indexOf(item.payment.TransRefNo) !== -1) {
                        IsValid = false;
                        Message += "Please enter Ref No. having minimum  6 characters\n";
                    }
                    else if (item.payment.TransRefNo.trim().length < 6) {
                        IsValid = false;
                        Message += "Ref No. must have minimum  6 characters\n";
                    }
                };
            }

            // validate ApplicationNo
            if (invalids.indexOf(item.ApplicationNo) !== -1 && [115, 7, 106, 118, 130].indexOf(item.ProductId) != -1) {
                IsValid = false;
                Message += "Please Enter Application No.\n";
            };

        };


        if (intloanPrds.indexOf(item.ProductId) != -1) {
            try {

                IssuanceDate = new Date(item.IssuanceDate.replace(/(\d{2})-(\d{2})-(\d{4})/, "$2/$1/$3"));
                if (IssuanceDate == "Invalid Date") {
                    IsValid = false;
                    Message += " IssuanceDate  is not valid.\n";
                }
            }
            catch (error) {
                IsValid = false;
                Message += " IssuanceDate  is not valid.\n";
            }
        }
    };

    if (item.ProductId == 2) {
        // Validate Pay term
        if (item.IsSTP == null || ([0, 1].indexOf(item.IsSTP) === -1)) {
            IsValid = false;
            Message += "STP/NSTP is mandatory.\n";
        }
    }

    //Check for valid loading amount
    if (item.ProductId === 131) {
        if (item.SubProduct && item.SubProduct.ID && item.SubProduct.ID == 19) {
            if (item.LD && item.LD.Id && item.LD.Id > 0 && item.LDAmount) {
                //do nothing
            }
            else {
                IsValid = false;
                Message += "Loading/Discounting amount is mandatory.\n";
            }
        }
        if (item.LD && item.LDAmount) {
            var amount = parseInt(item.LDAmount)
            if ((amount >= item.TotalPremium) || (isNaN(amount))) {
                IsValid = false;
                Message += item.LD.Name + " amount should be numeric and less than premium amount.\n";
            }
        }
        if (item.SubProduct && item.SubProduct.ID == 19) {
            if (item.PolicyCategory && item.PolicyCategory.Name) {
                //do nothing
            }
            else {
                IsValid = false;
                Message += "Policy category is required\n"
            }
        }
        if (item.SubProduct && item.SubProduct.ID && [5, 7, 8, 112, 113].indexOf(item.SubProduct.ID) !== -1) {
            //TERRORISM PREMIUM - Should not be invalid and should be less than Total Premium
            if (item.TerrorismPremiumVal && item.TerrorismPremiumVal.Id === 1) {
                if ((invalids.indexOf(item.TerrorismPremium) !== -1 || isNaN(parseFloat(item.TerrorismPremium)))
                    || parseFloat(item.TotalPremium) <= parseFloat(item.TerrorismPremium || 0)) {
                    IsValid = false;
                    Message += "Terrorism Premium should be valid numeric and less than Premium amount.\n";
                }
            }
            //BURGLARY PREMIUM - Should not be invalid and should be less than Total Premium
            if (item.BurglaryPremiumVal && item.BurglaryPremiumVal.Id === 1) {
                if ((invalids.indexOf(item.BurglaryPremium) !== -1 || isNaN(parseFloat(item.BurglaryPremium)))
                    || parseFloat(item.TotalPremium) <= parseFloat(item.BurglaryPremium || 0)) {
                    IsValid = false;
                    Message += "Burglary Premium should be valid numeric and less than Premium amount.\n";
                }
            }
            //BOTH - The sum of both should be less than Total Premium (Can not be equal)
            if (item.TerrorismPremiumVal && item.TerrorismPremiumVal.Id === 1
                && item.BurglaryPremiumVal && item.BurglaryPremiumVal.Id === 1) {

                let burglaryPremium = parseFloat(item.BurglaryPremium) || 0;
                let terrorismPremium = parseFloat(item.TerrorismPremium) || 0;
                let totalPremium = parseFloat(item.TotalPremium) || 0;

                if (totalPremium <= (burglaryPremium + terrorismPremium)) {
                    IsValid = false;
                    Message += "Total Premium should be greater than the sum of Burglary Premium and Terrorism Premium.\n";
                }
            }
            //FIRE PREMIUM - Fire premium should not be invalid, positive, and less than Total Premium
            if (item.TerrorismPremiumVal?.Id == 1 || item.BurglaryPremiumVal?.Id == 1) {
                if (invalids.indexOf(item.FirePremium) == -1) {
                    //do nothing
                }
                else {
                    IsValid = false;
                    Message += "Fire Premium is invalid. Enter valid Terrorism Premium and Burglary Premium.\n";
                }
            }
            if (item.TerrorismPremium < 0 || item.BurglaryPremium < 0) {
                IsValid = false;
                Message += "Negative values are not accepted.\n";
            }
        }
    }
    try {

        if (IsValid) {
            var Item = {
                SumInsured: item.SumInsured,
                PolicyTypeId: item.PolicyType.PolicyTypeId,
                PolicyTypeName: item.PolicyType.PolicyTypeName,
                BookingTypeId: parseInt(item.BookingType),
                ApplicationNo: (item.ApplicationNo == undefined ? "" : item.ApplicationNo),
                PolicyNo: (item.PolicyNo == undefined ? "" : item.PolicyNo),
                SalesAgent: 0,
                MedicalRequired: item.MedicalRequired || 0,
                InvestmentTypeID: (item.SubProduct == undefined ? 0 : item.SubProduct.ID),
                PolicyTerm: parseInt(item.PolicyTerm),
                PayTerm: parseInt(item.PayTerm),
                InsuredName: item.InsuredName,
                RiderSI: parseInt(item.RiderSI),
                Rider: item.Rider,
                PaymentSource: item.PaymentSource,
                InstallmentPaid: item.InstallmentPaid,
                Supplier: item.Supplier,
                Plan: item.Plan,
                TotalPremium: item.TotalPremium,
                MatrixLeadId: item.leadId,
                PreviousBookingNo: [1, 2].indexOf(item.PolicyType.PolicyTypeId) != -1 ? item.PreviousBookingNo : "",
                Brokerage: item.Brokerage,
                IsRSA: (item.IsRSA == "1" || item.IsRSA == 1) ? 1 : 0,
                PrevPolicyNo: item.PrevPolicyNo,
                PaidPremium: item.PaidPremium,
                TransitType: item.TransitType,
                TermTenure: item.TermTenure,
                TermSI: item.TermSI,
                BranchName: [32, 47, 68].indexOf(item.Supplier.SupplierId) != -1 ? item.BranchName : "",
                OtherOccupany: item.OtherOccupany,
                RegistrationNo: item.RegistrationNo,
                PBDiscount: item.PBDiscount || 0,
                InsurerFee: item.InsurerFee || 0,
                TPPremium: item.TPPremium || 0,
                AddonPremium: item.AddonPremium || 0,
                ServiceTax: item.ServiceTax || 0,
                ExpiringInsurer: item.ExpiringInsurer,
                PolicyDocName: item.PolicyDocName,
                DocumentId: item.DocumentId,
                SalesPartners: item.SalesPartners,
                SalesSpecialists: item.SalesSpecialists,
                PrimaryAgentSharePercentage: item.PrimaryAgentSharePercentage ? item.PrimaryAgentSharePercentage : "",
                SumInsuredType: item.SumInsuredType,
                FamilyType: item.FamilyType,
                Grades: item.Grades,
                ShipmentType: item.ShipmentType ? item.ShipmentType : "",
                IsTP: (item.IsTP == "1" || item.IsTP == 1) ? 1 : 0,
                MarineCoverType: item.MarineCoverType || 0,
                InstallmentsData: item.InstallmentsData,
                TerrorismPremium: (item.TerrorismPremiumVal && item.TerrorismPremium && item.TerrorismPremiumVal.Id == 1 && item.TerrorismPremium > 0) ? item.TerrorismPremium : null,
                BurglaryPremium: (item.BurglaryPremiumVal && item.BurglaryPremium && item.BurglaryPremiumVal.Id == 1 && item.BurglaryPremium > 0) ? item.BurglaryPremium : null,
                FirePremium: (item.FirePremium && item.FirePremium > 0) ? item.FirePremium : null
            };

            if (invalidNumeric.indexOf(item.ODPremium) == -1) {
                Item.ODPremium = item.ODPremium
            }

            if (invalidNumeric.indexOf(item.Portability) === -1) {
                if (item.Portability === true) {
                    Item.Portability = 1
                }
                else if (item.Portability === false) {
                    Item.Portability = 0
                }
            }

            if (validationType > 0 && item.payment) {
                Item.PaymentStatus = parseInt(item.payment.PaymentStatus);
                Item.ChequeNo = item.payment.ChequeNo;
                Item.PaymentPeriodicityId = parseInt(item.payment.PaymentPeriodicity);
                Item.TransRefNo = item.payment.TransRefNo;
                Item.BankNameBranch = item.payment.BankNameBranch;
                //Item.IsEMI = item.payment.IsEMI;
                Item.IsEMI = (item.payment.IsEMI === "1" || item.payment.IsEMI === 1) ? 1 : 0;
                Item.PaymentSubStatus = item.payment.PaymentSubStatus;
                Item.ProposalNo = item.ProposalNo;
            }

            if (item.ProductId == 117 && item.LeadSource !== 'Renewal') {
                Item.Medical_or_InspectionRequired = item.Medical_or_InspectionRequired;
                if (item.Medical_or_InspectionRequired == 1) {
                    Item.ReferenceNo = item.ReferenceNo;
                    Item.DateOfInspection = dateOfInspection ? dateOfInspection.getTime() : 0;
                    Item.InspectionStatus = item.InspectionStatus.ID;
                }
                else {
                    Item.ReferenceNo = "";
                    Item.DateOfInspection = 0;
                    Item.InspectionStatus = "";
                }
            }

            if (item.ProductId == 131) {
                Item.CityState = item.CityState;
                Item.CompanyName = item.CompanyName;
                Item.CityID = item.CityState.CityID;
                Item.StateID = item.CityState.StateID;
                Item.ShopTypeId = item.ShopTypeId;
                Item.TransitFromCityId = (item.TransitFrom && item.TransitFrom.CityID) ? item.TransitFrom.CityID : null;
                Item.TransitToCityId = (item.TransitTo && item.TransitTo.CityID) ? item.TransitTo.CityID : null;
                Item.OccupationType = item.OccupationType ? item.OccupationType : null;
                Item.ManufacturerTraderName = item.ManufacturerTraderName ? item.ManufacturerTraderName.trim() : null;
                Item.ManufacturerTraderContactNo = item.ManufacturerTraderContactNo ? item.ManufacturerTraderContactNo.trim() : null;
                Item.ConstitutionOfBusiness = item.ConstitutionOfBusiness ? item.ConstitutionOfBusiness : null;
                if (item.Inclusion && item.Inclusion.length > 0) {
                    Item.Inclusion = [];
                    item.Inclusion.forEach(element => {
                        Item.Inclusion.push(parseInt(element.Id));
                    });
                }
                Item.MedicalExtension = item.MedicalExtension || null;
                if (item.MedicalExtension === "Other") {
                    Item.MedicalExtension = item.OtherMedicalExtension || null;
                }
                if (item.WorkerType && item.WorkerType.length > 0) {
                    Item.WorkerTypes = [];
                    item.WorkerType.forEach(element => {
                        if (element.Name === "Skilled")
                            Item.WorkerTypes.push({ WorkerType: element.Name, NoOfWorker: item.NoOfWorkerSkilled, Salary: item.SalaryOfWorkerSkilled });
                        else if (element.Name === "Semi-Skilled")
                            Item.WorkerTypes.push({ WorkerType: element.Name, NoOfWorker: item.NoOfWorkerSemiSkilled, Salary: item.SalaryOfWorkerSemiSkilled });
                        else if (element.Name === "Un-Skilled")
                            Item.WorkerTypes.push({ WorkerType: element.Name, NoOfWorker: item.NoOfWorkerUnSkilled, Salary: item.SalaryOfWorkerUnSkilled });
                        else if (element.Name === "Other")
                            Item.WorkerTypes.push({ WorkerType: element.Name, NoOfWorker: item.NoOfWorkerOther, Salary: item.SalaryOfWorkerOther });
                    });
                }
                if (item.RiskLocations && item.RiskLocations.length > 0) {
                    Item.RiskLocations = [];
                    item.RiskLocations.forEach(element => {
                        let cityId;
                        if (element.City && element.City.CityID)
                            cityId = element.City.CityID
                        Item.RiskLocations.push({ RiskAddress: element.RiskAddress, CityId: cityId, PinCode: element.PinCode });
                    });
                }
                if (item.InsuredScope && item.InsuredScope.length > 0) {
                    item.InsuredScope.forEach(element => {
                        if (element.Name === "Building")
                            Item.BuildingSI = item.BuildingValue || 0;
                        else if (element.Name === "Content")
                            Item.ContentSI = item.ContentValue || 0;
                        else if (element.Name === "Stock")
                            Item.StockSI = item.StockValue || 0;
                    });
                }
                if (item.LoadingAmount != "") {
                    Item.LoadingAmount = item.LoadingAmount;
                }
                //Item.OccupancyId = item.Occupation.ID;
                if (invalids.indexOf(item.Occupation) == -1) {
                    if (invalidNumeric.indexOf(item.Occupation.ID) == -1) {
                        Item.OccupancyId = item.Occupation.ID;
                    }
                }
                if (item.NoOfLives != "") {
                    Item.NoOfLives = item.NoOfLives;
                }
                if (item.NoOfLives != "") {
                    Item.NoOfEmployees = item.NoOfEmployees;
                }
                Item.DocumentsRequired = item.DocumentsRequired;
                Item.IsDocReceived = item.IsDocReceived;
                Item.PaymentDate = 0;

                if (invalids.indexOf(item.PaymentDate) == -1) {
                    Item.PaymentDate = PaymentDate.getTime()
                }
                Item.EmailID = item.EmailID;
                Item.PAN = item.PAN || '';
                Item.GST = item.GST || '';
                Item.CIN = item.CIN || '';
                Item.Name = item.Name || '';

                if (item.CoInsurance && item.CoInsurance == 1) {
                    Item.CoInsurance = parseInt(item.CoInsurance) || 0;
                    Item.LeadersPercentage = parseInt(item.LeadersPercentage) || 0;

                    if (Array.isArray(item.FollowerSuppliers) && item.FollowerSuppliers.length > 0) {
                        let followerSuppliers = [];
                        item.FollowerSuppliers.forEach((sup) => {
                            followerSuppliers.push({
                                FollowerPercentage: sup.FollowerPercentage,
                                SupplierId: sup.FollowerSupplier.OldSupplierId
                            });
                        });
                        Item.FollowerSuppliers = followerSuppliers || null;
                    }
                }
                else {
                    Item.CoInsurance = 0;
                    Item.LeadersPercentage = 0;
                    Item.FollowerSuppliers = null;
                }

                Item.PropertyTypeId = item.PropertyType ? item.PropertyType.ID : 0;
                Item.ChildOccupancyId = (item.ChildOccupation && item.ChildOccupation.ID > 0) ? item.ChildOccupation.ID : null;
                if (item.ChildOccupancies && item.ChildOccupancies.length > 0) {
                    var childOcc = item.ChildOccupancies.map((e) => e.ID)
                    if (childOcc) {
                        Item.ChildOccupancies = childOcc.toString();
                    }
                }
                else {
                    Item.ChildOccupancies = null;
                }


                if (item.LD && item.LD.Name && item.LDAmount) {
                    if (item.LD.Name === "Loading") {
                        Item.Loading = parseInt(item.LDAmount)
                    }
                    else {
                        Item.Discounting = parseInt(item.LDAmount)
                    }
                }
                if (item.SubProduct && item.SubProduct.ID === 14 && item.Association) {
                    Item.AssociationId = item.Association.Id;
                }

                if (invalids.indexOf(item.BookingFrom) == -1 && item.BookingFrom.Name && item.BookingFrom.Name != '') {
                    Item.BookingFrom = item.BookingFrom.Name
                }
                if (invalids.indexOf(item.QuoteId) == -1 && item.SubProduct && item.SubProduct.ID && item.SubProduct.ID == 1
                    && item.BookingFrom && item.BookingFrom.Name && item.BookingFrom.Name == 'CJ') {
                    Item.QuoteId = item.QuoteId;
                }
                if (item.SubProduct && item.SubProduct.ID == 19 && invalids.indexOf(item.PolicyCategory) == -1 &&
                    invalids.indexOf(item.PolicyCategory.Name) == -1) {
                    Item.PolicyCategory = item.PolicyCategory.Name
                }
            }

            if (item.ProductId == 101) {
                Item.CoverageTypeId = item.CoverType.ID;
                Item.PropertyTypeId = item.PropertyType.ID;
                Item.PurposeId = item.PropertyPurpose.ID;
                //Item.PolicyTerm = PolicyTerm;
                Item.EmailID = item.EmailID;
                Item.BuildingSI = item.BuildingSI || 0;
                Item.ContentSI = item.ContentSI || 0;
                Item.CityState = item.CityState;
                Item.CityID = item.CityState.CityID;
                Item.StateID = item.CityState.StateID;
            }

            if (invalids.indexOf(item.IssuanceDate) == -1) {
                Item.IssuanceDate = IssuanceDate.getTime()
            }

            if (invalids.indexOf(PolicyStartDate) == -1)
                Item.PolicyStartDate = PolicyStartDate.getTime();
            if (invalids.indexOf(PolicyEndDate) == -1) {
                Item.PolicyEndDate = PolicyEndDate.getTime()
            }

            if (item.ProductId == 3) {
                Item.DestinationCountry = item.DestinationCountry;
            }
            if (item.ProductId == 2) {
                Item.IsSTP = item.IsSTP;
            }
        }
    }
    catch (error) {
        console.log(error);
    }

    return { IsValid: IsValid, Item: Item || {}, Message: Message };
};